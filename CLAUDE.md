# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

ADC Multi-Languages is a comprehensive translation management platform built as a monorepo with:
- **Frontend**: Next.js application with TypeScript, Tailwind CSS, and Redux Toolkit
- **Backend**: Go-based API server using Gin, GORM, and PostgreSQL
- **Legacy Backend**: Rust backend (deprecated, being replaced by Go)

This is a multi-tenant translation platform supporting organizations, projects, locales, API keys, and subscription management.

## Architecture

### Frontend (`frontend/`)
- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS v4 with Radix UI components
- **State Management**: Redux Toolkit with RTK Query
- **Authentication**: NextAuth.js with JWT tokens
- **Payments**: Stripe integration
- **AI Integration**: Google Gemini for translation services

### Backend (`backend-go/`)
- **Framework**: Go with Gin web framework
- **Database**: PostgreSQL with GORM ORM
- **Authentication**: JWT tokens with API key support
- **Architecture**: Handler → Service → Repository pattern
- **Features**: Multi-tenant, RBAC, rate limiting, caching, audit logging

### API Proxy Architecture
The frontend includes a universal API proxy that:
- Routes all `/api/*` requests to the Go backend on port 8300
- Handles authentication session management
- Provides unified error handling and file upload support

## Development Commands

### Main Commands
```bash
# Setup dependencies for both frontend and backend
make setup

# Run both frontend (port 3300) and backend (port 8300) concurrently
make dev

# Run with auto-reload (requires air for Go)
make dev-watch

# Run individual services
make frontend     # Next.js frontend only
make backend      # Go backend only
```

### Frontend Commands
```bash
cd frontend
bun dev           # Start development server on port 3300
bun build         # Build for production
bun lint          # Run ESLint
```

### Backend Commands
```bash
cd backend-go
go run main.go                    # Start server on port 8300
go build -o adc-backend .         # Build binary
./run_tests.sh                    # Run comprehensive API integration tests
go run cmd/api_tester.go test     # Run simple API tests
```

### Testing
```bash
# Backend testing
make test         # Run comprehensive integration tests
make test-simple  # Run basic API tests

# Frontend testing
cd frontend && bun lint
```

### Utility Commands
```bash
make status           # Check if services are running
make proxy-test       # Test API proxy functionality
make proxy-test-simple # Quick proxy test with curl
make kill            # Stop all running services
make clean           # Clean build artifacts
```

## Key File Locations

### Frontend Structure
- `frontend/src/app/` - Next.js App Router pages
- `frontend/src/components/` - Reusable React components
- `frontend/src/lib/redux/` - Redux store and API endpoints
- `frontend/src/lib/api/proxy.ts` - Universal API proxy configuration
- `frontend/src/middleware.ts` - NextAuth middleware

### Backend Structure
- `backend-go/handlers/` - HTTP request handlers
- `backend-go/services/` - Business logic layer
- `backend-go/models/` - Database models and schemas
- `backend-go/middleware/` - HTTP middleware (auth, CORS, rate limiting)
- `backend-go/routes/routes.go` - Route definitions
- `backend-go/tests/` - Integration tests

### Configuration
- `backend-go/.env.example` - Backend environment template
- `frontend/next.config.js` - Next.js configuration with API proxy
- `Makefile` - Build and development commands

## Environment Setup

1. **Backend Setup**: Copy `backend-go/.env.example` to `backend-go/.env` and configure:
   - `DATABASE_URL` - PostgreSQL connection string
   - `JWT_SECRET` - JWT signing secret
   - `SMTP_*` - Email service configuration
   - `STRIPE_*` - Payment processing keys

2. **Frontend Setup**: Environment variables are handled through NextAuth.js configuration

## Database

- **ORM**: GORM with automatic migrations
- **Migrations**: Run automatically on backend startup
- **Models**: User, Organization, Project, Locale, TranslationKey, Translation, APIKey, etc.

## Authentication & Authorization

- **Frontend**: NextAuth.js with JWT session management
- **Backend**: JWT tokens and API keys
- **RBAC**: Permission groups with fine-grained access control
- **Multi-tenant**: Organization-based data isolation

## API Standards

- **Response Format**: Standardized JSON with success/error status
- **Pagination**: Query parameters `page`, `per_page`, `limit`, `offset`
- **Authentication**: Bearer tokens or `X-API-Key` header
- **Error Handling**: Consistent error codes and messages

## Development Guidelines

- Follow existing code patterns in each service
- Use the Makefile commands for consistency
- Test API changes with `make proxy-test`
- Frontend uses shadcn/ui components with Tailwind CSS
- Backend follows repository pattern with proper error handling
- Always run tests before committing changes

## Ports

- **Frontend**: http://localhost:3300 (with API proxy)
- **Backend**: http://localhost:8300 (direct API access)
- **API Proxy**: All `/api/*` requests from frontend route to backend
# Google Cloud Run Deployment Setup

This guide will help you set up automatic deployment to Google Cloud Run using GitHub Actions.

## Prerequisites

1. Google Cloud Project with billing enabled
2. GitHub repository with the workflow files
3. Docker images will be stored in Google Container Registry (GCR)

## Google Cloud Setup

### 1. Enable Required APIs

```bash
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com
gcloud services enable cloudbuild.googleapis.com
```

### 2. Create Service Account

```bash
# Create service account
gcloud iam service-accounts create github-actions \
    --description="Service account for GitHub Actions" \
    --display-name="GitHub Actions"

# Grant necessary permissions
gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="serviceAccount:github-actions@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/run.admin"

gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="serviceAccount:github-actions@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/storage.admin"

gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="serviceAccount:github-actions@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/iam.serviceAccountUser"
```

### 3. Set up Workload Identity Federation (Recommended)

```bash
# Create workload identity pool
gcloud iam workload-identity-pools create "github-pool" \
    --project="PROJECT_ID" \
    --location="global" \
    --display-name="GitHub Actions Pool"

# Create workload identity provider
gcloud iam workload-identity-pools providers create-oidc "github-provider" \
    --project="PROJECT_ID" \
    --location="global" \
    --workload-identity-pool="github-pool" \
    --display-name="GitHub Actions Provider" \
    --attribute-mapping="google.subject=assertion.sub,attribute.actor=assertion.actor,attribute.repository=assertion.repository" \
    --issuer-uri="https://token.actions.githubusercontent.com"

# Allow the GitHub Actions to impersonate the service account
gcloud iam service-accounts add-iam-policy-binding \
    --project="PROJECT_ID" \
    --role="roles/iam.workloadIdentityUser" \
    --member="principalSet://iam.googleapis.com/projects/PROJECT_NUMBER/locations/global/workloadIdentityPools/github-pool/attribute.repository/YOUR_GITHUB_USERNAME/adc-muti-languages" \
    github-actions@PROJECT_ID.iam.gserviceaccount.com
```

## GitHub Secrets Configuration

Add the following secrets to your GitHub repository (`Settings > Secrets and variables > Actions`):

### Required Secrets

1. **GCP_PROJECT_ID**: Your Google Cloud Project ID
2. **GCP_REGION**: Deployment region (e.g., `us-central1`)
3. **WIF_PROVIDER**: `projects/PROJECT_NUMBER/locations/global/workloadIdentityPools/github-pool/providers/github-provider`
4. **WIF_SERVICE_ACCOUNT**: `github-actions@PROJECT_ID.iam.gserviceaccount.com`

### Application Secrets

#### Backend Environment Variables
- **JWT_SECRET**: Secret key for JWT token signing
- **DATABASE_URL**: PostgreSQL connection string
- **SMTP_HOST**: Email server hostname
- **SMTP_PORT**: Email server port
- **SMTP_USERNAME**: Email username
- **SMTP_PASSWORD**: Email password
- **STRIPE_SECRET_KEY**: Stripe secret key
- **STRIPE_WEBHOOK_SECRET**: Stripe webhook secret

#### Frontend Environment Variables
- **NEXTAUTH_SECRET**: NextAuth.js secret key
- **NEXTAUTH_URL**: Your frontend URL (e.g., `https://your-frontend-service-url`)

## Database Setup

### Option 1: Google Cloud SQL (Recommended)

```bash
# Create Cloud SQL instance
gcloud sql instances create adc-postgres \
    --database-version=POSTGRES_14 \
    --tier=db-f1-micro \
    --region=us-central1

# Create database
gcloud sql databases create adc_db --instance=adc-postgres

# Create user
gcloud sql users create adc_user --instance=adc-postgres --password=SECURE_PASSWORD

# Get connection string
gcloud sql instances describe adc-postgres --format="value(connectionName)"
```

Your `DATABASE_URL` should be:
```
*******************************************************************************************
```

### Option 2: External Database

Use any PostgreSQL database and set the `DATABASE_URL` accordingly.

## Deployment

### Automatic Deployment

The workflow will automatically deploy when you push to `main` or `develop` branches.

### Manual Deployment

You can trigger deployment manually from the GitHub Actions tab using the "workflow_dispatch" event.

## Monitoring and Logs

### View Logs
```bash
# Backend logs
gcloud logs read --service=adc-backend --limit=50

# Frontend logs
gcloud logs read --service=adc-frontend --limit=50
```

### Service URLs
```bash
# Get service URLs
gcloud run services describe adc-backend --region=us-central1 --format="value(status.url)"
gcloud run services describe adc-frontend --region=us-central1 --format="value(status.url)"
```

## Custom Domain (Optional)

1. Map your custom domain:
```bash
gcloud run domain-mappings create --service=adc-frontend --domain=yourdomain.com --region=us-central1
```

2. Update DNS records as instructed by the output.

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure service account has proper IAM roles
2. **Build Failures**: Check Dockerfile syntax and build context
3. **Database Connection**: Verify DATABASE_URL format and network access
4. **Environment Variables**: Ensure all required secrets are set in GitHub

### Debug Commands

```bash
# Check service status
gcloud run services describe SERVICE_NAME --region=REGION

# View recent deployments
gcloud run revisions list --service=SERVICE_NAME --region=REGION

# Check logs
gcloud logs read --service=SERVICE_NAME --limit=100
```

## Security Considerations

1. Use Workload Identity Federation instead of service account keys
2. Regularly rotate secrets and API keys
3. Enable Cloud Armor for DDoS protection
4. Use Cloud CDN for static assets
5. Implement proper monitoring and alerting

## Cost Optimization

1. Set appropriate CPU and memory limits
2. Configure minimum instances to 0 for cost savings
3. Use Cloud Run's pay-per-request pricing model
4. Monitor usage with Cloud Monitoring
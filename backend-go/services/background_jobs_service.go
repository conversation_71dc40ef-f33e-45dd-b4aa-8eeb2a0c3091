package services

import (
	"context"
	"fmt"
	"sync"
	"time"

	"adc-multi-languages/utils"
)

// JobStatus represents the status of a background job
type JobStatus string

const (
	JobStatusPending   JobStatus = "pending"
	JobStatusRunning   JobStatus = "running"
	JobStatusCompleted JobStatus = "completed"
	JobStatusFailed    JobStatus = "failed"
	JobStatusCancelled JobStatus = "cancelled"
)

// Job represents a background job
type Job struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Payload     map[string]interface{} `json:"payload"`
	Status      JobStatus              `json:"status"`
	Progress    int                    `json:"progress"`
	Result      interface{}            `json:"result,omitempty"`
	Error       string                 `json:"error,omitempty"`
	CreatedAt   time.Time              `json:"created_at"`
	StartedAt   *time.Time             `json:"started_at,omitempty"`
	CompletedAt *time.Time             `json:"completed_at,omitempty"`
	RetryCount  int                    `json:"retry_count"`
	MaxRetries  int                    `json:"max_retries"`
	UserID      string                 `json:"user_id,omitempty"`
}

// JobHandler represents a function that processes a job
type JobHandler func(ctx context.Context, job *Job) error

// BackgroundJobService manages background job processing
type BackgroundJobService struct {
	jobs     map[string]*Job
	handlers map[string]JobHandler
	jobQueue chan *Job
	workers  int
	mutex    sync.RWMutex
	ctx      context.Context
	cancel   context.CancelFunc
	wg       sync.WaitGroup
}

// NewBackgroundJobService creates a new background job service
func NewBackgroundJobService(workers int) *BackgroundJobService {
	ctx, cancel := context.WithCancel(context.Background())

	service := &BackgroundJobService{
		jobs:     make(map[string]*Job),
		handlers: make(map[string]JobHandler),
		jobQueue: make(chan *Job, 1000), // Buffer for 1000 jobs
		workers:  workers,
		ctx:      ctx,
		cancel:   cancel,
	}

	// Register default job handlers
	service.registerDefaultHandlers()

	// Start workers
	service.startWorkers()

	utils.Logger.Info(fmt.Sprintf("Background job service started with %d workers", workers))
	return service
}

// registerDefaultHandlers registers built-in job handlers
func (bjs *BackgroundJobService) registerDefaultHandlers() {
	bjs.RegisterHandler("email_send", bjs.handleEmailSend)
	bjs.RegisterHandler("file_cleanup", bjs.handleFileCleanup)
	bjs.RegisterHandler("translation_batch", bjs.handleTranslationBatch)
	bjs.RegisterHandler("export_translations", bjs.handleExportTranslations)
	bjs.RegisterHandler("import_translations", bjs.handleImportTranslations)
	bjs.RegisterHandler("generate_report", bjs.handleGenerateReport)
}

// RegisterHandler registers a job handler for a specific job type
func (bjs *BackgroundJobService) RegisterHandler(jobType string, handler JobHandler) {
	bjs.mutex.Lock()
	defer bjs.mutex.Unlock()

	bjs.handlers[jobType] = handler
	utils.Logger.Info(fmt.Sprintf("Registered job handler for type: %s", jobType))
}

// EnqueueJob adds a new job to the queue
func (bjs *BackgroundJobService) EnqueueJob(jobType string, payload map[string]interface{}, userID string) (*Job, error) {
	job := &Job{
		ID:         generateJobID(),
		Type:       jobType,
		Payload:    payload,
		Status:     JobStatusPending,
		Progress:   0,
		CreatedAt:  time.Now(),
		RetryCount: 0,
		MaxRetries: 3,
		UserID:     userID,
	}

	bjs.mutex.Lock()
	bjs.jobs[job.ID] = job
	bjs.mutex.Unlock()

	// Check if handler exists
	if _, exists := bjs.handlers[jobType]; !exists {
		job.Status = JobStatusFailed
		job.Error = fmt.Sprintf("No handler registered for job type: %s", jobType)
		return job, fmt.Errorf("no handler for job type: %s", jobType)
	}

	// Add to queue
	select {
	case bjs.jobQueue <- job:
		utils.Logger.Info(fmt.Sprintf("Job enqueued: %s (type: %s)", job.ID, job.Type))
		return job, nil
	default:
		job.Status = JobStatusFailed
		job.Error = "Job queue is full"
		return job, fmt.Errorf("job queue is full")
	}
}

// GetJob retrieves a job by ID
func (bjs *BackgroundJobService) GetJob(jobID string) (*Job, error) {
	bjs.mutex.RLock()
	defer bjs.mutex.RUnlock()

	job, exists := bjs.jobs[jobID]
	if !exists {
		return nil, fmt.Errorf("job not found: %s", jobID)
	}

	return job, nil
}

// GetUserJobs retrieves all jobs for a specific user
func (bjs *BackgroundJobService) GetUserJobs(userID string) []*Job {
	bjs.mutex.RLock()
	defer bjs.mutex.RUnlock()

	var userJobs []*Job
	for _, job := range bjs.jobs {
		if job.UserID == userID {
			userJobs = append(userJobs, job)
		}
	}

	return userJobs
}

// CancelJob cancels a pending or running job
func (bjs *BackgroundJobService) CancelJob(jobID string) error {
	bjs.mutex.Lock()
	defer bjs.mutex.Unlock()

	job, exists := bjs.jobs[jobID]
	if !exists {
		return fmt.Errorf("job not found: %s", jobID)
	}

	if job.Status == JobStatusCompleted || job.Status == JobStatusFailed {
		return fmt.Errorf("cannot cancel job in status: %s", job.Status)
	}

	job.Status = JobStatusCancelled
	now := time.Now()
	job.CompletedAt = &now

	utils.Logger.Info(fmt.Sprintf("Job cancelled: %s", jobID))
	return nil
}

// startWorkers starts the background workers
func (bjs *BackgroundJobService) startWorkers() {
	for i := 0; i < bjs.workers; i++ {
		bjs.wg.Add(1)
		go bjs.worker(i)
	}
}

// worker processes jobs from the queue
func (bjs *BackgroundJobService) worker(workerID int) {
	defer bjs.wg.Done()

	for {
		select {
		case <-bjs.ctx.Done():
			utils.Logger.Info(fmt.Sprintf("Worker %d shutting down", workerID))
			return
		case job := <-bjs.jobQueue:
			bjs.processJob(job, workerID)
		}
	}
}

// processJob processes a single job
func (bjs *BackgroundJobService) processJob(job *Job, workerID int) {
	// Check if job was cancelled
	if job.Status == JobStatusCancelled {
		return
	}

	// Update job status
	bjs.mutex.Lock()
	job.Status = JobStatusRunning
	now := time.Now()
	job.StartedAt = &now
	bjs.mutex.Unlock()

	utils.Logger.Info(fmt.Sprintf("Worker %d processing job: %s (type: %s)", workerID, job.ID, job.Type))

	// Get handler
	handler, exists := bjs.handlers[job.Type]
	if !exists {
		bjs.failJob(job, fmt.Sprintf("No handler for job type: %s", job.Type))
		return
	}

	// Process job with timeout
	ctx, cancel := context.WithTimeout(bjs.ctx, 30*time.Minute)
	defer cancel()

	err := handler(ctx, job)

	bjs.mutex.Lock()
	defer bjs.mutex.Unlock()

	now = time.Now()
	job.CompletedAt = &now

	if err != nil {
		job.RetryCount++
		if job.RetryCount <= job.MaxRetries {
			// Retry job
			job.Status = JobStatusPending
			job.StartedAt = nil
			job.CompletedAt = nil
			job.Error = ""

			// Re-enqueue with delay
			go func() {
				time.Sleep(time.Duration(job.RetryCount) * time.Minute)
				select {
				case bjs.jobQueue <- job:
					utils.Logger.Info(fmt.Sprintf("Job retried: %s (attempt %d)", job.ID, job.RetryCount))
				default:
					bjs.failJob(job, "Failed to re-enqueue job for retry")
				}
			}()
		} else {
			bjs.failJob(job, err.Error())
		}
	} else {
		job.Status = JobStatusCompleted
		job.Progress = 100
		utils.Logger.Info(fmt.Sprintf("Job completed: %s", job.ID))
	}
}

// failJob marks a job as failed
func (bjs *BackgroundJobService) failJob(job *Job, errorMsg string) {
	job.Status = JobStatusFailed
	job.Error = errorMsg
	now := time.Now()
	job.CompletedAt = &now
	utils.Logger.Error(fmt.Sprintf("Job failed: %s - %s", job.ID, errorMsg))
}

// updateJobProgress updates the progress of a job
func (bjs *BackgroundJobService) updateJobProgress(jobID string, progress int) {
	bjs.mutex.Lock()
	defer bjs.mutex.Unlock()

	if job, exists := bjs.jobs[jobID]; exists {
		job.Progress = progress
	}
}

// Shutdown gracefully shuts down the background job service
func (bjs *BackgroundJobService) Shutdown() {
	utils.Logger.Info("Shutting down background job service...")

	bjs.cancel()
	close(bjs.jobQueue)
	bjs.wg.Wait()

	utils.Logger.Info("Background job service shut down complete")
}

// generateJobID generates a unique job ID
func generateJobID() string {
	return fmt.Sprintf("job_%d_%d", time.Now().UnixNano(), time.Now().Nanosecond())
}

// Default job handlers

// handleEmailSend handles email sending jobs
func (bjs *BackgroundJobService) handleEmailSend(ctx context.Context, job *Job) error {
	// Extract email parameters from payload
	to, ok := job.Payload["to"].(string)
	if !ok {
		return fmt.Errorf("missing 'to' parameter")
	}

	subject, ok := job.Payload["subject"].(string)
	if !ok {
		return fmt.Errorf("missing 'subject' parameter")
	}

	body, ok := job.Payload["body"].(string)
	if !ok {
		return fmt.Errorf("missing 'body' parameter")
	}

	// Send email using email service
	emailService := NewEmailService()
	err := emailService.SendEmail(to, subject, body, "")
	if err != nil {
		return fmt.Errorf("failed to send email: %w", err)
	}

	job.Result = map[string]interface{}{
		"sent_to": to,
		"sent_at": time.Now(),
	}

	return nil
}

// handleFileCleanup handles file cleanup jobs
func (bjs *BackgroundJobService) handleFileCleanup(ctx context.Context, job *Job) error {
	maxAge, ok := job.Payload["max_age_hours"].(float64)
	if !ok {
		maxAge = 24 // Default to 24 hours
	}

	fileService := NewFileUploadService()
	err := fileService.CleanupOldFiles(time.Duration(maxAge) * time.Hour)
	if err != nil {
		return fmt.Errorf("failed to cleanup files: %w", err)
	}

	job.Result = map[string]interface{}{
		"cleaned_at":    time.Now(),
		"max_age_hours": maxAge,
	}

	return nil
}

// handleTranslationBatch handles batch translation jobs
func (bjs *BackgroundJobService) handleTranslationBatch(ctx context.Context, job *Job) error {
	// This would implement batch translation logic
	// For now, just simulate processing
	bjs.updateJobProgress(job.ID, 25)
	time.Sleep(2 * time.Second)

	bjs.updateJobProgress(job.ID, 50)
	time.Sleep(2 * time.Second)

	bjs.updateJobProgress(job.ID, 75)
	time.Sleep(2 * time.Second)

	job.Result = map[string]interface{}{
		"translations_processed": 100,
		"completed_at":           time.Now(),
	}

	return nil
}

// handleExportTranslations handles translation export jobs
func (bjs *BackgroundJobService) handleExportTranslations(ctx context.Context, job *Job) error {
	// Simulate export processing
	bjs.updateJobProgress(job.ID, 30)
	time.Sleep(3 * time.Second)

	bjs.updateJobProgress(job.ID, 70)
	time.Sleep(3 * time.Second)

	job.Result = map[string]interface{}{
		"export_file": "/exports/translations_" + job.ID + ".json",
		"exported_at": time.Now(),
	}

	return nil
}

// handleImportTranslations handles translation import jobs
func (bjs *BackgroundJobService) handleImportTranslations(ctx context.Context, job *Job) error {
	// Simulate import processing
	bjs.updateJobProgress(job.ID, 20)
	time.Sleep(2 * time.Second)

	bjs.updateJobProgress(job.ID, 60)
	time.Sleep(3 * time.Second)

	bjs.updateJobProgress(job.ID, 90)
	time.Sleep(2 * time.Second)

	job.Result = map[string]interface{}{
		"imported_keys": 250,
		"imported_at":   time.Now(),
	}

	return nil
}

// handleGenerateReport handles report generation jobs
func (bjs *BackgroundJobService) handleGenerateReport(ctx context.Context, job *Job) error {
	// Simulate report generation
	bjs.updateJobProgress(job.ID, 40)
	time.Sleep(4 * time.Second)

	bjs.updateJobProgress(job.ID, 80)
	time.Sleep(3 * time.Second)

	job.Result = map[string]interface{}{
		"report_file":  "/reports/report_" + job.ID + ".pdf",
		"generated_at": time.Now(),
	}

	return nil
}

// Global background job service instance
var globalBackgroundJobService *BackgroundJobService

// GetBackgroundJobService returns the global background job service instance
func GetBackgroundJobService() *BackgroundJobService {
	if globalBackgroundJobService == nil {
		globalBackgroundJobService = NewBackgroundJobService(5) // 5 workers by default
	}
	return globalBackgroundJobService
}

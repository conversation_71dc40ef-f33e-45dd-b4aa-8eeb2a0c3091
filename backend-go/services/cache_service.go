package services

import (
	"encoding/json"
	"sync"
	"time"

	"adc-multi-languages/utils"
)

// CacheItem represents a cached item with expiration
type CacheItem struct {
	Value     interface{}
	ExpiresAt time.Time
}

// IsExpired checks if the cache item has expired
func (item *CacheItem) IsExpired() bool {
	return time.Now().After(item.ExpiresAt)
}

// Cache represents an in-memory cache
type Cache struct {
	items map[string]*CacheItem
	mutex sync.RWMutex
}

// NewCache creates a new cache instance
func NewCache() *Cache {
	cache := &Cache{
		items: make(map[string]*CacheItem),
	}

	// Start cleanup goroutine
	go cache.cleanup()

	return cache
}

// cleanup removes expired items from the cache
func (c *Cache) cleanup() {
	ticker := time.NewTicker(5 * time.Minute) // Cleanup every 5 minutes
	defer ticker.Stop()

	for range ticker.C {
		c.mutex.Lock()
		now := time.Now()
		for key, item := range c.items {
			if now.After(item.ExpiresAt) {
				delete(c.items, key)
			}
		}
		c.mutex.Unlock()
	}
}

// Set stores a value in the cache with expiration
func (c *Cache) Set(key string, value interface{}, duration time.Duration) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.items[key] = &CacheItem{
		Value:     value,
		ExpiresAt: time.Now().Add(duration),
	}
}

// Get retrieves a value from the cache
func (c *Cache) Get(key string) (interface{}, bool) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	item, exists := c.items[key]
	if !exists {
		return nil, false
	}

	if item.IsExpired() {
		// Item has expired, remove it
		c.mutex.RUnlock()
		c.mutex.Lock()
		delete(c.items, key)
		c.mutex.Unlock()
		c.mutex.RLock()
		return nil, false
	}

	return item.Value, true
}

// GetString retrieves a string value from the cache
func (c *Cache) GetString(key string) (string, bool) {
	value, exists := c.Get(key)
	if !exists {
		return "", false
	}

	str, ok := value.(string)
	return str, ok
}

// GetInt retrieves an int value from the cache
func (c *Cache) GetInt(key string) (int, bool) {
	value, exists := c.Get(key)
	if !exists {
		return 0, false
	}

	i, ok := value.(int)
	return i, ok
}

// GetJSON retrieves and unmarshals a JSON value from the cache
func (c *Cache) GetJSON(key string, dest interface{}) bool {
	value, exists := c.Get(key)
	if !exists {
		return false
	}

	jsonStr, ok := value.(string)
	if !ok {
		return false
	}

	err := json.Unmarshal([]byte(jsonStr), dest)
	return err == nil
}

// SetJSON marshals and stores a JSON value in the cache
func (c *Cache) SetJSON(key string, value interface{}, duration time.Duration) error {
	jsonBytes, err := json.Marshal(value)
	if err != nil {
		return err
	}

	c.Set(key, string(jsonBytes), duration)
	return nil
}

// Delete removes a value from the cache
func (c *Cache) Delete(key string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	delete(c.items, key)
}

// Exists checks if a key exists in the cache
func (c *Cache) Exists(key string) bool {
	_, exists := c.Get(key)
	return exists
}

// Clear removes all items from the cache
func (c *Cache) Clear() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.items = make(map[string]*CacheItem)
}

// Size returns the number of items in the cache
func (c *Cache) Size() int {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	return len(c.items)
}

// Keys returns all keys in the cache
func (c *Cache) Keys() []string {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	keys := make([]string, 0, len(c.items))
	for key := range c.items {
		keys = append(keys, key)
	}

	return keys
}

// GetOrSet retrieves a value from cache or sets it if not found
func (c *Cache) GetOrSet(key string, duration time.Duration, setter func() (interface{}, error)) (interface{}, error) {
	// Try to get from cache first
	if value, exists := c.Get(key); exists {
		return value, nil
	}

	// Not in cache, call setter function
	value, err := setter()
	if err != nil {
		return nil, err
	}

	// Store in cache
	c.Set(key, value, duration)
	return value, nil
}

// CacheService provides caching functionality for the application
type CacheService struct {
	cache *Cache
}

// NewCacheService creates a new cache service
func NewCacheService() *CacheService {
	return &CacheService{
		cache: NewCache(),
	}
}

// Cache key constants
const (
	UserCachePrefix         = "user:"
	OrganizationCachePrefix = "org:"
	ProjectCachePrefix      = "project:"
	LocaleCachePrefix       = "locale:"
	TranslationCachePrefix  = "translation:"
	APIKeyCachePrefix       = "apikey:"
)

// Cache durations
const (
	ShortCacheDuration  = 5 * time.Minute
	MediumCacheDuration = 30 * time.Minute
	LongCacheDuration   = 2 * time.Hour
)

// CacheUser caches user data
func (cs *CacheService) CacheUser(userID string, user interface{}) error {
	key := UserCachePrefix + userID
	return cs.cache.SetJSON(key, user, MediumCacheDuration)
}

// GetCachedUser retrieves cached user data
func (cs *CacheService) GetCachedUser(userID string, dest interface{}) bool {
	key := UserCachePrefix + userID
	return cs.cache.GetJSON(key, dest)
}

// CacheOrganization caches organization data
func (cs *CacheService) CacheOrganization(orgID string, org interface{}) error {
	key := OrganizationCachePrefix + orgID
	return cs.cache.SetJSON(key, org, MediumCacheDuration)
}

// GetCachedOrganization retrieves cached organization data
func (cs *CacheService) GetCachedOrganization(orgID string, dest interface{}) bool {
	key := OrganizationCachePrefix + orgID
	return cs.cache.GetJSON(key, dest)
}

// CacheProject caches project data
func (cs *CacheService) CacheProject(projectID string, project interface{}) error {
	key := ProjectCachePrefix + projectID
	return cs.cache.SetJSON(key, project, MediumCacheDuration)
}

// GetCachedProject retrieves cached project data
func (cs *CacheService) GetCachedProject(projectID string, dest interface{}) bool {
	key := ProjectCachePrefix + projectID
	return cs.cache.GetJSON(key, dest)
}

// CacheLocales caches locale list
func (cs *CacheService) CacheLocales(locales interface{}) error {
	key := LocaleCachePrefix + "all"
	return cs.cache.SetJSON(key, locales, LongCacheDuration)
}

// GetCachedLocales retrieves cached locale list
func (cs *CacheService) GetCachedLocales(dest interface{}) bool {
	key := LocaleCachePrefix + "all"
	return cs.cache.GetJSON(key, dest)
}

// InvalidateUser removes user from cache
func (cs *CacheService) InvalidateUser(userID string) {
	key := UserCachePrefix + userID
	cs.cache.Delete(key)
}

// InvalidateOrganization removes organization from cache
func (cs *CacheService) InvalidateOrganization(orgID string) {
	key := OrganizationCachePrefix + orgID
	cs.cache.Delete(key)
}

// InvalidateProject removes project from cache
func (cs *CacheService) InvalidateProject(projectID string) {
	key := ProjectCachePrefix + projectID
	cs.cache.Delete(key)
}

// InvalidateLocales removes locale cache
func (cs *CacheService) InvalidateLocales() {
	key := LocaleCachePrefix + "all"
	cs.cache.Delete(key)
}

// GetStats returns cache statistics
func (cs *CacheService) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"size": cs.cache.Size(),
		"keys": cs.cache.Keys(),
	}
}

// Global cache service instance
var globalCacheService *CacheService

// GetCacheService returns the global cache service instance
func GetCacheService() *CacheService {
	if globalCacheService == nil {
		globalCacheService = NewCacheService()
		// Only log if logger is initialized
		if utils.Logger != nil {
			utils.Logger.Info("Cache service initialized")
		}
	}
	return globalCacheService
}

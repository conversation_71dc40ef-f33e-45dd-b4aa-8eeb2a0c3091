package services

import (
	"crypto/md5"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"

	"adc-multi-languages/utils"
)

// FileUploadService handles file upload functionality
type FileUploadService struct {
	uploadDir     string
	maxFileSize   int64
	allowedTypes  map[string]bool
	baseURL       string
}

// NewFileUploadService creates a new file upload service
func NewFileUploadService() *FileUploadService {
	uploadDir := getEnvOrDefault("UPLOAD_DIR", "./uploads")
	maxFileSize := int64(10 * 1024 * 1024) // 10MB default
	baseURL := getEnvOrDefault("BASE_URL", "http://localhost:8080")

	// Create upload directory if it doesn't exist
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		utils.Logger.Error("Failed to create upload directory: " + err.Error())
	}

	// Define allowed file types
	allowedTypes := map[string]bool{
		".json":  true,
		".csv":   true,
		".xlsx":  true,
		".xls":   true,
		".txt":   true,
		".po":    true,
		".pot":   true,
		".xliff": true,
		".tmx":   true,
		".xml":   true,
		".yaml":  true,
		".yml":   true,
		".properties": true,
		".strings": true,
		".resx":  true,
		".arb":   true,
	}

	return &FileUploadService{
		uploadDir:    uploadDir,
		maxFileSize:  maxFileSize,
		allowedTypes: allowedTypes,
		baseURL:      baseURL,
	}
}

// UploadedFile represents an uploaded file
type UploadedFile struct {
	ID           string    `json:"id"`
	OriginalName string    `json:"original_name"`
	FileName     string    `json:"file_name"`
	FilePath     string    `json:"file_path"`
	FileSize     int64     `json:"file_size"`
	ContentType  string    `json:"content_type"`
	Extension    string    `json:"extension"`
	MD5Hash      string    `json:"md5_hash"`
	URL          string    `json:"url"`
	UploadedAt   time.Time `json:"uploaded_at"`
}

// FileUploadError represents a file upload error
type FileUploadError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

func (e *FileUploadError) Error() string {
	return fmt.Sprintf("File upload error [%s]: %s", e.Code, e.Message)
}

// UploadFile uploads a single file
func (fs *FileUploadService) UploadFile(fileHeader *multipart.FileHeader, subDir string) (*UploadedFile, error) {
	// Validate file size
	if fileHeader.Size > fs.maxFileSize {
		return nil, &FileUploadError{
			Code:    "FILE_TOO_LARGE",
			Message: fmt.Sprintf("File size exceeds maximum allowed size of %d bytes", fs.maxFileSize),
		}
	}

	// Validate file type
	ext := strings.ToLower(filepath.Ext(fileHeader.Filename))
	if !fs.allowedTypes[ext] {
		return nil, &FileUploadError{
			Code:    "INVALID_FILE_TYPE",
			Message: fmt.Sprintf("File type %s is not allowed", ext),
		}
	}

	// Open uploaded file
	file, err := fileHeader.Open()
	if err != nil {
		return nil, &FileUploadError{
			Code:    "FILE_OPEN_ERROR",
			Message: "Failed to open uploaded file",
			Details: err.Error(),
		}
	}
	defer file.Close()

	// Generate unique filename
	fileID := uuid.New().String()
	fileName := fmt.Sprintf("%s%s", fileID, ext)

	// Create subdirectory if specified
	targetDir := fs.uploadDir
	if subDir != "" {
		targetDir = filepath.Join(fs.uploadDir, subDir)
		if err := os.MkdirAll(targetDir, 0755); err != nil {
			return nil, &FileUploadError{
				Code:    "DIRECTORY_CREATE_ERROR",
				Message: "Failed to create target directory",
				Details: err.Error(),
			}
		}
	}

	// Full file path
	filePath := filepath.Join(targetDir, fileName)

	// Create destination file
	dst, err := os.Create(filePath)
	if err != nil {
		return nil, &FileUploadError{
			Code:    "FILE_CREATE_ERROR",
			Message: "Failed to create destination file",
			Details: err.Error(),
		}
	}
	defer dst.Close()

	// Copy file content and calculate MD5 hash
	hash := md5.New()
	multiWriter := io.MultiWriter(dst, hash)

	fileSize, err := io.Copy(multiWriter, file)
	if err != nil {
		// Clean up the file if copy failed
		os.Remove(filePath)
		return nil, &FileUploadError{
			Code:    "FILE_COPY_ERROR",
			Message: "Failed to copy file content",
			Details: err.Error(),
		}
	}

	// Generate MD5 hash
	md5Hash := fmt.Sprintf("%x", hash.Sum(nil))

	// Generate URL
	relativePath := fileName
	if subDir != "" {
		relativePath = filepath.Join(subDir, fileName)
	}
	fileURL := fmt.Sprintf("%s/uploads/%s", fs.baseURL, strings.ReplaceAll(relativePath, "\\", "/"))

	uploadedFile := &UploadedFile{
		ID:           fileID,
		OriginalName: fileHeader.Filename,
		FileName:     fileName,
		FilePath:     filePath,
		FileSize:     fileSize,
		ContentType:  fileHeader.Header.Get("Content-Type"),
		Extension:    ext,
		MD5Hash:      md5Hash,
		URL:          fileURL,
		UploadedAt:   time.Now().UTC(),
	}

	utils.Logger.Info("File uploaded successfully: " + fileHeader.Filename)
	return uploadedFile, nil
}

// UploadMultipleFiles uploads multiple files
func (fs *FileUploadService) UploadMultipleFiles(fileHeaders []*multipart.FileHeader, subDir string) ([]*UploadedFile, []error) {
	var uploadedFiles []*UploadedFile
	var errors []error

	for _, fileHeader := range fileHeaders {
		uploadedFile, err := fs.UploadFile(fileHeader, subDir)
		if err != nil {
			errors = append(errors, err)
		} else {
			uploadedFiles = append(uploadedFiles, uploadedFile)
		}
	}

	return uploadedFiles, errors
}

// DeleteFile deletes an uploaded file
func (fs *FileUploadService) DeleteFile(filePath string) error {
	// Ensure the file is within the upload directory
	absUploadDir, err := filepath.Abs(fs.uploadDir)
	if err != nil {
		return &FileUploadError{
			Code:    "PATH_RESOLUTION_ERROR",
			Message: "Failed to resolve upload directory path",
			Details: err.Error(),
		}
	}

	absFilePath, err := filepath.Abs(filePath)
	if err != nil {
		return &FileUploadError{
			Code:    "PATH_RESOLUTION_ERROR",
			Message: "Failed to resolve file path",
			Details: err.Error(),
		}
	}

	if !strings.HasPrefix(absFilePath, absUploadDir) {
		return &FileUploadError{
			Code:    "INVALID_FILE_PATH",
			Message: "File path is outside upload directory",
		}
	}

	// Delete the file
	if err := os.Remove(filePath); err != nil {
		if os.IsNotExist(err) {
			return &FileUploadError{
				Code:    "FILE_NOT_FOUND",
				Message: "File not found",
			}
		}
		return &FileUploadError{
			Code:    "FILE_DELETE_ERROR",
			Message: "Failed to delete file",
			Details: err.Error(),
		}
	}

	utils.Logger.Info("File deleted successfully: " + filePath)
	return nil
}

// GetFileInfo returns information about an uploaded file
func (fs *FileUploadService) GetFileInfo(filePath string) (*UploadedFile, error) {
	// Check if file exists
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, &FileUploadError{
				Code:    "FILE_NOT_FOUND",
				Message: "File not found",
			}
		}
		return nil, &FileUploadError{
			Code:    "FILE_STAT_ERROR",
			Message: "Failed to get file information",
			Details: err.Error(),
		}
	}

	// Calculate MD5 hash
	file, err := os.Open(filePath)
	if err != nil {
		return nil, &FileUploadError{
			Code:    "FILE_OPEN_ERROR",
			Message: "Failed to open file",
			Details: err.Error(),
		}
	}
	defer file.Close()

	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		return nil, &FileUploadError{
			Code:    "HASH_CALCULATION_ERROR",
			Message: "Failed to calculate file hash",
			Details: err.Error(),
		}
	}

	md5Hash := fmt.Sprintf("%x", hash.Sum(nil))

	// Generate file info
	fileName := filepath.Base(filePath)
	ext := strings.ToLower(filepath.Ext(fileName))
	
	// Generate URL
	relPath, _ := filepath.Rel(fs.uploadDir, filePath)
	fileURL := fmt.Sprintf("%s/uploads/%s", fs.baseURL, strings.ReplaceAll(relPath, "\\", "/"))

	return &UploadedFile{
		OriginalName: fileName,
		FileName:     fileName,
		FilePath:     filePath,
		FileSize:     fileInfo.Size(),
		Extension:    ext,
		MD5Hash:      md5Hash,
		URL:          fileURL,
		UploadedAt:   fileInfo.ModTime(),
	}, nil
}

// ValidateFileType checks if a file type is allowed
func (fs *FileUploadService) ValidateFileType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	return fs.allowedTypes[ext]
}

// GetAllowedTypes returns the list of allowed file types
func (fs *FileUploadService) GetAllowedTypes() []string {
	var types []string
	for ext := range fs.allowedTypes {
		types = append(types, ext)
	}
	return types
}

// GetMaxFileSize returns the maximum allowed file size
func (fs *FileUploadService) GetMaxFileSize() int64 {
	return fs.maxFileSize
}

// CleanupOldFiles removes files older than the specified duration
func (fs *FileUploadService) CleanupOldFiles(maxAge time.Duration) error {
	cutoff := time.Now().Add(-maxAge)

	err := filepath.Walk(fs.uploadDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip directories
		if info.IsDir() {
			return nil
		}

		// Delete files older than cutoff
		if info.ModTime().Before(cutoff) {
			if err := os.Remove(path); err != nil {
				utils.Logger.Error("Failed to delete old file: " + path + " - " + err.Error())
			} else {
				utils.Logger.Info("Deleted old file: " + path)
			}
		}

		return nil
	})

	return err
}

// GetUploadStats returns statistics about uploaded files
func (fs *FileUploadService) GetUploadStats() (map[string]interface{}, error) {
	stats := map[string]interface{}{
		"total_files": 0,
		"total_size":  int64(0),
		"file_types":  make(map[string]int),
	}

	err := filepath.Walk(fs.uploadDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip directories
		if info.IsDir() {
			return nil
		}

		stats["total_files"] = stats["total_files"].(int) + 1
		stats["total_size"] = stats["total_size"].(int64) + info.Size()

		ext := strings.ToLower(filepath.Ext(info.Name()))
		fileTypes := stats["file_types"].(map[string]int)
		fileTypes[ext]++

		return nil
	})

	return stats, err
}

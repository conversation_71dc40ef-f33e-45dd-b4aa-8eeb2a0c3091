package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"adc-multi-languages/config"
	"adc-multi-languages/database"
	"adc-multi-languages/internal/container"
	"adc-multi-languages/internal/infrastructure/database/models"
	"adc-multi-languages/services"
	"adc-multi-languages/utils"
	"github.com/gin-gonic/gin"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize logger
	utils.InitLogger(cfg.LogLevel)
	utils.Logger.Info("Starting ADC Multi-Languages API server with Clean Architecture")

	// Connect to database
	dbConfig := database.Config{
		Host:     cfg.DatabaseHost,
		Port:     cfg.DatabasePort,
		User:     cfg.DatabaseUser,
		Password: cfg.DatabasePassword,
		DBName:   cfg.DatabaseName,
		SSLMode:  cfg.DatabaseSSLMode,
		URL:      cfg.DatabaseURL,
	}

	// Try to connect to database
	if cfg.DatabaseHost != "" {
		if err := database.Connect(dbConfig); err != nil {
			utils.Logger.Warn("Failed to connect to database (running without DB): " + err.Error())
		} else {
			defer database.Close()
			// Run database migrations
			if err := runCleanMigrations(); err != nil {
				utils.Logger.Warn("Failed to run database migrations: " + err.Error())
			}
		}
	} else {
		utils.Logger.Info("Database connection disabled - running in test mode")
	}

	// Initialize services (for legacy compatibility)
	services.GetCacheService()
	services.GetMetricsService()
	services.GetBackgroundJobService()
	services.InitializeStripe(cfg)

	// Create dependency injection container
	appContainer := container.NewContainer(database.GetDB())
	defer appContainer.Close()

	// Setup routes with clean architecture
	router := setupCleanRoutes(cfg, appContainer)

	// Create HTTP server
	server := &http.Server{
		Addr:         fmt.Sprintf("%s:%s", cfg.Host, cfg.Port),
		Handler:      router,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  120 * time.Second,
	}

	// Start server in a goroutine
	go func() {
		utils.Logger.WithField("address", server.Addr).Info("Clean Architecture Server starting")
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	utils.Logger.Info("Server shutting down...")

	// Graceful shutdown with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	utils.Logger.Info("Server exited")
}

// runCleanMigrations runs database migrations for clean architecture models
func runCleanMigrations() error {
	utils.Logger.Info("Running clean architecture database migrations...")

	// Auto-migrate clean architecture models
	err := database.Migrate(
		&models.User{},
		&models.Organization{},
		&models.OrganizationMembership{},
		// Add other models as they are implemented
	)

	if err != nil {
		return fmt.Errorf("failed to run clean architecture migrations: %w", err)
	}

	utils.Logger.Info("Clean architecture database migrations completed successfully")
	return nil
}

// setupCleanRoutes configures routes using clean architecture
func setupCleanRoutes(cfg *config.Config, container *container.Container) *gin.Engine {
	// Set Gin mode based on environment
	if cfg.IsProduction() {
		gin.SetMode(gin.ReleaseMode)
	}

	// Create Gin router
	router := gin.New()

	// Add middleware
	router.Use(gin.Recovery())
	router.Use(utils.LoggerMiddleware())

	// Health check
	router.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"service": "ADC Multi-Languages API",
			"version": "2.0.0-clean",
			"status":  "healthy",
		})
	})
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "healthy"})
	})

	// API routes with clean architecture
	api := router.Group("/api/v2")
	{
		// User routes
		users := api.Group("/users")
		{
			users.POST("", container.UserHandler.CreateUser)
			users.GET("", container.UserHandler.ListUsers)
			users.GET("/:id", container.UserHandler.GetUser)
			users.PUT("/:id", container.UserHandler.UpdateUser)
			users.DELETE("/:id", container.UserHandler.DeleteUser)
			users.POST("/:id/activate", container.UserHandler.ActivateUser)
			users.POST("/:id/deactivate", container.UserHandler.DeactivateUser)
			users.POST("/:id/change-password", container.UserHandler.ChangePassword)
		}

		// Auth routes
		auth := api.Group("/auth")
		{
			auth.POST("/login", container.UserHandler.AuthenticateUser)
			// TODO: Add other auth endpoints as needed
		}

		// TODO: Add other resource routes as they are implemented
		// organizations := api.Group("/organizations")
		// projects := api.Group("/projects")
		// translations := api.Group("/translations")
	}

	return router
}
# Clean Architecture Implementation

This document describes the clean architecture refactoring implemented for the ADC Multi-Languages Go backend.

## Architecture Overview

The application has been restructured following clean architecture principles with clear separation of concerns:

```
internal/
├── domain/           # Enterprise Business Rules
├── application/      # Application Business Rules  
├── infrastructure/   # Frameworks & Drivers
├── presentation/     # Interface Adapters
└── container/        # Dependency Injection
```

## Layer Details

### 1. Domain Layer (`internal/domain/`)

The innermost layer containing pure business logic and entities.

#### Entities (`internal/domain/entities/`)
- **BaseEntity**: Common fields and behavior for all entities
- **User**: User domain entity with business methods
- **Organization**: Organization domain entity with subscription logic
- **Project**: Project domain entity with locale management
- **Translation**: Translation-related entities (TranslationKey, Translation, Locale)

#### Repositories (`internal/domain/repositories/`)
- Repository interfaces defining data access contracts
- No implementation details, just interfaces
- Pagination and filtering abstractions

#### Services (`internal/domain/services/`)
- Domain service interfaces for complex business logic
- AI translation service interface
- Organization management service interface

#### Value Objects (`internal/domain/valueobjects/`)
- **Email**: Email validation and operations
- **Slug**: URL-friendly slug generation and validation
- **LocaleCode**: Locale code validation and operations

### 2. Application Layer (`internal/application/`)

Contains application-specific business rules and use cases.

#### Services (`internal/application/services/`)
- **UserService**: User management use cases
- Orchestrates domain entities and repositories
- Handles application-level validation and business rules

#### DTOs (`internal/application/dtos/`)
- **UserDTO**: Request/response objects for user operations
- **Common**: Shared DTOs (pagination, responses, etc.)
- Input validation and serialization

#### Interfaces (`internal/application/interfaces/`)
- Application service interfaces (planned)

### 3. Infrastructure Layer (`internal/infrastructure/`)

Contains implementations of external interfaces.

#### Database (`internal/infrastructure/database/`)

##### Models (`models/`)
- **User**: Database model with GORM tags
- **Organization**: Database model with relationships
- Entity ↔ Model conversion methods

##### Repositories (`repositories/`)
- **UserRepositoryImpl**: Implementation of UserRepository interface
- **RepositoryManager**: Manages all repository instances
- **UnitOfWork**: Transaction management

##### Migrations (`migrations/`)
- Database migration files (planned)

#### External (`internal/infrastructure/external/`)
- External service implementations (planned)
- Email service, payment service, etc.

### 4. Presentation Layer (`internal/presentation/`)

Contains HTTP handlers and routing.

#### Handlers (`internal/presentation/handlers/`)
- **UserHandler**: HTTP request handling for user operations
- Thin layer that delegates to application services
- HTTP-specific concerns (status codes, serialization)

#### Middleware, Requests, Responses (`middleware/`, `requests/`, `responses/`)
- Planned for HTTP-specific concerns

### 5. Container (`internal/container/`)

Dependency injection container that wires up all dependencies.

## Key Improvements

### 1. **Dependency Inversion**
- Handlers no longer directly access database
- Dependencies injected through interfaces
- Easy to mock for testing

### 2. **Separation of Concerns**
- Business logic in domain entities
- Data access in repository implementations
- HTTP concerns in handlers

### 3. **Testability**
- Each layer can be tested independently
- Mock implementations for interfaces
- Business logic testing without database

### 4. **Maintainability**
- Clear responsibility boundaries
- Easy to understand and modify
- Consistent patterns across the codebase

### 5. **Flexibility**
- Easy to swap implementations
- Database-agnostic domain layer
- Framework-independent business logic

## Usage Examples

### Running the Clean Architecture Version

```bash
# Run with clean architecture
go run main_clean.go

# The server will start on port 8300 with /api/v2 endpoints
```

### API Endpoints

The clean architecture implementation provides these endpoints:

```
POST   /api/v2/users                    # Create user
GET    /api/v2/users                    # List users
GET    /api/v2/users/:id               # Get user by ID
PUT    /api/v2/users/:id               # Update user
DELETE /api/v2/users/:id               # Delete user
POST   /api/v2/users/:id/activate      # Activate user
POST   /api/v2/users/:id/deactivate    # Deactivate user
POST   /api/v2/users/:id/change-password # Change password
POST   /api/v2/auth/login              # Authenticate user
```

### Example Request/Response

#### Create User
```bash
curl -X POST http://localhost:8300/api/v2/users \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "first_name": "John",
    "last_name": "Doe"
  }'
```

Response:
```json
{
  "success": true,
  "message": "User created successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "email": "<EMAIL>",
    "full_name": "John Doe",
    "display_name": "<EMAIL>",
    "is_active": true,
    "created_at": "2023-01-01T00:00:00Z"
  }
}
```

## Migration Strategy

### Phase 1: ✅ Completed
- [x] Domain layer foundation (entities, repositories, value objects)
- [x] Repository interfaces and implementations
- [x] Application services for user management
- [x] Dependency injection container
- [x] Clean architecture handlers

### Phase 2: In Progress
- [ ] Complete repository implementations for all entities
- [ ] Organization management services
- [ ] Project management services
- [ ] Translation services

### Phase 3: Planned
- [ ] Migrate remaining handlers to clean architecture
- [ ] Add comprehensive validation
- [ ] Implement domain services
- [ ] Add audit logging
- [ ] Performance optimizations

### Phase 4: Planned
- [ ] Remove legacy code
- [ ] Update frontend integration
- [ ] Documentation updates
- [ ] Production deployment

## Testing Strategy

### Unit Tests
```go
// Example: Testing user service
func TestUserService_CreateUser(t *testing.T) {
    // Arrange
    mockRepo := &mocks.MockUserRepository{}
    service := services.NewUserService(mockRepo)
    
    // Act & Assert
    user, err := service.CreateUser(ctx, &dtos.CreateUserRequest{
        Email: "<EMAIL>",
        Password: "password123",
    })
    
    assert.NoError(t, err)
    assert.Equal(t, "<EMAIL>", user.Email)
}
```

### Integration Tests
```go
// Example: Testing with real database
func TestUserRepository_Integration(t *testing.T) {
    // Setup test database
    db := setupTestDB(t)
    repo := repositories.NewUserRepository(db)
    
    // Test create and retrieve
    user := entities.NewUser("<EMAIL>", "hash")
    err := repo.Create(ctx, user)
    assert.NoError(t, err)
    
    retrieved, err := repo.GetByEmail(ctx, "<EMAIL>")
    assert.NoError(t, err)
    assert.Equal(t, user.Email, retrieved.Email)
}
```

## Best Practices

### 1. **Entity Methods**
- Keep business logic in entity methods
- Validate data integrity at entity level
- Use value objects for complex validations

### 2. **Repository Patterns**
- Define interfaces in domain layer
- Implement in infrastructure layer
- Use specific methods rather than generic CRUD

### 3. **Service Layer**
- Orchestrate entities and repositories
- Handle application-level business rules
- Keep handlers thin

### 4. **Error Handling**
- Domain errors for business rule violations
- Application errors for use case failures
- Infrastructure errors for technical issues

### 5. **Testing**
- Mock repository interfaces for unit tests
- Use test containers for integration tests
- Test business logic independently of frameworks

## Benefits Achieved

1. **Clean Separation**: Each layer has a clear responsibility
2. **Testability**: Easy to write unit and integration tests
3. **Maintainability**: Code is easier to understand and modify
4. **Flexibility**: Easy to change implementations
5. **Domain Focus**: Business logic is clearly separated
6. **Future-Proof**: Architecture supports growth and changes

This clean architecture implementation provides a solid foundation for the ADC Multi-Languages platform that will scale with the business requirements while maintaining code quality and developer productivity.
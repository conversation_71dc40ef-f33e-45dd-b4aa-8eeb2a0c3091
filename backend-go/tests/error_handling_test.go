package tests

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestErrorHandling tests various error scenarios and edge cases
func (suite *IntegrationTestSuite) TestErrorHandling() {
	// Test authentication errors
	suite.T().Run("AuthenticationErrors", func(t *testing.T) {
		// Test missing authorization header
		w := suite.makeRequest("GET", "/api/users/me", nil, nil)
		assert.Equal(t, http.StatusUnauthorized, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, false, response["success"])
		assert.Contains(t, response["message"], "Authorization header")

		// Test invalid token format
		headers := map[string]string{
			"Authorization": "InvalidFormat",
		}
		w = suite.makeRequest("GET", "/api/users/me", nil, headers)
		assert.Equal(t, http.StatusUnauthorized, w.Code)

		// Test expired/invalid token
		headers = map[string]string{
			"Authorization": "Bearer invalid.token.here",
		}
		w = suite.makeRequest("GET", "/api/users/me", nil, headers)
		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	// Test validation errors
	suite.T().Run("ValidationErrors", func(t *testing.T) {
		// Test invalid email format
		signUpData := map[string]interface{}{
			"email":      "invalid-email",
			"password":   "password123",
			"first_name": "Test",
			"last_name":  "User",
		}
		w := suite.makeRequest("POST", "/api/auth/signup", signUpData, nil)
		assert.Equal(t, http.StatusBadRequest, w.Code)

		// Test missing required fields
		signUpData = map[string]interface{}{
			"email": "<EMAIL>",
			// Missing password
		}
		w = suite.makeRequest("POST", "/api/auth/signup", signUpData, nil)
		assert.Equal(t, http.StatusBadRequest, w.Code)

		// Test password too short
		signUpData = map[string]interface{}{
			"email":      "<EMAIL>",
			"password":   "123", // Too short
			"first_name": "Test",
			"last_name":  "User",
		}
		w = suite.makeRequest("POST", "/api/auth/signup", signUpData, nil)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	// Test resource not found errors
	suite.T().Run("ResourceNotFound", func(t *testing.T) {
		// Test non-existent user
		w := suite.makeAuthenticatedRequest("GET", "/api/users/00000000-0000-0000-0000-000000000000", nil)
		assert.Equal(t, http.StatusNotFound, w.Code)

		// Test non-existent organization
		w = suite.makeAuthenticatedRequest("GET", "/api/organizations/00000000-0000-0000-0000-000000000000", nil)
		assert.Equal(t, http.StatusNotFound, w.Code)

		// Test non-existent project
		w = suite.makeAuthenticatedRequest("GET", "/api/projects/00000000-0000-0000-0000-000000000000", nil)
		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	// Test permission errors
	suite.T().Run("PermissionErrors", func(t *testing.T) {
		// Try to access another organization's resources
		// This would require creating another organization and user
		// For now, test with invalid organization ID
		w := suite.makeAuthenticatedRequest("GET", "/api/organizations/00000000-0000-0000-0000-000000000000/projects", nil)
		assert.Equal(t, http.StatusNotFound, w.Code)
	})
}

// TestInputValidation tests input validation and sanitization
func (suite *IntegrationTestSuite) TestInputValidation() {
	// Test SQL injection attempts
	suite.T().Run("SQLInjectionPrevention", func(t *testing.T) {
		// Test SQL injection in search
		maliciousQuery := "'; DROP TABLE users; --"
		url := fmt.Sprintf("/api/projects/%s/search?q=%s", suite.projectID, maliciousQuery)
		w := suite.makeAuthenticatedRequest("GET", url, nil)
		
		// Should not crash and should return proper response
		assert.NotEqual(t, http.StatusInternalServerError, w.Code)
	})

	// Test XSS prevention
	suite.T().Run("XSSPrevention", func(t *testing.T) {
		// Test XSS in project name
		projectData := map[string]interface{}{
			"organization_id": suite.orgID,
			"name":            "<script>alert('xss')</script>",
			"description":     "Test project",
			"default_locale":  "en",
		}

		w := suite.makeAuthenticatedRequest("POST", "/api/projects", projectData)
		
		if w.Code == http.StatusCreated {
			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)
			
			data := response["data"].(map[string]interface{})
			// Name should be sanitized or escaped
			name := data["name"].(string)
			assert.NotContains(t, name, "<script>")
		}
	})

	// Test oversized input
	suite.T().Run("OversizedInput", func(t *testing.T) {
		// Test very long project name
		longName := strings.Repeat("a", 1000)
		projectData := map[string]interface{}{
			"organization_id": suite.orgID,
			"name":            longName,
			"description":     "Test project",
			"default_locale":  "en",
		}

		w := suite.makeAuthenticatedRequest("POST", "/api/projects", projectData)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	// Test invalid JSON
	suite.T().Run("InvalidJSON", func(t *testing.T) {
		// Send malformed JSON
		req := suite.makeRequest("POST", "/api/projects", "invalid json", map[string]string{
			"Authorization": "Bearer " + suite.authToken,
			"Content-Type":  "application/json",
		})
		assert.Equal(t, http.StatusBadRequest, req.Code)
	})
}

// TestRateLimiting tests rate limiting functionality
func (suite *IntegrationTestSuite) TestRateLimiting() {
	suite.T().Run("AuthenticationRateLimit", func(t *testing.T) {
		// Make multiple failed login attempts
		signInData := map[string]interface{}{
			"email":    "<EMAIL>",
			"password": "wrongpassword",
		}

		// Make several requests quickly
		for i := 0; i < 10; i++ {
			w := suite.makeRequest("POST", "/api/auth/signin", signInData, nil)
			// Should get unauthorized, but not rate limited in test environment
			assert.Equal(t, http.StatusUnauthorized, w.Code)
		}
	})
}

// TestConcurrency tests concurrent access scenarios
func (suite *IntegrationTestSuite) TestConcurrency() {
	suite.T().Run("ConcurrentProjectCreation", func(t *testing.T) {
		// This test would require goroutines to test concurrent access
		// For now, just test sequential creation with similar names
		
		projectData1 := map[string]interface{}{
			"organization_id": suite.orgID,
			"name":            "Concurrent Test 1",
			"description":     "Test project 1",
			"default_locale":  "en",
		}

		projectData2 := map[string]interface{}{
			"organization_id": suite.orgID,
			"name":            "Concurrent Test 2",
			"description":     "Test project 2",
			"default_locale":  "en",
		}

		w1 := suite.makeAuthenticatedRequest("POST", "/api/projects", projectData1)
		w2 := suite.makeAuthenticatedRequest("POST", "/api/projects", projectData2)

		// Both should succeed
		assert.Equal(t, http.StatusCreated, w1.Code)
		assert.Equal(t, http.StatusCreated, w2.Code)
	})
}

// TestEdgeCases tests various edge cases
func (suite *IntegrationTestSuite) TestEdgeCases() {
	// Test empty request body
	suite.T().Run("EmptyRequestBody", func(t *testing.T) {
		w := suite.makeAuthenticatedRequest("POST", "/api/projects", nil)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	// Test invalid UUID formats
	suite.T().Run("InvalidUUIDFormat", func(t *testing.T) {
		w := suite.makeAuthenticatedRequest("GET", "/api/projects/invalid-uuid", nil)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	// Test special characters in URLs
	suite.T().Run("SpecialCharactersInURL", func(t *testing.T) {
		// Test URL encoding
		searchQuery := "test query with spaces & symbols"
		url := fmt.Sprintf("/api/projects/%s/search?q=%s", suite.projectID, searchQuery)
		w := suite.makeAuthenticatedRequest("GET", url, nil)
		
		// Should handle URL encoding properly
		assert.NotEqual(t, http.StatusInternalServerError, w.Code)
	})

	// Test pagination edge cases
	suite.T().Run("PaginationEdgeCases", func(t *testing.T) {
		// Test negative page number
		w := suite.makeAuthenticatedRequest("GET", "/api/projects?page=-1", nil)
		assert.NotEqual(t, http.StatusInternalServerError, w.Code)

		// Test very large page number
		w = suite.makeAuthenticatedRequest("GET", "/api/projects?page=999999", nil)
		assert.NotEqual(t, http.StatusInternalServerError, w.Code)

		// Test invalid per_page value
		w = suite.makeAuthenticatedRequest("GET", "/api/projects?per_page=0", nil)
		assert.NotEqual(t, http.StatusInternalServerError, w.Code)

		// Test very large per_page value
		w = suite.makeAuthenticatedRequest("GET", "/api/projects?per_page=10000", nil)
		assert.NotEqual(t, http.StatusInternalServerError, w.Code)
	})
}

// TestDatabaseConstraints tests database constraint violations
func (suite *IntegrationTestSuite) TestDatabaseConstraints() {
	// Test duplicate email registration
	suite.T().Run("DuplicateEmailRegistration", func(t *testing.T) {
		signUpData := map[string]interface{}{
			"email":      "<EMAIL>",
			"password":   "password123",
			"first_name": "First",
			"last_name":  "User",
		}

		// First registration should succeed
		w1 := suite.makeRequest("POST", "/api/auth/signup", signUpData, nil)
		assert.Equal(t, http.StatusCreated, w1.Code)

		// Second registration with same email should fail
		w2 := suite.makeRequest("POST", "/api/auth/signup", signUpData, nil)
		assert.Equal(t, http.StatusConflict, w2.Code)
	})

	// Test duplicate organization slug
	suite.T().Run("DuplicateOrganizationSlug", func(t *testing.T) {
		orgData := map[string]interface{}{
			"name": "Duplicate Org",
			"slug": "duplicate-org-slug",
		}

		// First creation should succeed
		w1 := suite.makeAuthenticatedRequest("POST", "/api/organizations", orgData)
		assert.Equal(t, http.StatusCreated, w1.Code)

		// Second creation with same slug should fail
		orgData["name"] = "Another Duplicate Org"
		w2 := suite.makeAuthenticatedRequest("POST", "/api/organizations", orgData)
		assert.Equal(t, http.StatusConflict, w2.Code)
	})
}

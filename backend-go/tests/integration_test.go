package tests

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"adc-multi-languages/config"
	"adc-multi-languages/middleware"
	"adc-multi-languages/models"
	"adc-multi-languages/routes"
	"adc-multi-languages/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// IntegrationTestSuite defines the test suite
type IntegrationTestSuite struct {
	suite.Suite
	router    *gin.Engine
	db        *gorm.DB
	config    *config.Config
	authToken string
	userID    string
	orgID     string
	projectID string
}

// SetupSuite runs once before all tests
func (suite *IntegrationTestSuite) SetupSuite() {
	// Set test environment
	os.Setenv("APP_ENV", "test")
	os.Setenv("DATABASE_URL", "postgresql://postgres:<EMAIL>:5432/postgres")
	os.Setenv("JWT_SECRET", "test-secret-key-for-testing-only")
	os.Setenv("JWT_REFRESH_SECRET", "test-refresh-secret-key")

	// Load test configuration
	cfg, err := config.Load()
	suite.Require().NoError(err)
	suite.config = cfg

	// Initialize logger to avoid nil pointer issues
	utils.InitLogger("error")

	// Initialize test database using the safer setup
	suite.db, err = SetupTestDatabase(cfg)
	suite.Require().NoError(err)

	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Setup routes
	suite.router = routes.SetupRoutes(cfg)

	// Create test data
	suite.createTestData()
}

// TearDownSuite runs once after all tests
func (suite *IntegrationTestSuite) TearDownSuite() {
	suite.cleanDatabase()
}

// SetupTest runs before each test
func (suite *IntegrationTestSuite) SetupTest() {
	// Clean up any test-specific data if needed
}

// TearDownTest runs after each test
func (suite *IntegrationTestSuite) TearDownTest() {
	// Clean up any test-specific data if needed
}

// cleanDatabase removes all test data (only test-specific data)
func (suite *IntegrationTestSuite) cleanDatabase() {
	// Only clean test data by filtering with test-specific patterns
	// This is safer when using a shared database like Supabase
	// Clean in reverse dependency order to avoid foreign key violations

	// Clean translation histories first
	suite.db.Unscoped().Where("translation_id IN (SELECT id FROM translations WHERE key_id IN (SELECT id FROM translation_keys WHERE project_id IN (SELECT id FROM projects WHERE name LIKE '%Test%' OR slug LIKE '%test%')))").Delete(&models.TranslationHistory{})

	// Clean translations
	suite.db.Unscoped().Where("key_id IN (SELECT id FROM translation_keys WHERE project_id IN (SELECT id FROM projects WHERE name LIKE '%Test%' OR slug LIKE '%test%'))").Delete(&models.Translation{})

	// Clean translation keys
	suite.db.Unscoped().Where("project_id IN (SELECT id FROM projects WHERE name LIKE '%Test%' OR slug LIKE '%test%')").Delete(&models.TranslationKey{})

	// Clean API keys for test organizations
	suite.db.Unscoped().Where("organization_id IN (SELECT id FROM organizations WHERE name LIKE '%Test%' OR slug LIKE '%test%')").Delete(&models.APIKey{})

	// Clean AI credit transactions
	suite.db.Unscoped().Where("organization_id IN (SELECT id FROM organizations WHERE name LIKE '%Test%' OR slug LIKE '%test%')").Delete(&models.AICreditTransaction{})

	// Clean organization memberships
	suite.db.Unscoped().Where("organization_id IN (SELECT id FROM organizations WHERE name LIKE '%Test%' OR slug LIKE '%test%')").Delete(&models.OrganizationMembership{})

	// Clean test projects
	suite.db.Unscoped().Where("name LIKE '%Test%' OR slug LIKE '%test%'").Delete(&models.Project{})

	// Clean test organizations
	suite.db.Unscoped().Where("name LIKE '%Test%' OR slug LIKE '%test%'").Delete(&models.Organization{})

	// Clean test users (only those with test emails)
	suite.db.Unscoped().Where("email LIKE '%@test.example.com' OR email LIKE '%test%'").Delete(&models.User{})

	// Clean test locales (only test-specific ones)
	suite.db.Unscoped().Where("code IN ('test-en', 'test-es', 'test-fr')").Delete(&models.Locale{})
}

// assertResponseSuccess checks if response is successful and returns data
func (suite *IntegrationTestSuite) assertResponseSuccess(w *httptest.ResponseRecorder, expectedStatus int) map[string]interface{} {
	assert.Equal(suite.T(), expectedStatus, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), true, response["success"])

	return response
}

// assertResponseError checks if response is an error
func (suite *IntegrationTestSuite) assertResponseError(w *httptest.ResponseRecorder, expectedStatus int) map[string]interface{} {
	assert.Equal(suite.T(), expectedStatus, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), false, response["success"])

	return response
}

// createTestTranslationKey creates a test translation key
func (suite *IntegrationTestSuite) createTestTranslationKey(projectID, key, description string) string {
	keyData := map[string]interface{}{
		"key":         key,
		"description": description,
		"project_id":  projectID,
	}

	url := fmt.Sprintf("/api/projects/%s/keys", projectID)
	w := suite.makeAuthenticatedRequest("POST", url, keyData)

	response := suite.assertResponseSuccess(w, http.StatusCreated)
	data := response["data"].(map[string]interface{})
	return data["id"].(string)
}

// createTestTranslation creates a test translation
func (suite *IntegrationTestSuite) createTestTranslation(keyID, locale, value string) string {
	translationData := map[string]interface{}{
		"key_id": keyID,
		"locale": locale,
		"value":  value,
	}

	w := suite.makeAuthenticatedRequest("POST", "/api/translations", translationData)

	response := suite.assertResponseSuccess(w, http.StatusCreated)
	data := response["data"].(map[string]interface{})
	return data["id"].(string)
}

// createTestData creates initial test data
func (suite *IntegrationTestSuite) createTestData() {
	// Create test user with test-specific email
	user := models.User{
		Email:     "<EMAIL>",
		FirstName: &[]string{"Test"}[0],
		LastName:  &[]string{"User"}[0],
	}
	// Set password hash for "password123"
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("password123"), bcrypt.DefaultCost)
	user.PasswordHash = string(hashedPassword)
	user.EmailVerified = true

	err := suite.db.Create(&user).Error
	suite.Require().NoError(err)
	suite.userID = user.ID.String()

	// Create test organization with test-specific naming
	org := models.Organization{
		Name:               "Integration Test Organization",
		Slug:               "integration-test-org",
		SubscriptionTier:   "free",
		AICreditsRemaining: &[]int{1000}[0],
	}
	err = suite.db.Create(&org).Error
	suite.Require().NoError(err)
	suite.orgID = org.ID.String()

	// Create organization membership
	membership := models.OrganizationMembership{
		OrganizationID: org.ID,
		UserID:         user.ID,
		Role:           "admin",
		IsActive:       true,
	}
	err = suite.db.Create(&membership).Error
	suite.Require().NoError(err)

	// Create test project with test-specific naming
	project := models.Project{
		OrganizationID: org.ID,
		Name:           "Integration Test Project",
		Slug:           "integration-test-project",
		DefaultLocale:  &[]string{"en"}[0],
	}
	err = suite.db.Create(&project).Error
	suite.Require().NoError(err)
	suite.projectID = project.ID.String()

	// Create test locales
	locales := []models.Locale{
		{Code: "en", Name: "English", NativeName: "English", Direction: "ltr", IsActive: true},
		{Code: "es", Name: "Spanish", NativeName: "Español", Direction: "ltr", IsActive: true},
		{Code: "fr", Name: "French", NativeName: "Français", Direction: "ltr", IsActive: true},
	}
	for _, locale := range locales {
		err = suite.db.Create(&locale).Error
		suite.Require().NoError(err)
	}

	// Create test subscription plan
	plan := models.SubscriptionPlan{
		Name:                      "Integration Test Plan",
		Description:               "Integration test subscription plan",
		PriceMonthly:              9.99,
		AICreditsMonthlyAllowance: 5000,
		MaxProjects:               &[]int{10}[0],
		MaxUsers:                  &[]int{5}[0],
		Features:                  `["basic_features", "api_access"]`,
		IsActive:                  true,
	}
	err = suite.db.Create(&plan).Error
	suite.Require().NoError(err)

	// Generate auth token
	suite.authToken = suite.generateAuthToken(user.ID.String())
}

// generateAuthToken creates a JWT token for testing
func (suite *IntegrationTestSuite) generateAuthToken(userID string) string {
	authConfig := middleware.AuthConfig{
		JWTSecret:          suite.config.JWTSecret,
		JWTExpirationHours: suite.config.JWTExpirationHours,
	}
	token, err := middleware.GenerateJWT(authConfig, userID, "<EMAIL>", "")
	suite.Require().NoError(err)
	return token
}

// makeRequest makes an HTTP request and returns the response
func (suite *IntegrationTestSuite) makeRequest(method, url string, body interface{}, headers map[string]string) *httptest.ResponseRecorder {
	var reqBody []byte
	if body != nil {
		var err error
		reqBody, err = json.Marshal(body)
		suite.Require().NoError(err)
	}

	req, err := http.NewRequest(method, url, bytes.NewBuffer(reqBody))
	suite.Require().NoError(err)

	// Set default headers
	req.Header.Set("Content-Type", "application/json")

	// Set custom headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	// Create response recorder
	w := httptest.NewRecorder()

	// Perform request
	suite.router.ServeHTTP(w, req)

	return w
}

// makeAuthenticatedRequest makes an authenticated HTTP request
func (suite *IntegrationTestSuite) makeAuthenticatedRequest(method, url string, body interface{}) *httptest.ResponseRecorder {
	headers := map[string]string{
		"Authorization": "Bearer " + suite.authToken,
	}
	return suite.makeRequest(method, url, body, headers)
}

// TestHealthEndpoint tests the health check endpoint
func (suite *IntegrationTestSuite) TestHealthEndpoint() {
	w := suite.makeRequest("GET", "/health", nil, nil)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "healthy", response["status"])
}

// TestAuthSignUp tests user registration
func (suite *IntegrationTestSuite) TestAuthSignUp() {
	signUpData := map[string]interface{}{
		"email":      "<EMAIL>",
		"password":   "password123",
		"first_name": "New",
		"last_name":  "User",
	}

	w := suite.makeRequest("POST", "/api/auth/signup", signUpData, nil)

	assert.Equal(suite.T(), http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), true, response["success"])
	assert.Contains(suite.T(), response, "data")
}

// TestAuthSignIn tests user login
func (suite *IntegrationTestSuite) TestAuthSignIn() {
	signInData := map[string]interface{}{
		"email":    "<EMAIL>",
		"password": "password123",
	}

	w := suite.makeRequest("POST", "/api/auth/signin", signInData, nil)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), true, response["success"])

	data := response["data"].(map[string]interface{})
	assert.Contains(suite.T(), data, "access_token")
	assert.Contains(suite.T(), data, "refresh_token")
}

// TestUserProfile tests getting user profile
func (suite *IntegrationTestSuite) TestUserProfile() {
	w := suite.makeAuthenticatedRequest("GET", "/api/users/me", nil)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), true, response["success"])

	data := response["data"].(map[string]interface{})
	assert.Equal(suite.T(), "<EMAIL>", data["email"])
}

// TestOrganizationsList tests listing organizations
func (suite *IntegrationTestSuite) TestOrganizationsList() {
	w := suite.makeAuthenticatedRequest("GET", "/api/organizations", nil)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), true, response["success"])

	data := response["data"].([]interface{})
	assert.Len(suite.T(), data, 1)

	org := data[0].(map[string]interface{})
	assert.Equal(suite.T(), "Integration Test Organization", org["name"])
}

// TestCreateProject tests creating a new project
func (suite *IntegrationTestSuite) TestCreateProject() {
	projectData := map[string]interface{}{
		"organization_id": suite.orgID,
		"name":            "New Test Project",
		"description":     "A new test project",
		"default_locale":  "en",
	}

	w := suite.makeAuthenticatedRequest("POST", "/api/projects", projectData)

	assert.Equal(suite.T(), http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), true, response["success"])

	data := response["data"].(map[string]interface{})
	assert.Equal(suite.T(), "New Test Project", data["name"])
	assert.Contains(suite.T(), data, "id")
}

// TestListLocales tests listing locales
func (suite *IntegrationTestSuite) TestListLocales() {
	w := suite.makeAuthenticatedRequest("GET", "/api/locales", nil)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), true, response["success"])

	data := response["data"].([]interface{})
	assert.GreaterOrEqual(suite.T(), len(data), 3) // We created 3 locales
}

// Run the test suite
func TestIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(IntegrationTestSuite))
}

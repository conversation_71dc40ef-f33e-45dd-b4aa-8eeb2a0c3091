package tests

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"adc-multi-languages/config"
	"adc-multi-languages/database"
	"adc-multi-languages/middleware"
	"adc-multi-languages/models"
	"adc-multi-languages/routes"
	"adc-multi-languages/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// WorkingIntegrationTestSuite defines a working test suite
type WorkingIntegrationTestSuite struct {
	suite.Suite
	router *gin.Engine
	db     *gorm.DB
	config *config.Config
}

// SetupSuite runs once before all tests
func (suite *WorkingIntegrationTestSuite) SetupSuite() {
	// Set test environment
	os.Setenv("APP_ENV", "test")
	os.Setenv("DATABASE_URL", "postgresql://postgres:<EMAIL>:5432/postgres")
	os.Setenv("JWT_SECRET", "test-secret-key-for-testing-only")
	os.Setenv("JWT_REFRESH_SECRET", "test-refresh-secret-key")
	os.Setenv("LOG_LEVEL", "error")

	// Initialize logger first
	utils.InitLogger("error")

	// Load configuration
	cfg, err := config.Load()
	suite.Require().NoError(err)
	suite.config = cfg

	// Connect to database
	dbConfig := database.Config{
		URL: cfg.DatabaseURL,
	}
	err = database.Connect(dbConfig)
	suite.Require().NoError(err)
	suite.db = database.GetDB()

	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Setup routes
	suite.router = routes.SetupRoutes(cfg)
}

// TearDownSuite runs once after all tests
func (suite *WorkingIntegrationTestSuite) TearDownSuite() {
	// Clean up test data
	suite.cleanTestData()
}

// cleanTestData removes test-specific data
func (suite *WorkingIntegrationTestSuite) cleanTestData() {
	// Only clean test data with specific patterns
	suite.db.Unscoped().Where("email LIKE '%@test-integration.com'").Delete(&models.User{})
	suite.db.Unscoped().Where("name LIKE 'Working Test%'").Delete(&models.Organization{})
	suite.db.Unscoped().Where("name LIKE 'Working Test%'").Delete(&models.Project{})
}

// makeRequest makes an HTTP request
func (suite *WorkingIntegrationTestSuite) makeRequest(method, url string, body interface{}, headers map[string]string) *httptest.ResponseRecorder {
	var reqBody []byte
	if body != nil {
		var err error
		reqBody, err = json.Marshal(body)
		suite.Require().NoError(err)
	}

	req, err := http.NewRequest(method, url, bytes.NewBuffer(reqBody))
	suite.Require().NoError(err)

	// Set default headers
	req.Header.Set("Content-Type", "application/json")

	// Set custom headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)
	return w
}

// TestHealthEndpoint tests the health check endpoint
func (suite *WorkingIntegrationTestSuite) TestHealthEndpoint() {
	w := suite.makeRequest("GET", "/health", nil, nil)
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Contains(suite.T(), response, "status")
}

// TestAuthSignUpValidation tests signup validation
func (suite *WorkingIntegrationTestSuite) TestAuthSignUpValidation() {
	// Test missing email
	signUpData := map[string]interface{}{
		"password":   "password123",
		"first_name": "Test",
		"last_name":  "User",
	}

	w := suite.makeRequest("POST", "/api/auth/signup", signUpData, nil)
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)

	// Test invalid email format
	signUpData = map[string]interface{}{
		"email":      "invalid-email",
		"password":   "password123",
		"first_name": "Test",
		"last_name":  "User",
	}

	w = suite.makeRequest("POST", "/api/auth/signup", signUpData, nil)
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)

	// Test missing password
	signUpData = map[string]interface{}{
		"email":      "<EMAIL>",
		"first_name": "Test",
		"last_name":  "User",
	}

	w = suite.makeRequest("POST", "/api/auth/signup", signUpData, nil)
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)
}

// TestAuthSignInValidation tests signin validation
func (suite *WorkingIntegrationTestSuite) TestAuthSignInValidation() {
	// Test missing credentials
	w := suite.makeRequest("POST", "/api/auth/signin", map[string]interface{}{}, nil)
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)

	// Test invalid credentials
	signInData := map[string]interface{}{
		"email":    "<EMAIL>",
		"password": "wrongpassword",
	}

	w = suite.makeRequest("POST", "/api/auth/signin", signInData, nil)
	assert.Equal(suite.T(), http.StatusUnauthorized, w.Code)
}

// TestUnauthorizedAccess tests accessing protected endpoints without auth
func (suite *WorkingIntegrationTestSuite) TestUnauthorizedAccess() {
	protectedEndpoints := []string{
		"/api/users/me",
		"/api/organizations",
		"/api/projects",
	}

	for _, endpoint := range protectedEndpoints {
		w := suite.makeRequest("GET", endpoint, nil, nil)
		assert.Equal(suite.T(), http.StatusUnauthorized, w.Code, "Endpoint %s should require authentication", endpoint)
	}
}

// TestInvalidJSONRequest tests handling of invalid JSON
func (suite *WorkingIntegrationTestSuite) TestInvalidJSONRequest() {
	req, _ := http.NewRequest("POST", "/api/auth/signup", bytes.NewBufferString("invalid json"))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)
}

// TestCORSHeaders tests CORS functionality
func (suite *WorkingIntegrationTestSuite) TestCORSHeaders() {
	req, _ := http.NewRequest("OPTIONS", "/api/auth/signup", nil)
	req.Header.Set("Origin", "http://localhost:3000")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Should have CORS headers
	assert.NotEmpty(suite.T(), w.Header().Get("Access-Control-Allow-Origin"))
}

// TestDatabaseConnection tests database connectivity
func (suite *WorkingIntegrationTestSuite) TestDatabaseConnection() {
	// Test basic query
	var result int
	err := suite.db.Raw("SELECT 1").Scan(&result).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), 1, result)

	// Test table existence
	var tableExists bool
	err = suite.db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'users')").Scan(&tableExists).Error
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), tableExists)
}

// TestJWTTokenGeneration tests JWT functionality
func (suite *WorkingIntegrationTestSuite) TestJWTTokenGeneration() {
	authConfig := middleware.AuthConfig{
		JWTSecret:          suite.config.JWTSecret,
		JWTExpirationHours: 24,
	}

	userID := "123e4567-e89b-12d3-a456-426614174000"
	email := "<EMAIL>"

	// Test token generation
	token, err := middleware.GenerateJWT(authConfig, userID, email, "")
	assert.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), token)

	// Test token validation
	claims, err := middleware.ValidateToken(authConfig, token)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), userID, claims.UserID)
	assert.Equal(suite.T(), email, claims.Email)
}

// TestPasswordHashing tests password hashing
func (suite *WorkingIntegrationTestSuite) TestPasswordHashing() {
	password := "testpassword123"

	// Test password hashing
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	assert.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), hashedPassword)

	// Test password verification
	err = bcrypt.CompareHashAndPassword(hashedPassword, []byte(password))
	assert.NoError(suite.T(), err)

	// Test wrong password
	err = bcrypt.CompareHashAndPassword(hashedPassword, []byte("wrongpassword"))
	assert.Error(suite.T(), err)
}

// TestUserRegistrationFlow tests the complete user registration flow
func (suite *WorkingIntegrationTestSuite) TestUserRegistrationFlow() {
	// Test successful registration
	signUpData := map[string]interface{}{
		"email":      "<EMAIL>",
		"password":   "password123",
		"first_name": "New",
		"last_name":  "User",
	}

	w := suite.makeRequest("POST", "/api/auth/signup", signUpData, nil)

	// Should succeed, return conflict if user exists, or bad request for validation errors
	validStatusCodes := []int{http.StatusCreated, http.StatusConflict, http.StatusBadRequest}
	assert.Contains(suite.T(), validStatusCodes, w.Code, "Registration should return 201, 409, or 400")

	if w.Code == http.StatusCreated {
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(suite.T(), err)
		assert.Equal(suite.T(), true, response["success"])
	}
}

// TestErrorHandling tests various error scenarios
func (suite *WorkingIntegrationTestSuite) TestErrorHandling() {
	// Test 404 for non-existent endpoint
	w := suite.makeRequest("GET", "/api/nonexistent", nil, nil)
	assert.Equal(suite.T(), http.StatusNotFound, w.Code)

	// Test method not allowed - use an endpoint that exists but with wrong method
	w = suite.makeRequest("DELETE", "/api/auth/signup", nil, nil)
	// This might return 404 or 405 depending on router configuration
	assert.True(suite.T(), w.Code == http.StatusNotFound || w.Code == http.StatusMethodNotAllowed)
}

// Run the working test suite
func TestWorkingIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(WorkingIntegrationTestSuite))
}

# Integration Tests for Go Backend

This directory contains comprehensive integration tests for the Go backend API. The tests cover all major functionality including authentication, organization management, project management, translation operations, and more.

## 📋 Test Structure

### Test Files

- **`integration_test.go`** - Main test suite with core functionality tests
- **`api_keys_test.go`** - API key management and usage analytics tests
- **`credit_management_test.go`** - AI credit management and analytics tests
- **`slug_translation_test.go`** - Slug-based lookups and translation tests
- **`subscription_test.go`** - Subscription management and audit log tests
- **`translation_management_test.go`** - Translation CRUD, import/export, and search tests

### Helper Files

- **`test_config.go`** - Test configuration and setup utilities
- **`run_integration_tests.go`** - Standalone test runner with advanced features
- **`Makefile`** - Make targets for easy test execution
- **`README.md`** - This documentation file

## 🚀 Quick Start

### Prerequisites

1. **Go 1.23+** installed
2. **PostgreSQL** running locally
3. **Environment variables** configured (see below)

### Running Tests

```bash
# Simple test execution
make test

# Verbose output
make test-verbose

# With coverage report
make test-coverage

# Run specific tests
make test-specific PATTERN=TestAuth

# Full test suite with database setup
make test-full
```

### Alternative: Using Go directly

```bash
# Set environment variables
export APP_ENV=test
export DATABASE_URL="postgres://postgres:password@localhost:5432/adc_test?sslmode=disable"
export JWT_SECRET="test-secret-key-for-testing-only"

# Run tests
go test -v ./tests/...
```

## 🔧 Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `APP_ENV` | `test` | Application environment |
| `DATABASE_URL` | `postgres://postgres:password@localhost:5432/adc_test?sslmode=disable` | Test database URL |
| `JWT_SECRET` | `test-secret-key-for-testing-only` | JWT secret for testing |
| `JWT_REFRESH_SECRET` | `test-refresh-secret-key` | JWT refresh secret |
| `JWT_EXPIRATION_HOURS` | `24` | JWT token expiration |
| `LOG_LEVEL` | `error` | Log level during tests |
| `PORT` | `8301` | Test server port |

### Database Setup

The tests require a PostgreSQL database. You can set it up using:

```bash
# Create test database
make setup-db

# Or manually
psql -h localhost -U postgres -c "CREATE DATABASE adc_test;"
```

## 📊 Test Categories

### 1. Authentication Tests
- User registration and login
- JWT token validation
- Password reset flow
- Email verification

### 2. Organization Management
- Organization CRUD operations
- Membership management
- Subscription handling

### 3. Project Management
- Project creation and management
- Locale assignment
- Resource management

### 4. Translation Operations
- Translation key management
- Translation CRUD operations
- Import/export functionality
- Search and filtering

### 5. API Key Management
- API key creation and management
- Usage analytics
- Permission validation

### 6. Credit Management
- AI credit tracking
- Usage analytics
- Limit management

### 7. Audit and Logging
- Audit log functionality
- Permission tracking
- Usage monitoring

## 🎯 Test Execution Options

### Make Targets

```bash
make test           # Run all tests
make test-verbose   # Verbose output
make test-short     # Skip slow tests
make test-coverage  # Generate coverage report
make test-race      # Race condition detection
make test-ci        # CI environment tests
make test-watch     # Watch for changes
make benchmark      # Run benchmarks
make profile        # Generate profiles
```

### Advanced Test Runner

```bash
# Using the custom test runner
cd tests
go run run_integration_tests.go -v -coverage

# With specific pattern
go run run_integration_tests.go -run TestAuth -v

# Short mode
go run run_integration_tests.go -short
```

## 📈 Coverage Reports

Coverage reports are generated in multiple formats:

- **`coverage.out`** - Raw coverage data
- **`coverage.html`** - HTML coverage report
- **Console output** - Summary statistics

View the HTML report:
```bash
open coverage.html  # macOS
xdg-open coverage.html  # Linux
```

## 🔍 Debugging Tests

### Verbose Output
```bash
make test-verbose
```

### Running Single Tests
```bash
go test -v -run TestSpecificFunction ./tests/
```

### Database Inspection
```bash
# Connect to test database
psql postgres://postgres:password@localhost:5432/adc_test
```

### Log Analysis
Tests run with `LOG_LEVEL=error` by default. Change to `debug` for detailed logs:
```bash
export LOG_LEVEL=debug
make test-verbose
```

## 🧹 Cleanup

```bash
# Clean test artifacts
make clean

# Remove test database
make teardown-db

# Full cleanup
make clean teardown-db
```

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```bash
   # Check PostgreSQL is running
   pg_ctl status
   
   # Verify connection
   psql -h localhost -U postgres -c "SELECT 1;"
   ```

2. **Port Already in Use**
   ```bash
   # Change test port
   export PORT=8302
   ```

3. **Permission Denied**
   ```bash
   # Check database permissions
   psql -h localhost -U postgres -c "CREATE DATABASE test_permissions;"
   ```

4. **Tests Hanging**
   ```bash
   # Run with timeout
   go test -timeout 5m ./tests/...
   ```

### Performance Issues

1. **Slow Tests**
   ```bash
   # Run in short mode
   make test-short
   
   # Run specific test suite
   make test-specific PATTERN=TestAuth
   ```

2. **Database Performance**
   ```bash
   # Use in-memory database for faster tests
   export DATABASE_URL="sqlite://file::memory:?cache=shared"
   ```

## 📝 Writing New Tests

### Test Structure
```go
func (suite *IntegrationTestSuite) TestNewFeature() {
    suite.T().Run("SubTest", func(t *testing.T) {
        // Test implementation
        w := suite.makeAuthenticatedRequest("GET", "/api/endpoint", nil)
        response := suite.assertResponseSuccess(w, http.StatusOK)
        
        // Assertions
        assert.Equal(t, expected, actual)
    })
}
```

### Helper Functions
- `suite.makeRequest()` - Make HTTP request
- `suite.makeAuthenticatedRequest()` - Make authenticated request
- `suite.assertResponseSuccess()` - Assert successful response
- `suite.assertResponseError()` - Assert error response
- `suite.createTestTranslationKey()` - Create test translation key
- `suite.createTestTranslation()` - Create test translation

### Best Practices
1. Use descriptive test names
2. Clean up test data in teardown
3. Use helper functions for common operations
4. Test both success and error cases
5. Include edge cases and boundary conditions

## 🤝 Contributing

When adding new tests:

1. Follow the existing test structure
2. Add tests to appropriate test files
3. Update this README if needed
4. Ensure tests pass in CI environment
5. Include both positive and negative test cases

## 📚 Additional Resources

- [Go Testing Documentation](https://golang.org/pkg/testing/)
- [Testify Documentation](https://github.com/stretchr/testify)
- [GORM Testing Guide](https://gorm.io/docs/testing.html)
- [Gin Testing Examples](https://github.com/gin-gonic/gin#testing)

package tests

import (
	"os"
	"testing"

	"adc-multi-languages/config"
	"adc-multi-languages/database"
	"adc-multi-languages/middleware"

	"github.com/stretchr/testify/assert"
	"golang.org/x/crypto/bcrypt"
)

// TestBasicFunctionality tests core functionality without complex setup
func TestBasicFunctionality(t *testing.T) {
	// Set minimal test environment
	os.Setenv("DATABASE_URL", "postgresql://postgres:<EMAIL>:5432/postgres")
	os.Setenv("JWT_SECRET", "test-secret-key-for-testing-only")
	os.Setenv("LOG_LEVEL", "error")

	t.Run("ConfigLoading", func(t *testing.T) {
		cfg, err := config.Load()
		assert.NoError(t, err)
		assert.NotNil(t, cfg)
		assert.NotEmpty(t, cfg.DatabaseURL)
		assert.NotEmpty(t, cfg.JWTSecret)
	})

	t.Run("DatabaseConnection", func(t *testing.T) {
		cfg, err := config.Load()
		assert.NoError(t, err)

		dbConfig := database.Config{
			URL: cfg.DatabaseURL,
		}
		err = database.Connect(dbConfig)
		assert.NoError(t, err)

		db := database.GetDB()
		assert.NotNil(t, db)

		// Test basic query
		sqlDB, err := db.DB()
		assert.NoError(t, err)
		assert.NoError(t, sqlDB.Ping())

		var result int
		err = db.Raw("SELECT 1").Scan(&result).Error
		assert.NoError(t, err)
		assert.Equal(t, 1, result)
	})

	t.Run("JWTTokens", func(t *testing.T) {
		authConfig := middleware.AuthConfig{
			JWTSecret:          "test-secret-key",
			JWTExpirationHours: 24,
		}

		userID := "123e4567-e89b-12d3-a456-426614174000"
		email := "<EMAIL>"
		orgID := "123e4567-e89b-12d3-a456-426614174001"

		// Test token generation
		token, err := middleware.GenerateJWT(authConfig, userID, email, orgID)
		assert.NoError(t, err)
		assert.NotEmpty(t, token)

		// Test token validation
		claims, err := middleware.ValidateToken(authConfig, token)
		assert.NoError(t, err)
		assert.Equal(t, userID, claims.UserID)
		assert.Equal(t, email, claims.Email)
		assert.Equal(t, orgID, claims.OrganizationID)
	})

	t.Run("PasswordHashing", func(t *testing.T) {
		password := "testpassword123"

		// Test password hashing
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
		assert.NoError(t, err)
		assert.NotEmpty(t, hashedPassword)

		// Test password verification
		err = bcrypt.CompareHashAndPassword(hashedPassword, []byte(password))
		assert.NoError(t, err)

		// Test wrong password
		err = bcrypt.CompareHashAndPassword(hashedPassword, []byte("wrongpassword"))
		assert.Error(t, err)
	})

	t.Run("InvalidJWTToken", func(t *testing.T) {
		authConfig := middleware.AuthConfig{
			JWTSecret: "test-secret-key",
		}

		// Test invalid token format
		_, err := middleware.ValidateToken(authConfig, "invalid.token.format")
		assert.Error(t, err)

		// Test empty token
		_, err = middleware.ValidateToken(authConfig, "")
		assert.Error(t, err)
	})
}

// TestDatabaseQueries tests basic database operations
func TestDatabaseQueries(t *testing.T) {
	// Set test environment
	os.Setenv("DATABASE_URL", "postgresql://postgres:<EMAIL>:5432/postgres")

	cfg, err := config.Load()
	assert.NoError(t, err)

	dbConfig := database.Config{
		URL: cfg.DatabaseURL,
	}
	err = database.Connect(dbConfig)
	assert.NoError(t, err)

	db := database.GetDB()
	assert.NotNil(t, db)

	t.Run("BasicQueries", func(t *testing.T) {
		// Test simple SELECT
		var count int64
		err := db.Raw("SELECT COUNT(*) FROM users").Scan(&count).Error
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, count, int64(0))

		// Test table existence
		var tableExists bool
		err = db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'users')").Scan(&tableExists).Error
		assert.NoError(t, err)
		assert.True(t, tableExists)
	})

	t.Run("DatabaseInfo", func(t *testing.T) {
		// Test database version
		var version string
		err := db.Raw("SELECT version()").Scan(&version).Error
		assert.NoError(t, err)
		assert.NotEmpty(t, version)
		assert.Contains(t, version, "PostgreSQL")

		// Test current database
		var dbName string
		err = db.Raw("SELECT current_database()").Scan(&dbName).Error
		assert.NoError(t, err)
		assert.Equal(t, "postgres", dbName)
	})

	t.Run("TableStructure", func(t *testing.T) {
		// Test that key tables exist
		tables := []string{"users", "organizations", "projects"}
		
		for _, table := range tables {
			var exists bool
			err := db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = ?)", table).Scan(&exists).Error
			assert.NoError(t, err, "Table %s should exist", table)
			assert.True(t, exists, "Table %s should exist", table)
		}
	})
}

// TestEnvironmentConfiguration tests environment-specific configuration
func TestEnvironmentConfiguration(t *testing.T) {
	t.Run("TestEnvironment", func(t *testing.T) {
		os.Setenv("APP_ENV", "test")
		os.Setenv("JWT_SECRET", "test-secret")
		os.Setenv("DATABASE_URL", "postgresql://postgres:<EMAIL>:5432/postgres")

		cfg, err := config.Load()
		assert.NoError(t, err)
		assert.NotNil(t, cfg)
		assert.NotEmpty(t, cfg.Env)
	})

	t.Run("RequiredEnvironmentVariables", func(t *testing.T) {
		// Test that required environment variables are set
		requiredVars := []string{"DATABASE_URL", "JWT_SECRET"}
		
		for _, varName := range requiredVars {
			value := os.Getenv(varName)
			assert.NotEmpty(t, value, "Environment variable %s should be set", varName)
		}
	})
}

// TestUtilityFunctions tests utility functions
func TestUtilityFunctions(t *testing.T) {
	t.Run("JWTRefreshToken", func(t *testing.T) {
		authConfig := middleware.AuthConfig{
			JWTSecret:                "test-secret-key",
			JWTRefreshExpirationDays: 30,
		}

		userID := "123e4567-e89b-12d3-a456-426614174000"

		// Test refresh token generation
		refreshToken, err := middleware.GenerateRefreshToken(authConfig, userID)
		assert.NoError(t, err)
		assert.NotEmpty(t, refreshToken)

		// Test refresh token validation
		claims, err := middleware.ValidateToken(authConfig, refreshToken)
		assert.NoError(t, err)
		assert.Equal(t, userID, claims.UserID)
	})

	t.Run("PasswordResetToken", func(t *testing.T) {
		authConfig := middleware.AuthConfig{
			JWTSecret: "test-secret-key",
		}

		userID := "123e4567-e89b-12d3-a456-426614174000"
		email := "<EMAIL>"

		// Test password reset token generation
		resetToken, err := middleware.GeneratePasswordResetToken(authConfig, userID, email)
		assert.NoError(t, err)
		assert.NotEmpty(t, resetToken)

		// Test token validation
		claims, err := middleware.ValidateToken(authConfig, resetToken)
		assert.NoError(t, err)
		assert.Equal(t, userID, claims.UserID)
		assert.Equal(t, email, claims.Email)
	})

	t.Run("EmailVerificationToken", func(t *testing.T) {
		authConfig := middleware.AuthConfig{
			JWTSecret: "test-secret-key",
		}

		userID := "123e4567-e89b-12d3-a456-426614174000"
		email := "<EMAIL>"

		// Test email verification token generation
		verificationToken, err := middleware.GenerateEmailVerificationToken(authConfig, userID, email)
		assert.NoError(t, err)
		assert.NotEmpty(t, verificationToken)

		// Test token validation
		claims, err := middleware.ValidateToken(authConfig, verificationToken)
		assert.NoError(t, err)
		assert.Equal(t, userID, claims.UserID)
		assert.Equal(t, email, claims.Email)
	})
}

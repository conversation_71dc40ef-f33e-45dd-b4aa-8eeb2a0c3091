package tests

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"adc-multi-languages/config"
	"adc-multi-languages/database"
	"adc-multi-languages/middleware"
	"adc-multi-languages/models"
	"adc-multi-languages/routes"
	"adc-multi-languages/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// OrganizationManagementTestSuite defines the organization management test suite
type OrganizationManagementTestSuite struct {
	suite.Suite
	router    *gin.Engine
	db        *gorm.DB
	config    *config.Config
	authToken string
	userID    string
}

// SetupSuite runs once before all tests
func (suite *OrganizationManagementTestSuite) SetupSuite() {
	// Set test environment
	os.Setenv("APP_ENV", "test")
	os.Setenv("DATABASE_URL", "postgresql://postgres:<EMAIL>:5432/postgres")
	os.Setenv("JWT_SECRET", "test-secret-key-for-testing-only")
	os.Setenv("JWT_REFRESH_SECRET", "test-refresh-secret-key")
	os.Setenv("LOG_LEVEL", "error")

	// Initialize logger
	utils.InitLogger("error")

	// Load configuration
	cfg, err := config.Load()
	suite.Require().NoError(err)
	suite.config = cfg

	// Connect to database
	dbConfig := database.Config{
		URL: cfg.DatabaseURL,
	}
	err = database.Connect(dbConfig)
	suite.Require().NoError(err)
	suite.db = database.GetDB()

	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Setup routes
	suite.router = routes.SetupRoutes(cfg)

	// Create test data
	suite.createTestData()
}

// TearDownSuite runs once after all tests
func (suite *OrganizationManagementTestSuite) TearDownSuite() {
	suite.cleanTestData()
}

// cleanTestData removes test-specific data
func (suite *OrganizationManagementTestSuite) cleanTestData() {
	// Clean in reverse dependency order
	suite.db.Unscoped().Where("organization_id IN (SELECT id FROM organizations WHERE name LIKE 'Org Test%')").Delete(&models.OrganizationMembership{})
	suite.db.Unscoped().Where("name LIKE 'Org Test%'").Delete(&models.Organization{})
	suite.db.Unscoped().Where("email LIKE '%@org-test.com'").Delete(&models.User{})
}

// createTestData creates initial test data
func (suite *OrganizationManagementTestSuite) createTestData() {
	// Create test user
	user := models.User{
		Email:     "<EMAIL>",
		FirstName: &[]string{"Org"}[0],
		LastName:  &[]string{"Manager"}[0],
	}
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("password123"), bcrypt.DefaultCost)
	user.PasswordHash = string(hashedPassword)
	user.EmailVerified = true

	err := suite.db.Create(&user).Error
	suite.Require().NoError(err)
	suite.userID = user.ID.String()

	// Generate auth token
	authConfig := middleware.AuthConfig{
		JWTSecret:          suite.config.JWTSecret,
		JWTExpirationHours: suite.config.JWTExpirationHours,
	}
	token, err := middleware.GenerateJWT(authConfig, user.ID.String(), user.Email, "")
	suite.Require().NoError(err)
	suite.authToken = token
}

// makeRequest makes an HTTP request
func (suite *OrganizationManagementTestSuite) makeRequest(method, url string, body interface{}, headers map[string]string) *httptest.ResponseRecorder {
	var reqBody []byte
	if body != nil {
		var err error
		reqBody, err = json.Marshal(body)
		suite.Require().NoError(err)
	}

	req, err := http.NewRequest(method, url, bytes.NewBuffer(reqBody))
	suite.Require().NoError(err)

	req.Header.Set("Content-Type", "application/json")
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)
	return w
}

// makeAuthenticatedRequest makes an authenticated HTTP request
func (suite *OrganizationManagementTestSuite) makeAuthenticatedRequest(method, url string, body interface{}) *httptest.ResponseRecorder {
	headers := map[string]string{
		"Authorization": "Bearer " + suite.authToken,
	}
	return suite.makeRequest(method, url, body, headers)
}

// TestOrganizationCreation tests creating organizations
func (suite *OrganizationManagementTestSuite) TestOrganizationCreation() {
	orgData := map[string]interface{}{
		"name":        "Org Test New Organization",
		"description": "A new test organization",
		"slug":        "org-test-new-org",
		"owner_id":    suite.userID,
	}

	w := suite.makeAuthenticatedRequest("POST", "/api/organizations", orgData)

	// Should succeed or return validation error
	assert.True(suite.T(), w.Code == http.StatusCreated || w.Code == http.StatusBadRequest || w.Code == http.StatusConflict)

	if w.Code == http.StatusCreated {
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(suite.T(), err)
		assert.Equal(suite.T(), true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Equal(suite.T(), "Org Test New Organization", data["name"])
		assert.Contains(suite.T(), data, "id")
		assert.Contains(suite.T(), data, "slug")
	}
}

// TestOrganizationListing tests listing organizations
func (suite *OrganizationManagementTestSuite) TestOrganizationListing() {
	w := suite.makeAuthenticatedRequest("GET", "/api/organizations", nil)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), true, response["success"])

	data := response["data"].([]interface{})
	assert.GreaterOrEqual(suite.T(), len(data), 0)
}

// TestOrganizationValidation tests organization validation
func (suite *OrganizationManagementTestSuite) TestOrganizationValidation() {
	// Test missing name
	orgData := map[string]interface{}{
		"description": "Organization without name",
		"slug":        "org-test-no-name",
	}

	w := suite.makeAuthenticatedRequest("POST", "/api/organizations", orgData)
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)

	// Test invalid slug format
	orgData = map[string]interface{}{
		"name":        "Org Test Invalid Slug",
		"description": "Organization with invalid slug",
		"slug":        "Invalid Slug With Spaces",
	}

	w = suite.makeAuthenticatedRequest("POST", "/api/organizations", orgData)
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)
}

// TestOrganizationUpdate tests updating organizations
func (suite *OrganizationManagementTestSuite) TestOrganizationUpdate() {
	// First create an organization
	orgData := map[string]interface{}{
		"name":        "Org Test Update Original",
		"description": "Original description",
		"slug":        "org-test-update-original",
	}

	w := suite.makeAuthenticatedRequest("POST", "/api/organizations", orgData)

	if w.Code == http.StatusCreated {
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(suite.T(), err)

		data := response["data"].(map[string]interface{})
		orgID := data["id"].(string)

		// Test updating the organization
		updateData := map[string]interface{}{
			"name":        "Org Test Update Modified",
			"description": "Updated description",
		}

		url := fmt.Sprintf("/api/organizations/%s", orgID)
		w = suite.makeAuthenticatedRequest("PUT", url, updateData)

		// Should succeed
		assert.True(suite.T(), w.Code == http.StatusOK || w.Code == http.StatusNotFound)

		if w.Code == http.StatusOK {
			var updateResponse map[string]interface{}
			err = json.Unmarshal(w.Body.Bytes(), &updateResponse)
			assert.NoError(suite.T(), err)
			assert.Equal(suite.T(), true, updateResponse["success"])

			updatedData := updateResponse["data"].(map[string]interface{})
			assert.Equal(suite.T(), "Org Test Update Modified", updatedData["name"])
		}
	}
}

// TestOrganizationDetails tests getting organization details
func (suite *OrganizationManagementTestSuite) TestOrganizationDetails() {
	// First create an organization
	orgData := map[string]interface{}{
		"name":        "Org Test Details",
		"description": "Organization for details testing",
		"slug":        "org-test-details",
	}

	w := suite.makeAuthenticatedRequest("POST", "/api/organizations", orgData)

	if w.Code == http.StatusCreated {
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(suite.T(), err)

		data := response["data"].(map[string]interface{})
		orgID := data["id"].(string)

		// Test getting organization details
		url := fmt.Sprintf("/api/organizations/%s", orgID)
		w = suite.makeAuthenticatedRequest("GET", url, nil)

		// Should succeed
		assert.True(suite.T(), w.Code == http.StatusOK || w.Code == http.StatusNotFound)

		if w.Code == http.StatusOK {
			var detailsResponse map[string]interface{}
			err = json.Unmarshal(w.Body.Bytes(), &detailsResponse)
			assert.NoError(suite.T(), err)
			assert.Equal(suite.T(), true, detailsResponse["success"])

			orgDetails := detailsResponse["data"].(map[string]interface{})
			assert.Equal(suite.T(), "Org Test Details", orgDetails["name"])
			assert.Equal(suite.T(), orgID, orgDetails["id"])
		}
	}
}

// TestOrganizationMembership tests organization membership management
func (suite *OrganizationManagementTestSuite) TestOrganizationMembership() {
	// First create an organization
	orgData := map[string]interface{}{
		"name":        "Org Test Membership",
		"description": "Organization for membership testing",
		"slug":        "org-test-membership",
	}

	w := suite.makeAuthenticatedRequest("POST", "/api/organizations", orgData)

	if w.Code == http.StatusCreated {
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(suite.T(), err)

		data := response["data"].(map[string]interface{})
		orgID := data["id"].(string)

		// Test getting organization members
		url := fmt.Sprintf("/api/organizations/%s/members", orgID)
		w = suite.makeAuthenticatedRequest("GET", url, nil)

		// Should succeed
		assert.True(suite.T(), w.Code == http.StatusOK || w.Code == http.StatusNotFound)

		if w.Code == http.StatusOK {
			var membersResponse map[string]interface{}
			err = json.Unmarshal(w.Body.Bytes(), &membersResponse)
			assert.NoError(suite.T(), err)
			assert.Equal(suite.T(), true, membersResponse["success"])

			members := membersResponse["data"].([]interface{})
			assert.GreaterOrEqual(suite.T(), len(members), 0)
		}
	}
}

// TestOrganizationPermissions tests organization access permissions
func (suite *OrganizationManagementTestSuite) TestOrganizationPermissions() {
	// Test unauthorized access
	w := suite.makeRequest("GET", "/api/organizations", nil, nil)
	assert.Equal(suite.T(), http.StatusUnauthorized, w.Code)

	// Test access with invalid organization ID
	w = suite.makeAuthenticatedRequest("GET", "/api/organizations/invalid-id", nil)
	assert.True(suite.T(), w.Code == http.StatusBadRequest || w.Code == http.StatusNotFound)
}

// TestOrganizationDeletion tests deleting organizations
func (suite *OrganizationManagementTestSuite) TestOrganizationDeletion() {
	// First create an organization to delete
	orgData := map[string]interface{}{
		"name":        "Org Test Delete Me",
		"description": "Organization for deletion testing",
		"slug":        "org-test-delete-me",
	}

	w := suite.makeAuthenticatedRequest("POST", "/api/organizations", orgData)

	if w.Code == http.StatusCreated {
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(suite.T(), err)

		data := response["data"].(map[string]interface{})
		orgID := data["id"].(string)

		// Test deleting the organization
		url := fmt.Sprintf("/api/organizations/%s", orgID)
		w = suite.makeAuthenticatedRequest("DELETE", url, nil)

		// Should succeed
		assert.True(suite.T(), w.Code == http.StatusOK || w.Code == http.StatusNotFound)
	}
}

// TestDuplicateOrganizationSlug tests creating organizations with duplicate slugs
func (suite *OrganizationManagementTestSuite) TestDuplicateOrganizationSlug() {
	// Create first organization
	orgData := map[string]interface{}{
		"name":        "Org Test Duplicate First",
		"description": "First organization",
		"slug":        "org-test-duplicate-slug",
	}

	w := suite.makeAuthenticatedRequest("POST", "/api/organizations", orgData)

	if w.Code == http.StatusCreated {
		// Try to create second organization with same slug
		orgData2 := map[string]interface{}{
			"name":        "Org Test Duplicate Second",
			"description": "Second organization with same slug",
			"slug":        "org-test-duplicate-slug",
		}

		w2 := suite.makeAuthenticatedRequest("POST", "/api/organizations", orgData2)
		assert.Equal(suite.T(), http.StatusConflict, w2.Code)
	}
}

// Run the organization management test suite
func TestOrganizationManagementTestSuite(t *testing.T) {
	suite.Run(t, new(OrganizationManagementTestSuite))
}

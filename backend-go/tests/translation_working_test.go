package tests

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"adc-multi-languages/config"
	"adc-multi-languages/database"
	"adc-multi-languages/middleware"
	"adc-multi-languages/models"
	"adc-multi-languages/routes"
	"adc-multi-languages/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// TranslationTestSuite defines the translation test suite
type TranslationTestSuite struct {
	suite.Suite
	router    *gin.Engine
	db        *gorm.DB
	config    *config.Config
	authToken string
	userID    string
	orgID     string
	projectID string
}

// SetupSuite runs once before all tests
func (suite *TranslationTestSuite) SetupSuite() {
	// Set test environment
	os.Setenv("APP_ENV", "test")
	os.Setenv("DATABASE_URL", "postgresql://postgres:<EMAIL>:5432/postgres")
	os.Setenv("JWT_SECRET", "test-secret-key-for-testing-only")
	os.Setenv("JWT_REFRESH_SECRET", "test-refresh-secret-key")
	os.Setenv("LOG_LEVEL", "error")

	// Initialize logger
	utils.InitLogger("error")

	// Load configuration
	cfg, err := config.Load()
	suite.Require().NoError(err)
	suite.config = cfg

	// Connect to database
	dbConfig := database.Config{
		URL: cfg.DatabaseURL,
	}
	err = database.Connect(dbConfig)
	suite.Require().NoError(err)
	suite.db = database.GetDB()

	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Setup routes
	suite.router = routes.SetupRoutes(cfg)

	// Create test data
	suite.createTestData()
}

// TearDownSuite runs once after all tests
func (suite *TranslationTestSuite) TearDownSuite() {
	suite.cleanTestData()
}

// cleanTestData removes test-specific data
func (suite *TranslationTestSuite) cleanTestData() {
	// Clean in reverse dependency order
	suite.db.Unscoped().Where("translation_id IN (SELECT id FROM translations WHERE key_id IN (SELECT id FROM translation_keys WHERE project_id IN (SELECT id FROM projects WHERE name LIKE 'Translation Test%')))").Delete(&models.TranslationHistory{})
	suite.db.Unscoped().Where("key_id IN (SELECT id FROM translation_keys WHERE project_id IN (SELECT id FROM projects WHERE name LIKE 'Translation Test%'))").Delete(&models.Translation{})
	suite.db.Unscoped().Where("project_id IN (SELECT id FROM projects WHERE name LIKE 'Translation Test%')").Delete(&models.TranslationKey{})
	suite.db.Unscoped().Where("name LIKE 'Translation Test%'").Delete(&models.Project{})
	suite.db.Unscoped().Where("organization_id IN (SELECT id FROM organizations WHERE name LIKE 'Translation Test%')").Delete(&models.OrganizationMembership{})
	suite.db.Unscoped().Where("name LIKE 'Translation Test%'").Delete(&models.Organization{})
	suite.db.Unscoped().Where("email LIKE '%@translation-test.com'").Delete(&models.User{})
}

// createTestData creates initial test data
func (suite *TranslationTestSuite) createTestData() {
	// Create test user
	user := models.User{
		Email:     "<EMAIL>",
		FirstName: &[]string{"Translation"}[0],
		LastName:  &[]string{"Tester"}[0],
	}
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("password123"), bcrypt.DefaultCost)
	user.PasswordHash = string(hashedPassword)
	user.EmailVerified = true

	err := suite.db.Create(&user).Error
	suite.Require().NoError(err)
	suite.userID = user.ID.String()

	// Create test organization
	org := models.Organization{
		Name:               "Translation Test Organization",
		Slug:               "translation-test-org",
		OwnerID:            user.ID, // Set the owner_id
		SubscriptionTier:   "pro",
		AICreditsRemaining: &[]int{5000}[0],
	}
	err = suite.db.Create(&org).Error
	suite.Require().NoError(err)
	suite.orgID = org.ID.String()

	// Create organization membership
	membership := models.OrganizationMembership{
		OrganizationID: org.ID,
		UserID:         user.ID,
		Role:           "admin",
		IsActive:       true,
	}
	err = suite.db.Create(&membership).Error
	suite.Require().NoError(err)

	// Create test project
	project := models.Project{
		OrganizationID: org.ID,
		Name:           "Translation Test Project",
		Slug:           "translation-test-project",
		DefaultLocale:  &[]string{"en"}[0],
	}
	err = suite.db.Create(&project).Error
	suite.Require().NoError(err)
	suite.projectID = project.ID.String()

	// Create test locales
	locales := []models.Locale{
		{Code: "en", Name: "English", NativeName: "English", Direction: "ltr", IsActive: true},
		{Code: "es", Name: "Spanish", NativeName: "Español", Direction: "ltr", IsActive: true},
		{Code: "fr", Name: "French", NativeName: "Français", Direction: "ltr", IsActive: true},
	}
	for _, locale := range locales {
		// Check if locale already exists
		var existingLocale models.Locale
		err = suite.db.Where("code = ?", locale.Code).First(&existingLocale).Error
		if err == gorm.ErrRecordNotFound {
			err = suite.db.Create(&locale).Error
			suite.Require().NoError(err)
		}
	}

	// Generate auth token
	authConfig := middleware.AuthConfig{
		JWTSecret:          suite.config.JWTSecret,
		JWTExpirationHours: suite.config.JWTExpirationHours,
	}
	token, err := middleware.GenerateJWT(authConfig, user.ID.String(), user.Email, org.ID.String())
	suite.Require().NoError(err)
	suite.authToken = token
}

// makeRequest makes an HTTP request
func (suite *TranslationTestSuite) makeRequest(method, url string, body interface{}, headers map[string]string) *httptest.ResponseRecorder {
	var reqBody []byte
	if body != nil {
		var err error
		reqBody, err = json.Marshal(body)
		suite.Require().NoError(err)
	}

	req, err := http.NewRequest(method, url, bytes.NewBuffer(reqBody))
	suite.Require().NoError(err)

	req.Header.Set("Content-Type", "application/json")
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)
	return w
}

// makeAuthenticatedRequest makes an authenticated HTTP request
func (suite *TranslationTestSuite) makeAuthenticatedRequest(method, url string, body interface{}) *httptest.ResponseRecorder {
	headers := map[string]string{
		"Authorization": "Bearer " + suite.authToken,
	}
	return suite.makeRequest(method, url, body, headers)
}

// TestTranslationKeyManagement tests translation key CRUD operations
func (suite *TranslationTestSuite) TestTranslationKeyManagement() {
	// Test creating a translation key
	keyData := map[string]interface{}{
		"key_name":    "welcome_message",
		"description": "Welcome message for users",
		"context":     "Homepage greeting",
		"project_id":  suite.projectID,
	}

	url := fmt.Sprintf("/api/projects/%s/keys", suite.projectID)
	w := suite.makeAuthenticatedRequest("POST", url, keyData)

	// Should succeed or return validation error
	assert.True(suite.T(), w.Code == http.StatusCreated || w.Code == http.StatusBadRequest)

	if w.Code == http.StatusCreated {
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(suite.T(), err)
		assert.Equal(suite.T(), true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Equal(suite.T(), "welcome_message", data["key_name"])
		assert.Contains(suite.T(), data, "id")
	}
}

// TestTranslationKeyListing tests listing translation keys
func (suite *TranslationTestSuite) TestTranslationKeyListing() {
	url := fmt.Sprintf("/api/projects/%s/keys", suite.projectID)
	w := suite.makeAuthenticatedRequest("GET", url, nil)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), true, response["success"])

	data := response["data"].([]interface{})
	assert.GreaterOrEqual(suite.T(), len(data), 0)
}

// TestTranslationManagement tests translation CRUD operations
func (suite *TranslationTestSuite) TestTranslationManagement() {
	// Test creating a translation
	translationData := map[string]interface{}{
		"key_name":   "test_key",
		"locale":     "en",
		"content":    "Hello, World!",
		"project_id": suite.projectID,
	}

	w := suite.makeAuthenticatedRequest("POST", "/api/translations", translationData)

	// Should succeed or return validation error
	assert.True(suite.T(), w.Code == http.StatusCreated || w.Code == http.StatusBadRequest)

	if w.Code == http.StatusCreated {
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(suite.T(), err)
		assert.Equal(suite.T(), true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Equal(suite.T(), "Hello, World!", data["content"])
		assert.Equal(suite.T(), "en", data["locale"])
	}
}

// TestTranslationValidation tests translation validation
func (suite *TranslationTestSuite) TestTranslationValidation() {
	// Test missing content
	translationData := map[string]interface{}{
		"key_name":   "test_key",
		"locale":     "en",
		"project_id": suite.projectID,
		// Missing content
	}

	w := suite.makeAuthenticatedRequest("POST", "/api/translations", translationData)
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)

	// Test invalid locale
	translationData = map[string]interface{}{
		"key_name":   "test_key",
		"locale":     "invalid_locale",
		"content":    "Test content",
		"project_id": suite.projectID,
	}

	w = suite.makeAuthenticatedRequest("POST", "/api/translations", translationData)
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)
}

// TestProjectTranslations tests getting translations for a project
func (suite *TranslationTestSuite) TestProjectTranslations() {
	url := fmt.Sprintf("/api/projects/%s/translations", suite.projectID)
	w := suite.makeAuthenticatedRequest("GET", url, nil)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), true, response["success"])

	data := response["data"].([]interface{})
	assert.GreaterOrEqual(suite.T(), len(data), 0)
}

// TestTranslationSearch tests translation search functionality
func (suite *TranslationTestSuite) TestTranslationSearch() {
	url := fmt.Sprintf("/api/projects/%s/search?q=test&locale=en", suite.projectID)
	w := suite.makeAuthenticatedRequest("GET", url, nil)

	// Should succeed
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), true, response["success"])

	data := response["data"].([]interface{})
	assert.GreaterOrEqual(suite.T(), len(data), 0)
}

// TestLocaleManagement tests locale operations
func (suite *TranslationTestSuite) TestLocaleManagement() {
	// Test listing locales
	w := suite.makeAuthenticatedRequest("GET", "/api/locales", nil)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), true, response["success"])

	data := response["data"].([]interface{})
	assert.GreaterOrEqual(suite.T(), len(data), 3) // We created 3 locales
}

// TestUnauthorizedTranslationAccess tests unauthorized access
func (suite *TranslationTestSuite) TestUnauthorizedTranslationAccess() {
	// Test without authentication
	url := fmt.Sprintf("/api/projects/%s/translations", suite.projectID)
	w := suite.makeRequest("GET", url, nil, nil)
	assert.Equal(suite.T(), http.StatusUnauthorized, w.Code)

	// Test with invalid project ID
	w = suite.makeAuthenticatedRequest("GET", "/api/projects/invalid-id/translations", nil)
	assert.True(suite.T(), w.Code == http.StatusBadRequest || w.Code == http.StatusNotFound)
}

// Run the translation test suite
func TestTranslationTestSuite(t *testing.T) {
	suite.Run(t, new(TranslationTestSuite))
}

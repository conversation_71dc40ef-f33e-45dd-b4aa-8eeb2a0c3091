package tests

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"adc-multi-languages/config"
	"adc-multi-languages/database"
	"adc-multi-languages/middleware"
	"adc-multi-languages/models"
	"adc-multi-languages/routes"
	"adc-multi-languages/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// APIKeysTestSuite defines the API keys test suite
type APIKeysTestSuite struct {
	suite.Suite
	router    *gin.Engine
	db        *gorm.DB
	config    *config.Config
	authToken string
	userID    string
	orgID     string
}

// SetupSuite runs once before all tests
func (suite *APIKeysTestSuite) SetupSuite() {
	// Set test environment
	os.Setenv("APP_ENV", "test")
	os.Setenv("DATABASE_URL", "postgresql://postgres:<EMAIL>:5432/postgres")
	os.Setenv("JWT_SECRET", "test-secret-key-for-testing-only")
	os.Setenv("JWT_REFRESH_SECRET", "test-refresh-secret-key")
	os.Setenv("LOG_LEVEL", "error")

	// Initialize logger
	utils.InitLogger("error")

	// Load configuration
	cfg, err := config.Load()
	suite.Require().NoError(err)
	suite.config = cfg

	// Connect to database
	dbConfig := database.Config{
		URL: cfg.DatabaseURL,
	}
	err = database.Connect(dbConfig)
	suite.Require().NoError(err)
	suite.db = database.GetDB()

	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Setup routes
	suite.router = routes.SetupRoutes(cfg)

	// Create test data
	suite.createTestData()
}

// TearDownSuite runs once after all tests
func (suite *APIKeysTestSuite) TearDownSuite() {
	suite.cleanTestData()
}

// cleanTestData removes test-specific data
func (suite *APIKeysTestSuite) cleanTestData() {
	// Clean API keys first (foreign key dependency)
	suite.db.Unscoped().Where("organization_id IN (SELECT id FROM organizations WHERE name LIKE 'API Test%')").Delete(&models.APIKey{})

	// Clean organization memberships
	suite.db.Unscoped().Where("organization_id IN (SELECT id FROM organizations WHERE name LIKE 'API Test%')").Delete(&models.OrganizationMembership{})

	// Clean organizations
	suite.db.Unscoped().Where("name LIKE 'API Test%'").Delete(&models.Organization{})

	// Clean users
	suite.db.Unscoped().Where("email LIKE '%@api-test.com'").Delete(&models.User{})
}

// createTestData creates initial test data
func (suite *APIKeysTestSuite) createTestData() {
	// Create test user
	user := models.User{
		Email:     "<EMAIL>",
		FirstName: &[]string{"API"}[0],
		LastName:  &[]string{"Tester"}[0],
	}
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("password123"), bcrypt.DefaultCost)
	user.PasswordHash = string(hashedPassword)
	user.EmailVerified = true

	err := suite.db.Create(&user).Error
	suite.Require().NoError(err)
	suite.userID = user.ID.String()

	// Create test organization
	org := models.Organization{
		Name:               "API Test Organization",
		Slug:               "api-test-org",
		OwnerID:            user.ID, // Set the owner_id
		SubscriptionTier:   "pro",
		AICreditsRemaining: &[]int{5000}[0],
	}
	err = suite.db.Create(&org).Error
	suite.Require().NoError(err)
	suite.orgID = org.ID.String()

	// Create organization membership
	membership := models.OrganizationMembership{
		OrganizationID: org.ID,
		UserID:         user.ID,
		Role:           "admin",
		IsActive:       true,
	}
	err = suite.db.Create(&membership).Error
	suite.Require().NoError(err)

	// Generate auth token
	authConfig := middleware.AuthConfig{
		JWTSecret:          suite.config.JWTSecret,
		JWTExpirationHours: suite.config.JWTExpirationHours,
	}
	token, err := middleware.GenerateJWT(authConfig, user.ID.String(), user.Email, org.ID.String())
	suite.Require().NoError(err)
	suite.authToken = token
}

// makeRequest makes an HTTP request
func (suite *APIKeysTestSuite) makeRequest(method, url string, body interface{}, headers map[string]string) *httptest.ResponseRecorder {
	var reqBody []byte
	if body != nil {
		var err error
		reqBody, err = json.Marshal(body)
		suite.Require().NoError(err)
	}

	req, err := http.NewRequest(method, url, bytes.NewBuffer(reqBody))
	suite.Require().NoError(err)

	req.Header.Set("Content-Type", "application/json")
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)
	return w
}

// makeAuthenticatedRequest makes an authenticated HTTP request
func (suite *APIKeysTestSuite) makeAuthenticatedRequest(method, url string, body interface{}) *httptest.ResponseRecorder {
	headers := map[string]string{
		"Authorization": "Bearer " + suite.authToken,
	}
	return suite.makeRequest(method, url, body, headers)
}

// TestAPIKeyCreation tests creating API keys
func (suite *APIKeysTestSuite) TestAPIKeyCreation() {
	apiKeyData := map[string]interface{}{
		"name":        "Test API Key",
		"description": "API key for testing purposes",
		"permissions": []string{"read", "write"},
	}

	url := fmt.Sprintf("/api/organizations/%s/api-keys", suite.orgID)
	w := suite.makeAuthenticatedRequest("POST", url, apiKeyData)

	// Should succeed or return a validation error
	assert.True(suite.T(), w.Code == http.StatusCreated || w.Code == http.StatusBadRequest)

	if w.Code == http.StatusCreated {
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(suite.T(), err)
		assert.Equal(suite.T(), true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Equal(suite.T(), "Test API Key", data["name"])
		assert.Contains(suite.T(), data, "key")
		assert.Contains(suite.T(), data, "id")
	}
}

// TestAPIKeyListing tests listing API keys
func (suite *APIKeysTestSuite) TestAPIKeyListing() {
	url := fmt.Sprintf("/api/organizations/%s/api-keys", suite.orgID)
	w := suite.makeAuthenticatedRequest("GET", url, nil)

	// Should succeed
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), true, response["success"])

	data := response["data"].([]interface{})
	assert.GreaterOrEqual(suite.T(), len(data), 0)
}

// TestAPIKeyValidation tests API key validation
func (suite *APIKeysTestSuite) TestAPIKeyValidation() {
	// Test missing name
	apiKeyData := map[string]interface{}{
		"description": "API key without name",
		"permissions": []string{"read"},
	}

	url := fmt.Sprintf("/api/organizations/%s/api-keys", suite.orgID)
	w := suite.makeAuthenticatedRequest("POST", url, apiKeyData)

	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)

	// Test invalid permissions
	apiKeyData = map[string]interface{}{
		"name":        "Invalid Permissions Key",
		"description": "API key with invalid permissions",
		"permissions": []string{"invalid_permission"},
	}

	w = suite.makeAuthenticatedRequest("POST", url, apiKeyData)
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)
}

// TestAPIKeyUsage tests API key usage for authentication
func (suite *APIKeysTestSuite) TestAPIKeyUsage() {
	// First create an API key
	apiKeyData := map[string]interface{}{
		"name":        "Usage Test Key",
		"description": "API key for usage testing",
		"permissions": []string{"read"},
	}

	url := fmt.Sprintf("/api/organizations/%s/api-keys", suite.orgID)
	w := suite.makeAuthenticatedRequest("POST", url, apiKeyData)

	if w.Code == http.StatusCreated {
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(suite.T(), err)

		data := response["data"].(map[string]interface{})
		apiKey := data["key"].(string)

		// Test using the API key for authentication
		headers := map[string]string{
			"X-API-Key": apiKey,
		}

		// Try to access a protected endpoint with the API key
		w = suite.makeRequest("GET", "/api/organizations", nil, headers)

		// Should either work or return unauthorized (depending on implementation)
		assert.True(suite.T(), w.Code == http.StatusOK || w.Code == http.StatusUnauthorized)
	}
}

// TestAPIKeyDeletion tests deleting API keys
func (suite *APIKeysTestSuite) TestAPIKeyDeletion() {
	// First create an API key to delete
	apiKeyData := map[string]interface{}{
		"name":        "Delete Test Key",
		"description": "API key for deletion testing",
		"permissions": []string{"read"},
	}

	url := fmt.Sprintf("/api/organizations/%s/api-keys", suite.orgID)
	w := suite.makeAuthenticatedRequest("POST", url, apiKeyData)

	if w.Code == http.StatusCreated {
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(suite.T(), err)

		data := response["data"].(map[string]interface{})
		keyID := data["id"].(string)

		// Test deleting the API key
		deleteURL := fmt.Sprintf("/api/organizations/%s/api-keys/%s", suite.orgID, keyID)
		w = suite.makeAuthenticatedRequest("DELETE", deleteURL, nil)

		// Should succeed
		assert.Equal(suite.T(), http.StatusOK, w.Code)
	}
}

// TestAPIKeyPermissions tests API key permission validation
func (suite *APIKeysTestSuite) TestAPIKeyPermissions() {
	// Test unauthorized access to API keys
	w := suite.makeRequest("GET", fmt.Sprintf("/api/organizations/%s/api-keys", suite.orgID), nil, nil)
	assert.Equal(suite.T(), http.StatusUnauthorized, w.Code)

	// Test access with invalid organization ID
	w = suite.makeAuthenticatedRequest("GET", "/api/organizations/invalid-id/api-keys", nil)
	assert.True(suite.T(), w.Code == http.StatusBadRequest || w.Code == http.StatusNotFound)
}

// Run the API keys test suite
func TestAPIKeysTestSuite(t *testing.T) {
	suite.Run(t, new(APIKeysTestSuite))
}

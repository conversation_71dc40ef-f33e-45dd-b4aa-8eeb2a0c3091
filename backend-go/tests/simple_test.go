package tests

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"adc-multi-languages/config"
	"adc-multi-languages/database"
	"adc-multi-languages/routes"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// TestSimpleAPI tests basic API functionality without complex database operations
func TestSimpleAPI(t *testing.T) {
	// Set test environment
	os.Setenv("APP_ENV", "test")
	os.Setenv("DATABASE_URL", "postgresql://postgres:<EMAIL>:5432/postgres")
	os.Setenv("JWT_SECRET", "test-secret-key-for-testing-only")
	os.Setenv("LOG_LEVEL", "error")

	// Load configuration
	cfg, err := config.Load()
	assert.NoError(t, err)

	// Initialize database connection (but don't run migrations)
	dbConfig := database.Config{
		URL: cfg.DatabaseURL,
	}
	err = database.Connect(dbConfig)
	assert.NoError(t, err)

	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create router
	router := routes.SetupRoutes(cfg)

	t.Run("HealthCheck", func(t *testing.T) {
		// Test health check endpoint
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/health", nil)
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, "ok", response["status"])
	})

	t.Run("InvalidEndpoint", func(t *testing.T) {
		// Test non-existent endpoint
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/nonexistent", nil)
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("AuthWithoutToken", func(t *testing.T) {
		// Test protected endpoint without token
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/api/users/me", nil)
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("InvalidJSONRequest", func(t *testing.T) {
		// Test endpoint with invalid JSON
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("POST", "/api/auth/signup", bytes.NewBufferString("invalid json"))
		req.Header.Set("Content-Type", "application/json")
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("SignupValidation", func(t *testing.T) {
		// Test signup with missing fields
		signupData := map[string]interface{}{
			"email": "<EMAIL>",
			// Missing password
		}

		jsonData, _ := json.Marshal(signupData)
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("POST", "/api/auth/signup", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, false, response["success"])
	})

	t.Run("SignupInvalidEmail", func(t *testing.T) {
		// Test signup with invalid email format
		signupData := map[string]interface{}{
			"email":      "invalid-email",
			"password":   "password123",
			"first_name": "Test",
			"last_name":  "User",
		}

		jsonData, _ := json.Marshal(signupData)
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("POST", "/api/auth/signup", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("SigninWithoutCredentials", func(t *testing.T) {
		// Test signin without credentials
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("POST", "/api/auth/signin", bytes.NewBufferString("{}"))
		req.Header.Set("Content-Type", "application/json")
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("SigninInvalidCredentials", func(t *testing.T) {
		// Test signin with invalid credentials
		signinData := map[string]interface{}{
			"email":    "<EMAIL>",
			"password": "wrongpassword",
		}

		jsonData, _ := json.Marshal(signinData)
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("POST", "/api/auth/signin", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, false, response["success"])
	})

	t.Run("CORSHeaders", func(t *testing.T) {
		// Test CORS headers
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("OPTIONS", "/api/auth/signup", nil)
		req.Header.Set("Origin", "http://localhost:3000")
		router.ServeHTTP(w, req)

		// Should have CORS headers
		assert.NotEmpty(t, w.Header().Get("Access-Control-Allow-Origin"))
	})

	t.Run("ContentTypeValidation", func(t *testing.T) {
		// Test request without proper content type
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("POST", "/api/auth/signup", bytes.NewBufferString(`{"email":"<EMAIL>"}`))
		// Don't set Content-Type header
		router.ServeHTTP(w, req)

		// Should handle missing content type gracefully
		assert.NotEqual(t, http.StatusInternalServerError, w.Code)
	})
}

// TestDatabaseConnection tests basic database connectivity
func TestDatabaseConnection(t *testing.T) {
	// Set test environment
	os.Setenv("DATABASE_URL", "postgresql://postgres:<EMAIL>:5432/postgres")

	// Load configuration
	cfg, err := config.Load()
	assert.NoError(t, err)

	// Test database connection
	dbConfig := database.Config{
		URL: cfg.DatabaseURL,
	}
	err = database.Connect(dbConfig)
	assert.NoError(t, err)

	// Test that we can get the database instance
	db := database.GetDB()
	assert.NotNil(t, db)

	// Test basic query
	sqlDB, err := db.DB()
	assert.NoError(t, err)

	err = sqlDB.Ping()
	assert.NoError(t, err)

	// Test a simple query
	var result int
	err = db.Raw("SELECT 1").Scan(&result).Error
	assert.NoError(t, err)
	assert.Equal(t, 1, result)
}

// TestConfigurationLoading tests configuration loading
func TestConfigurationLoading(t *testing.T) {
	// Test with minimal environment variables
	os.Setenv("DATABASE_URL", "postgresql://postgres:<EMAIL>:5432/postgres")
	os.Setenv("JWT_SECRET", "test-secret")

	cfg, err := config.Load()
	assert.NoError(t, err)
	assert.NotNil(t, cfg)
	assert.NotEmpty(t, cfg.DatabaseURL)
	assert.NotEmpty(t, cfg.JWTSecret)
}

// TestRouterSetup tests that the router can be set up without errors
func TestRouterSetup(t *testing.T) {
	// Set minimal environment
	os.Setenv("DATABASE_URL", "postgresql://postgres:<EMAIL>:5432/postgres")
	os.Setenv("JWT_SECRET", "test-secret")

	// Load config and connect to database
	cfg, err := config.Load()
	assert.NoError(t, err)

	dbConfig := database.Config{
		URL: cfg.DatabaseURL,
	}
	err = database.Connect(dbConfig)
	assert.NoError(t, err)

	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Test that router setup doesn't panic
	assert.NotPanics(t, func() {
		router := routes.SetupRoutes(cfg)
		assert.NotNil(t, router)
	})
}

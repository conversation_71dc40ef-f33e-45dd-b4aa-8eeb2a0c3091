package tests

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestTranslationKeyManagement tests translation key CRUD operations
func (suite *IntegrationTestSuite) TestTranslationKeyManagement() {
	// Test creating a translation key
	suite.T().Run("CreateTranslationKey", func(t *testing.T) {
		keyData := map[string]interface{}{
			"key_name":    "welcome_message",
			"description": "Welcome message for users",
			"context":     "Homepage greeting",
			"project_id":  suite.projectID,
		}

		url := fmt.Sprintf("/api/projects/%s/keys", suite.projectID)
		w := suite.makeAuthenticatedRequest("POST", url, keyData)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "welcome_message", data["key_name"])
		assert.Equal(t, "Welcome message for users", data["description"])
		assert.Contains(t, data, "id")
	})

	// Test listing translation keys
	suite.T().Run("ListTranslationKeys", func(t *testing.T) {
		url := fmt.Sprintf("/api/projects/%s/keys", suite.projectID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 1)
	})

	// Test creating duplicate key (should fail)
	suite.T().Run("CreateDuplicateTranslationKey", func(t *testing.T) {
		keyData := map[string]interface{}{
			"key_name":    "welcome_message", // Same as above
			"description": "Duplicate key",
			"project_id":  suite.projectID,
		}

		url := fmt.Sprintf("/api/projects/%s/keys", suite.projectID)
		w := suite.makeAuthenticatedRequest("POST", url, keyData)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, false, response["success"])
	})
}

// TestTranslationManagement tests translation CRUD operations
func (suite *IntegrationTestSuite) TestTranslationManagement() {
	// First create a translation key
	keyID := suite.createTestTranslationKey(suite.projectID, "test_key", "Test key for translations")

	// Test creating a translation
	suite.T().Run("CreateTranslation", func(t *testing.T) {
		translationData := map[string]interface{}{
			"key_id":  keyID,
			"locale":  "en",
			"content": "Hello, World!",
		}

		w := suite.makeAuthenticatedRequest("POST", "/api/translations", translationData)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "Hello, World!", data["content"])
		assert.Equal(t, "en", data["locale"])
		assert.Contains(t, data, "id")
	})

	// Test creating translation for another locale
	suite.T().Run("CreateTranslationSpanish", func(t *testing.T) {
		translationData := map[string]interface{}{
			"key_id":  keyID,
			"locale":  "es",
			"content": "¡Hola, Mundo!",
		}

		w := suite.makeAuthenticatedRequest("POST", "/api/translations", translationData)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "¡Hola, Mundo!", data["content"])
		assert.Equal(t, "es", data["locale"])
	})

	// Test getting translations for a key
	suite.T().Run("GetTranslationsForKey", func(t *testing.T) {
		url := fmt.Sprintf("/api/keys/%s/translations", keyID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].([]interface{})
		assert.Len(t, data, 2) // Should have 2 translations (en and es)
	})

	// Test getting translations for a project
	suite.T().Run("GetProjectTranslations", func(t *testing.T) {
		url := fmt.Sprintf("/api/projects/%s/translations", suite.projectID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 2)
	})
}

// TestTranslationExport tests translation export functionality
func (suite *IntegrationTestSuite) TestTranslationExport() {
	// Create some test data first
	keyID := suite.createTestTranslationKey(suite.projectID, "export_test", "Test key for export")
	suite.createTestTranslation(keyID, "en", "Export Test")
	suite.createTestTranslation(keyID, "es", "Prueba de Exportación")

	// Test exporting translations as JSON
	suite.T().Run("ExportTranslationsJSON", func(t *testing.T) {
		url := fmt.Sprintf("/api/projects/%s/export?format=json&locale=en", suite.projectID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var exportData map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &exportData)
		assert.NoError(t, err)
		assert.Contains(t, exportData, "export_test")
	})

	// Test exporting all locales
	suite.T().Run("ExportAllLocales", func(t *testing.T) {
		url := fmt.Sprintf("/api/projects/%s/export?format=json", suite.projectID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Contains(t, data, "en")
		assert.Contains(t, data, "es")
	})
}

// TestTranslationImport tests translation import functionality
func (suite *IntegrationTestSuite) TestTranslationImport() {
	// Test importing translations
	suite.T().Run("ImportTranslations", func(t *testing.T) {
		importData := map[string]interface{}{
			"locale": "fr",
			"translations": map[string]interface{}{
				"import_test_1": "Test d'importation 1",
				"import_test_2": "Test d'importation 2",
			},
		}

		url := fmt.Sprintf("/api/projects/%s/import", suite.projectID)
		w := suite.makeAuthenticatedRequest("POST", url, importData)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Contains(t, data, "imported_count")
		assert.Contains(t, data, "created_keys")
	})

	// Test importing with invalid data
	suite.T().Run("ImportInvalidData", func(t *testing.T) {
		importData := map[string]interface{}{
			"locale": "", // Invalid locale
			"translations": map[string]interface{}{
				"test": "value",
			},
		}

		url := fmt.Sprintf("/api/projects/%s/import", suite.projectID)
		w := suite.makeAuthenticatedRequest("POST", url, importData)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, false, response["success"])
	})
}

// TestTranslationSearch tests translation search functionality
func (suite *IntegrationTestSuite) TestTranslationSearch() {
	// Create test data
	keyID1 := suite.createTestTranslationKey(suite.projectID, "search_test_1", "First search test")
	keyID2 := suite.createTestTranslationKey(suite.projectID, "search_test_2", "Second search test")
	
	suite.createTestTranslation(keyID1, "en", "Search result one")
	suite.createTestTranslation(keyID2, "en", "Search result two")

	// Test searching translations
	suite.T().Run("SearchTranslations", func(t *testing.T) {
		url := fmt.Sprintf("/api/projects/%s/search?q=search&locale=en", suite.projectID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 2)
	})

	// Test searching with filters
	suite.T().Run("SearchWithFilters", func(t *testing.T) {
		url := fmt.Sprintf("/api/projects/%s/search?q=result&locale=en&key_filter=search_test_1", suite.projectID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 1)
	})
}

#!/bin/bash

# Integration Tests Setup Script
# This script helps set up and run integration tests for the Go backend

set -e

echo "🚀 Setting up Integration Tests for Go Backend"
echo "=============================================="

# Check if we're in the right directory
if [ ! -f "go.mod" ]; then
    echo "❌ Error: Please run this script from the backend-go directory"
    exit 1
fi

# Set environment variables for tests
export APP_ENV=test
export DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"
export JWT_SECRET="test-secret-key-for-testing-only"
export JWT_REFRESH_SECRET="test-refresh-secret-key"
export JWT_EXPIRATION_HOURS="24"
export LOG_LEVEL="error"
export PORT="8301"

echo "✅ Environment variables set"

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "❌ Error: Go is not installed or not in PATH"
    exit 1
fi

echo "✅ Go is available: $(go version)"

# Install dependencies
echo "📦 Installing dependencies..."
go mod tidy
go mod download

echo "✅ Dependencies installed"

# Check database connectivity
echo "🔍 Testing database connectivity..."
if go run -c 'package main; import ("database/sql"; _ "github.com/lib/pq"); func main() { db, err := sql.Open("postgres", "'$DATABASE_URL'"); if err != nil { panic(err) }; defer db.Close(); if err := db.Ping(); err != nil { panic(err) }; println("Database connection successful") }' 2>/dev/null; then
    echo "✅ Database connection successful"
else
    echo "⚠️  Warning: Could not verify database connection, but tests may still work"
fi

# Function to run tests
run_tests() {
    local test_type=$1
    local extra_args=$2
    
    echo "🧪 Running $test_type tests..."
    
    case $test_type in
        "unit")
            go test -v -run "Test[^I]" ./tests/ $extra_args
            ;;
        "integration")
            go test -v -run "TestIntegration" ./tests/ $extra_args
            ;;
        "all")
            go test -v ./tests/ $extra_args
            ;;
        "coverage")
            go test -v -coverprofile=coverage.out -covermode=atomic ./tests/ $extra_args
            go tool cover -html=coverage.out -o coverage.html
            echo "📊 Coverage report generated: coverage.html"
            go tool cover -func=coverage.out | tail -1
            ;;
        *)
            echo "❌ Unknown test type: $test_type"
            echo "Available types: unit, integration, all, coverage"
            return 1
            ;;
    esac
}

# Parse command line arguments
case "${1:-all}" in
    "setup")
        echo "✅ Setup completed successfully!"
        echo ""
        echo "Next steps:"
        echo "1. Run tests: ./tests/setup.sh all"
        echo "2. Run with coverage: ./tests/setup.sh coverage"
        echo "3. Run only unit tests: ./tests/setup.sh unit"
        echo "4. Run only integration tests: ./tests/setup.sh integration"
        ;;
    "unit")
        run_tests "unit" "${@:2}"
        ;;
    "integration")
        run_tests "integration" "${@:2}"
        ;;
    "working")
        echo "🧪 Running working integration tests..."
        go test -v ./tests/working_integration_test.go "${@:2}"
        ;;
    "comprehensive")
        echo "🧪 Running comprehensive test suite..."
        go test -v ./tests/working_integration_test.go ./tests/api_keys_working_test.go ./tests/translation_working_test.go ./tests/project_management_test.go ./tests/organization_management_test.go ./tests/user_management_test.go "${@:2}"
        ;;
    "modules")
        echo "🧪 Running all module tests..."
        go test -v ./tests/api_keys_working_test.go ./tests/translation_working_test.go ./tests/project_management_test.go ./tests/organization_management_test.go ./tests/user_management_test.go "${@:2}"
        ;;
    "all")
        run_tests "all" "${@:2}"
        ;;
    "coverage")
        run_tests "coverage" "${@:2}"
        ;;
    "clean")
        echo "🧹 Cleaning test artifacts..."
        rm -f coverage.out coverage.html
        echo "✅ Cleanup completed"
        ;;
    "help")
        echo "Usage: $0 [command] [options]"
        echo ""
        echo "Commands:"
        echo "  setup         - Set up test environment (default)"
        echo "  unit          - Run unit tests only"
        echo "  integration   - Run integration tests only"
        echo "  working       - Run working integration tests (recommended)"
        echo "  comprehensive - Run all working tests (core + modules)"
        echo "  modules       - Run module-specific tests only"
        echo "  all           - Run all tests"
        echo "  coverage      - Run tests with coverage report"
        echo "  clean         - Clean test artifacts"
        echo "  help          - Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0 all"
        echo "  $0 coverage"
        echo "  $0 unit -run TestJWT"
        echo "  $0 integration -timeout 60s"
        ;;
    *)
        echo "❌ Unknown command: $1"
        echo "Run '$0 help' for usage information"
        exit 1
        ;;
esac

package tests

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestAPIKeyManagement tests API key CRUD operations
func (suite *IntegrationTestSuite) TestAPIKeyManagement() {
	// Test creating an API key
	suite.T().Run("CreateAPIKey", func(t *testing.T) {
		apiKeyData := map[string]interface{}{
			"name":        "Test API Key",
			"description": "API key for testing",
			"permissions": []string{"read", "write"},
		}

		url := fmt.Sprintf("/api/organizations/%s/api-keys", suite.orgID)
		w := suite.makeAuthenticatedRequest("POST", url, apiKeyData)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "Test API Key", data["name"])
		assert.Contains(t, data, "key")
		assert.Contains(t, data, "id")
	})

	// Test listing API keys
	suite.T().Run("ListAPIKeys", func(t *testing.T) {
		url := fmt.Sprintf("/api/organizations/%s/api-keys", suite.orgID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 1)
	})
}

// TestAPIKeyUsageAnalytics tests API key usage analytics endpoints
func (suite *IntegrationTestSuite) TestAPIKeyUsageAnalytics() {
	// First create an API key
	apiKeyData := map[string]interface{}{
		"name":        "Analytics Test Key",
		"description": "API key for analytics testing",
		"permissions": []string{"read"},
	}

	createURL := fmt.Sprintf("/api/organizations/%s/api-keys", suite.orgID)
	w := suite.makeAuthenticatedRequest("POST", createURL, apiKeyData)
	assert.Equal(suite.T(), http.StatusCreated, w.Code)

	var createResponse map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &createResponse)
	assert.NoError(suite.T(), err)

	data := createResponse["data"].(map[string]interface{})
	apiKeyID := data["id"].(string)

	// Test getting API key usage
	suite.T().Run("GetAPIKeyUsage", func(t *testing.T) {
		url := fmt.Sprintf("/api/organizations/%s/api-keys/%s/usage", suite.orgID, apiKeyID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Contains(t, data, "api_key_id")
		assert.Contains(t, data, "total_calls")
		assert.Contains(t, data, "success_rate")
	})

	// Test getting usage by endpoint
	suite.T().Run("GetAPIKeyUsageByEndpoint", func(t *testing.T) {
		url := fmt.Sprintf("/api/organizations/%s/api-keys/%s/usage/by-endpoint", suite.orgID, apiKeyID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])
	})

	// Test getting daily usage
	suite.T().Run("GetAPIKeyUsageByDay", func(t *testing.T) {
		url := fmt.Sprintf("/api/organizations/%s/api-keys/%s/usage/by-day?days=7", suite.orgID, apiKeyID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])
	})
}

// TestAPIKeyAuditLogs tests API key audit logs
func (suite *IntegrationTestSuite) TestAPIKeyAuditLogs() {
	// First create an API key
	apiKeyData := map[string]interface{}{
		"name":        "Audit Test Key",
		"description": "API key for audit testing",
		"permissions": []string{"read"},
	}

	createURL := fmt.Sprintf("/api/organizations/%s/api-keys", suite.orgID)
	w := suite.makeAuthenticatedRequest("POST", createURL, apiKeyData)
	assert.Equal(suite.T(), http.StatusCreated, w.Code)

	var createResponse map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &createResponse)
	assert.NoError(suite.T(), err)

	data := createResponse["data"].(map[string]interface{})
	apiKeyID := data["id"].(string)

	// Test getting API key audit logs
	suite.T().Run("GetAPIKeyAuditLogs", func(t *testing.T) {
		url := fmt.Sprintf("/api/organizations/%s/api-keys/%s/audit-logs", suite.orgID, apiKeyID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])
	})
}

// TestAPIKeyPermissions tests API key permission validation
func (suite *IntegrationTestSuite) TestAPIKeyPermissions() {
	// Test creating API key with invalid permissions
	suite.T().Run("CreateAPIKeyInvalidPermissions", func(t *testing.T) {
		apiKeyData := map[string]interface{}{
			"name":        "Invalid Permissions Key",
			"description": "API key with invalid permissions",
			"permissions": []string{"invalid_permission"},
		}

		url := fmt.Sprintf("/api/organizations/%s/api-keys", suite.orgID)
		w := suite.makeAuthenticatedRequest("POST", url, apiKeyData)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, false, response["success"])
	})

	// Test creating API key with valid permissions
	suite.T().Run("CreateAPIKeyValidPermissions", func(t *testing.T) {
		apiKeyData := map[string]interface{}{
			"name":        "Valid Permissions Key",
			"description": "API key with valid permissions",
			"permissions": []string{"read", "write", "admin"},
		}

		url := fmt.Sprintf("/api/organizations/%s/api-keys", suite.orgID)
		w := suite.makeAuthenticatedRequest("POST", url, apiKeyData)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])
	})
}

// TestAPIKeyUpdate tests updating API keys
func (suite *IntegrationTestSuite) TestAPIKeyUpdate() {
	// First create an API key
	apiKeyData := map[string]interface{}{
		"name":        "Update Test Key",
		"description": "API key for update testing",
		"permissions": []string{"read"},
	}

	createURL := fmt.Sprintf("/api/organizations/%s/api-keys", suite.orgID)
	w := suite.makeAuthenticatedRequest("POST", createURL, apiKeyData)
	assert.Equal(suite.T(), http.StatusCreated, w.Code)

	var createResponse map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &createResponse)
	assert.NoError(suite.T(), err)

	data := createResponse["data"].(map[string]interface{})
	apiKeyID := data["id"].(string)

	// Test updating the API key
	suite.T().Run("UpdateAPIKey", func(t *testing.T) {
		updateData := map[string]interface{}{
			"name":        "Updated Test Key",
			"description": "Updated description",
			"permissions": []string{"read", "write"},
		}

		url := fmt.Sprintf("/api/organizations/%s/api-keys/%s", suite.orgID, apiKeyID)
		w := suite.makeAuthenticatedRequest("PUT", url, updateData)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "Updated Test Key", data["name"])
		assert.Equal(t, "Updated description", data["description"])
	})

	// Test deleting the API key
	suite.T().Run("DeleteAPIKey", func(t *testing.T) {
		url := fmt.Sprintf("/api/organizations/%s/api-keys/%s", suite.orgID, apiKeyID)
		w := suite.makeAuthenticatedRequest("DELETE", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])
	})
}

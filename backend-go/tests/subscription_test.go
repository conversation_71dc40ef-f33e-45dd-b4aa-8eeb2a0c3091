package tests

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"

	"adc-multi-languages/models"

	"github.com/stretchr/testify/assert"
)

// TestSubscriptionManagement tests organization subscription management
func (suite *IntegrationTestSuite) TestSubscriptionManagement() {
	// First, we need a subscription plan
	var planID string

	// Get existing subscription plan from test data
	var plan models.SubscriptionPlan
	err := suite.db.First(&plan).Error
	suite.Require().NoError(err)
	planID = plan.ID.String()

	// Test getting organization subscription (should be empty initially)
	suite.T().Run("GetOrganizationSubscriptionEmpty", func(t *testing.T) {
		url := fmt.Sprintf("/api/organizations/%s/subscription", suite.orgID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusNotFound, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, false, response["success"])
	})

	// Test creating organization subscription
	suite.T().Run("CreateOrganizationSubscription", func(t *testing.T) {
		subscriptionData := map[string]interface{}{
			"subscription_plan_id": planID,
			"billing_period":       "monthly",
		}

		url := fmt.Sprintf("/api/organizations/%s/subscription", suite.orgID)
		w := suite.makeAuthenticatedRequest("POST", url, subscriptionData)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Contains(t, data, "id")
		assert.Equal(t, "active", data["status"])
		assert.Equal(t, "monthly", data["billing_period"])
		assert.Contains(t, data, "subscription_plan")
	})

	// Test getting organization subscription (should exist now)
	suite.T().Run("GetOrganizationSubscription", func(t *testing.T) {
		url := fmt.Sprintf("/api/organizations/%s/subscription", suite.orgID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "active", data["status"])
		assert.Contains(t, data, "current_period_start")
		assert.Contains(t, data, "current_period_end")
	})

	// Test verifying organization subscription
	suite.T().Run("VerifyOrganizationSubscription", func(t *testing.T) {
		url := fmt.Sprintf("/api/organizations/%s/subscription/verify", suite.orgID)
		w := suite.makeAuthenticatedRequest("PUT", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])
	})

	// Test canceling organization subscription
	suite.T().Run("CancelOrganizationSubscription", func(t *testing.T) {
		url := fmt.Sprintf("/api/organizations/%s/subscription", suite.orgID)
		w := suite.makeAuthenticatedRequest("DELETE", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])
	})
}

// TestSubscriptionValidation tests subscription validation scenarios
func (suite *IntegrationTestSuite) TestSubscriptionValidation() {
	// Test creating subscription with invalid plan ID
	suite.T().Run("CreateSubscriptionInvalidPlan", func(t *testing.T) {
		subscriptionData := map[string]interface{}{
			"subscription_plan_id": "invalid-plan-id",
			"billing_period":       "monthly",
		}

		url := fmt.Sprintf("/api/organizations/%s/subscription", suite.orgID)
		w := suite.makeAuthenticatedRequest("POST", url, subscriptionData)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, false, response["success"])
	})

	// Test creating subscription with invalid billing period
	suite.T().Run("CreateSubscriptionInvalidBillingPeriod", func(t *testing.T) {
		var plan models.SubscriptionPlan
		err := suite.db.First(&plan).Error
		suite.Require().NoError(err)

		subscriptionData := map[string]interface{}{
			"subscription_plan_id": plan.ID.String(),
			"billing_period":       "invalid_period",
		}

		url := fmt.Sprintf("/api/organizations/%s/subscription", suite.orgID)
		w := suite.makeAuthenticatedRequest("POST", url, subscriptionData)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, false, response["success"])
	})
}

// TestAuditLogs tests audit log functionality
func (suite *IntegrationTestSuite) TestAuditLogs() {
	// Test getting organization audit logs
	suite.T().Run("GetOrganizationAuditLogs", func(t *testing.T) {
		url := fmt.Sprintf("/api/organizations/%s/audit-logs", suite.orgID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		// Should have pagination info
		assert.Contains(t, response, "pagination")
	})

	// Test getting audit log actions
	suite.T().Run("GetAuditLogActions", func(t *testing.T) {
		w := suite.makeAuthenticatedRequest("GET", "/api/audit-logs/actions", nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Contains(t, data, "authentication")
		assert.Contains(t, data, "organization")
		assert.Contains(t, data, "project")
	})

	// Test getting audit log resource types
	suite.T().Run("GetAuditLogResourceTypes", func(t *testing.T) {
		w := suite.makeAuthenticatedRequest("GET", "/api/audit-logs/resource-types", nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].([]interface{})
		assert.Contains(t, data, "user")
		assert.Contains(t, data, "organization")
		assert.Contains(t, data, "project")
	})

	// Test audit logs with filters
	suite.T().Run("GetAuditLogsWithFilters", func(t *testing.T) {
		url := fmt.Sprintf("/api/organizations/%s/audit-logs?action=create_organization&page=1&limit=10", suite.orgID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])
	})
}

// TestProjectResources tests project resource management
func (suite *IntegrationTestSuite) TestProjectResources() {
	// Test getting project locales (should be empty initially)
	suite.T().Run("GetProjectLocalesEmpty", func(t *testing.T) {
		url := fmt.Sprintf("/api/projects/%s/locales", suite.projectID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].([]interface{})
		assert.Len(t, data, 0) // Should be empty initially
	})

	// Test adding locale to project
	suite.T().Run("AddProjectLocale", func(t *testing.T) {
		localeData := map[string]interface{}{
			"locale_code": "en",
		}

		url := fmt.Sprintf("/api/projects/%s/locales", suite.projectID)
		w := suite.makeAuthenticatedRequest("POST", url, localeData)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "en", data["code"])
		assert.Equal(t, "English", data["name"])
	})

	// Test adding another locale
	suite.T().Run("AddAnotherProjectLocale", func(t *testing.T) {
		localeData := map[string]interface{}{
			"locale_code": "es",
		}

		url := fmt.Sprintf("/api/projects/%s/locales", suite.projectID)
		w := suite.makeAuthenticatedRequest("POST", url, localeData)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	// Test getting project locales (should have locales now)
	suite.T().Run("GetProjectLocales", func(t *testing.T) {
		url := fmt.Sprintf("/api/projects/%s/locales", suite.projectID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].([]interface{})
		assert.Len(t, data, 2) // Should have 2 locales now
	})

	// Test getting project resources overview
	suite.T().Run("GetProjectResources", func(t *testing.T) {
		url := fmt.Sprintf("/api/projects/%s/resources", suite.projectID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Contains(t, data, "project_id")
		assert.Contains(t, data, "project_name")
		assert.Contains(t, data, "locales")
		assert.Contains(t, data, "total_keys")
		assert.Contains(t, data, "completion_rate")
	})

	// Test adding duplicate locale (should fail)
	suite.T().Run("AddDuplicateProjectLocale", func(t *testing.T) {
		localeData := map[string]interface{}{
			"locale_code": "en", // Already added
		}

		url := fmt.Sprintf("/api/projects/%s/locales", suite.projectID)
		w := suite.makeAuthenticatedRequest("POST", url, localeData)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, false, response["success"])
	})
}

package middleware

import (
	"net/http"
	"strconv"
	"sync"
	"time"

	"github.com/gin-gonic/gin"

	"adc-multi-languages/utils"
)

// RateLimiter represents a rate limiter
type RateLimiter struct {
	requests map[string][]time.Time
	mutex    sync.RWMutex
	limit    int
	window   time.Duration
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(limit int, window time.Duration) *RateLimiter {
	rl := &RateLimiter{
		requests: make(map[string][]time.Time),
		limit:    limit,
		window:   window,
	}

	// Start cleanup goroutine
	go rl.cleanup()

	return rl
}

// cleanup removes old entries from the rate limiter
func (rl *RateLimiter) cleanup() {
	ticker := time.NewTicker(rl.window)
	defer ticker.Stop()

	for range ticker.C {
		rl.mutex.Lock()
		now := time.Now()
		for key, timestamps := range rl.requests {
			// Remove timestamps older than the window
			var validTimestamps []time.Time
			for _, timestamp := range timestamps {
				if now.Sub(timestamp) < rl.window {
					validTimestamps = append(validTimestamps, timestamp)
				}
			}

			if len(validTimestamps) == 0 {
				delete(rl.requests, key)
			} else {
				rl.requests[key] = validTimestamps
			}
		}
		rl.mutex.Unlock()
	}
}

// Allow checks if a request should be allowed
func (rl *RateLimiter) Allow(key string) bool {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	now := time.Now()

	// Get existing timestamps for this key
	timestamps, exists := rl.requests[key]
	if !exists {
		timestamps = []time.Time{}
	}

	// Remove timestamps older than the window
	var validTimestamps []time.Time
	for _, timestamp := range timestamps {
		if now.Sub(timestamp) < rl.window {
			validTimestamps = append(validTimestamps, timestamp)
		}
	}

	// Check if we're under the limit
	if len(validTimestamps) >= rl.limit {
		return false
	}

	// Add current timestamp
	validTimestamps = append(validTimestamps, now)
	rl.requests[key] = validTimestamps

	return true
}

// GetRemainingRequests returns the number of remaining requests for a key
func (rl *RateLimiter) GetRemainingRequests(key string) int {
	rl.mutex.RLock()
	defer rl.mutex.RUnlock()

	timestamps, exists := rl.requests[key]
	if !exists {
		return rl.limit
	}

	now := time.Now()
	var validCount int
	for _, timestamp := range timestamps {
		if now.Sub(timestamp) < rl.window {
			validCount++
		}
	}

	remaining := rl.limit - validCount
	if remaining < 0 {
		return 0
	}
	return remaining
}

// GetResetTime returns when the rate limit will reset for a key
func (rl *RateLimiter) GetResetTime(key string) time.Time {
	rl.mutex.RLock()
	defer rl.mutex.RUnlock()

	timestamps, exists := rl.requests[key]
	if !exists || len(timestamps) == 0 {
		return time.Now()
	}

	// Find the oldest valid timestamp
	now := time.Now()
	var oldestValid time.Time
	for _, timestamp := range timestamps {
		if now.Sub(timestamp) < rl.window {
			if oldestValid.IsZero() || timestamp.Before(oldestValid) {
				oldestValid = timestamp
			}
		}
	}

	if oldestValid.IsZero() {
		return time.Now()
	}

	return oldestValid.Add(rl.window)
}

// Global rate limiters
var (
	// General API rate limiter: 100 requests per minute
	generalLimiter = NewRateLimiter(100, time.Minute)

	// Auth rate limiter: 10 requests per minute (for login/signup)
	authLimiter = NewRateLimiter(10, time.Minute)

	// API key rate limiter: 1000 requests per hour
	apiKeyLimiter = NewRateLimiter(1000, time.Hour)
)

// RateLimitMiddleware creates a rate limiting middleware
func RateLimitMiddleware(limiter *RateLimiter) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get client identifier (IP address or user ID)
		key := getClientKey(c)

		if !limiter.Allow(key) {
			// Rate limit exceeded
			resetTime := limiter.GetResetTime(key)

			c.Header("X-RateLimit-Limit", "100")
			c.Header("X-RateLimit-Remaining", "0")
			c.Header("X-RateLimit-Reset", resetTime.Format(time.RFC3339))

			utils.Logger.Warn("Rate limit exceeded for client: " + key)
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":       "Rate limit exceeded",
				"message":     "Too many requests. Please try again later.",
				"retry_after": int(time.Until(resetTime).Seconds()),
			})
			c.Abort()
			return
		}

		// Add rate limit headers
		remaining := limiter.GetRemainingRequests(key)
		resetTime := limiter.GetResetTime(key)

		c.Header("X-RateLimit-Limit", "100")
		c.Header("X-RateLimit-Remaining", strconv.Itoa(remaining))
		c.Header("X-RateLimit-Reset", resetTime.Format(time.RFC3339))

		c.Next()
	}
}

// GeneralRateLimit applies general rate limiting
func GeneralRateLimit() gin.HandlerFunc {
	return RateLimitMiddleware(generalLimiter)
}

// AuthRateLimit applies auth-specific rate limiting
func AuthRateLimit() gin.HandlerFunc {
	return RateLimitMiddleware(authLimiter)
}

// APIKeyRateLimit applies API key-specific rate limiting
func APIKeyRateLimit() gin.HandlerFunc {
	return RateLimitMiddleware(apiKeyLimiter)
}

// getClientKey returns a unique identifier for the client
func getClientKey(c *gin.Context) string {
	// Try to get user ID first (for authenticated requests)
	if userID, exists := c.Get("user_id"); exists {
		return "user:" + userID.(string)
	}

	// Try to get API key ID (for API key requests)
	if apiKeyID, exists := c.Get("api_key_id"); exists {
		return "api_key:" + apiKeyID.(string)
	}

	// Fall back to IP address
	clientIP := c.ClientIP()
	return "ip:" + clientIP
}

// PerUserRateLimit creates a rate limiter that applies per authenticated user
func PerUserRateLimit(limit int, window time.Duration) gin.HandlerFunc {
	limiter := NewRateLimiter(limit, window)

	return func(c *gin.Context) {
		// Only apply to authenticated users
		userID, exists := c.Get("user_id")
		if !exists {
			c.Next()
			return
		}

		key := "user:" + userID.(string)

		if !limiter.Allow(key) {
			resetTime := limiter.GetResetTime(key)

			c.Header("X-RateLimit-Limit", strconv.Itoa(limit))
			c.Header("X-RateLimit-Remaining", "0")
			c.Header("X-RateLimit-Reset", resetTime.Format(time.RFC3339))

			utils.Logger.Warn("Per-user rate limit exceeded for user: " + userID.(string))
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":       "Rate limit exceeded",
				"message":     "Too many requests. Please try again later.",
				"retry_after": int(time.Until(resetTime).Seconds()),
			})
			c.Abort()
			return
		}

		remaining := limiter.GetRemainingRequests(key)
		resetTime := limiter.GetResetTime(key)

		c.Header("X-RateLimit-Limit", strconv.Itoa(limit))
		c.Header("X-RateLimit-Remaining", strconv.Itoa(remaining))
		c.Header("X-RateLimit-Reset", resetTime.Format(time.RFC3339))

		c.Next()
	}
}

package middleware

import (
	"time"

	"github.com/gin-gonic/gin"

	"adc-multi-languages/services"
)

// MetricsMiddleware creates a middleware that collects request metrics
func MetricsMiddleware() gin.HandlerFunc {
	metricsService := services.GetMetricsService()

	return func(c *gin.Context) {
		// Record start time
		startTime := time.Now()

		// Process request
		c.Next()

		// Calculate response time
		responseTime := time.Since(startTime)

		// Get user ID if available
		userID := ""
		if uid, exists := c.Get("user_id"); exists {
			userID = uid.(string)
		}

		// Get API key ID if available
		apiKeyID := ""
		if akid, exists := c.Get("api_key_id"); exists {
			apiKeyID = akid.(string)
		}

		// Record metrics
		metrics := services.RequestMetrics{
			Endpoint:     c.FullPath(),
			Method:       c.Request.Method,
			StatusCode:   c.Writer.Status(),
			ResponseTime: responseTime,
			UserID:       userID,
			APIKeyID:     apiKeyID,
			Timestamp:    startTime,
		}

		metricsService.RecordRequest(metrics)
	}
}

// PrometheusMetricsMiddleware creates a middleware for Prometheus-style metrics
func PrometheusMetricsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip metrics collection for metrics endpoint itself
		if c.Request.URL.Path == "/metrics" {
			c.Next()
			return
		}

		startTime := time.Now()
		c.Next()
		duration := time.Since(startTime)

		// Here you could integrate with Prometheus client library
		// For now, we'll use our internal metrics service
		metricsService := services.GetMetricsService()
		
		userID := ""
		if uid, exists := c.Get("user_id"); exists {
			userID = uid.(string)
		}

		apiKeyID := ""
		if akid, exists := c.Get("api_key_id"); exists {
			apiKeyID = akid.(string)
		}

		metrics := services.RequestMetrics{
			Endpoint:     c.FullPath(),
			Method:       c.Request.Method,
			StatusCode:   c.Writer.Status(),
			ResponseTime: duration,
			UserID:       userID,
			APIKeyID:     apiKeyID,
			Timestamp:    startTime,
		}

		metricsService.RecordRequest(metrics)
	}
}

package handlers

import (
	"path/filepath"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"adc-multi-languages/services"
	"adc-multi-languages/utils"
)

// FileUploadHandler handles file upload-related requests
type FileUploadHandler struct {
	fileUploadService *services.FileUploadService
}

// NewFileUploadHandler creates a new file upload handler
func NewFileUploadHandler() *FileUploadHandler {
	return &FileUploadHandler{
		fileUploadService: services.NewFileUploadService(),
	}
}

// UploadFile handles POST /api/files/upload
func (h *FileUploadHandler) UploadFile(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	// Parse multipart form
	err := c.Request.ParseMultipartForm(10 << 20) // 10 MB max
	if err != nil {
		utils.BadRequest(c, "Failed to parse multipart form: "+err.Error())
		return
	}

	// Get file from form
	file, fileHeader, err := c.Request.FormFile("file")
	if err != nil {
		utils.BadRequest(c, "No file provided or invalid file: "+err.Error())
		return
	}
	defer file.Close()

	// Get optional subdirectory
	subDir := c.PostForm("subdirectory")
	if subDir == "" {
		subDir = "general"
	}

	// Add user ID to subdirectory for organization
	subDir = filepath.Join(subDir, userID.(string))

	// Upload file
	uploadedFile, err := h.fileUploadService.UploadFile(fileHeader, subDir)
	if err != nil {
		utils.BadRequest(c, "File upload failed: "+err.Error())
		return
	}

	utils.SuccessWithMessage(c, uploadedFile, "File uploaded successfully")
}

// UploadMultipleFiles handles POST /api/files/upload-multiple
func (h *FileUploadHandler) UploadMultipleFiles(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	// Parse multipart form
	err := c.Request.ParseMultipartForm(50 << 20) // 50 MB max for multiple files
	if err != nil {
		utils.BadRequest(c, "Failed to parse multipart form: "+err.Error())
		return
	}

	// Get files from form
	form := c.Request.MultipartForm
	files := form.File["files"]

	if len(files) == 0 {
		utils.BadRequest(c, "No files provided")
		return
	}

	if len(files) > 10 {
		utils.BadRequest(c, "Too many files. Maximum 10 files allowed")
		return
	}

	// Get optional subdirectory
	subDir := c.PostForm("subdirectory")
	if subDir == "" {
		subDir = "general"
	}

	// Add user ID to subdirectory for organization
	subDir = filepath.Join(subDir, userID.(string))

	// Upload files
	uploadedFiles, errors := h.fileUploadService.UploadMultipleFiles(files, subDir)

	// Prepare response
	response := map[string]interface{}{
		"uploaded_files": uploadedFiles,
		"total_files":    len(files),
		"successful":     len(uploadedFiles),
		"failed":         len(errors),
	}

	if len(errors) > 0 {
		var errorMessages []string
		for _, err := range errors {
			errorMessages = append(errorMessages, err.Error())
		}
		response["errors"] = errorMessages
	}

	if len(uploadedFiles) > 0 {
		utils.SuccessWithMessage(c, response, "Files processed")
	} else {
		utils.BadRequest(c, "All file uploads failed")
	}
}

// GetFileInfo handles GET /api/files/:id/info
func (h *FileUploadHandler) GetFileInfo(c *gin.Context) {
	fileID := c.Param("id")

	// Validate UUID
	if _, err := uuid.Parse(fileID); err != nil {
		utils.BadRequest(c, "Invalid file ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	// Construct potential file paths (we need to search for the file)
	// In a real implementation, you'd store file metadata in the database
	subDirs := []string{
		filepath.Join("general", userID.(string)),
		filepath.Join("translations", userID.(string)),
		filepath.Join("projects", userID.(string)),
	}

	var fileInfo *services.UploadedFile
	var err error

	for _, subDir := range subDirs {
		// Try different extensions
		extensions := h.fileUploadService.GetAllowedTypes()
		for _, ext := range extensions {
			fileName := fileID + ext
			filePath := filepath.Join("./uploads", subDir, fileName)
			
			fileInfo, err = h.fileUploadService.GetFileInfo(filePath)
			if err == nil {
				fileInfo.ID = fileID
				break
			}
		}
		if fileInfo != nil {
			break
		}
	}

	if fileInfo == nil {
		utils.NotFound(c, "File not found")
		return
	}

	utils.Success(c, fileInfo)
}

// DeleteFile handles DELETE /api/files/:id
func (h *FileUploadHandler) DeleteFile(c *gin.Context) {
	fileID := c.Param("id")

	// Validate UUID
	if _, err := uuid.Parse(fileID); err != nil {
		utils.BadRequest(c, "Invalid file ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	// Find and delete the file
	subDirs := []string{
		filepath.Join("general", userID.(string)),
		filepath.Join("translations", userID.(string)),
		filepath.Join("projects", userID.(string)),
	}

	var deleted bool
	for _, subDir := range subDirs {
		extensions := h.fileUploadService.GetAllowedTypes()
		for _, ext := range extensions {
			fileName := fileID + ext
			filePath := filepath.Join("./uploads", subDir, fileName)
			
			if err := h.fileUploadService.DeleteFile(filePath); err == nil {
				deleted = true
				break
			}
		}
		if deleted {
			break
		}
	}

	if !deleted {
		utils.NotFound(c, "File not found")
		return
	}

	utils.SuccessWithMessage(c, nil, "File deleted successfully")
}

// GetUploadConfig handles GET /api/files/config
func (h *FileUploadHandler) GetUploadConfig(c *gin.Context) {
	config := map[string]interface{}{
		"max_file_size":   h.fileUploadService.GetMaxFileSize(),
		"allowed_types":   h.fileUploadService.GetAllowedTypes(),
		"max_files":       10,
		"supported_formats": map[string]string{
			".json":       "JSON translation files",
			".csv":        "Comma-separated values",
			".xlsx":       "Excel spreadsheet",
			".xls":        "Excel spreadsheet (legacy)",
			".txt":        "Plain text files",
			".po":         "GNU gettext files",
			".pot":        "GNU gettext template",
			".xliff":      "XLIFF translation files",
			".tmx":        "Translation Memory eXchange",
			".xml":        "XML files",
			".yaml":       "YAML files",
			".yml":        "YAML files",
			".properties": "Java properties files",
			".strings":    "iOS strings files",
			".resx":       ".NET resource files",
			".arb":        "Application Resource Bundle",
		},
	}

	utils.Success(c, config)
}

// GetUploadStats handles GET /api/files/stats
func (h *FileUploadHandler) GetUploadStats(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	// Get overall stats
	stats, err := h.fileUploadService.GetUploadStats()
	if err != nil {
		utils.InternalError(c, "Failed to get upload statistics: "+err.Error())
		return
	}

	// Add user-specific information
	stats["user_id"] = userID.(string)

	utils.Success(c, stats)
}

// ServeFile handles GET /uploads/*filepath
func (h *FileUploadHandler) ServeFile(c *gin.Context) {
	// Get the file path from the URL
	filePath := c.Param("filepath")
	
	// Security check: ensure the path doesn't contain directory traversal
	if filepath.IsAbs(filePath) || filepath.Clean(filePath) != filePath {
		utils.BadRequest(c, "Invalid file path")
		return
	}

	// Construct full file path
	fullPath := filepath.Join("./uploads", filePath)

	// Check if file exists and get info
	fileInfo, err := h.fileUploadService.GetFileInfo(fullPath)
	if err != nil {
		utils.NotFound(c, "File not found")
		return
	}

	// Set appropriate headers
	c.Header("Content-Type", fileInfo.ContentType)
	c.Header("Content-Length", strconv.FormatInt(fileInfo.FileSize, 10))
	c.Header("Content-Disposition", "inline; filename=\""+fileInfo.OriginalName+"\"")
	c.Header("Cache-Control", "public, max-age=3600") // Cache for 1 hour

	// Serve the file
	c.File(fullPath)
}

// DownloadFile handles GET /api/files/:id/download
func (h *FileUploadHandler) DownloadFile(c *gin.Context) {
	fileID := c.Param("id")

	// Validate UUID
	if _, err := uuid.Parse(fileID); err != nil {
		utils.BadRequest(c, "Invalid file ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	// Find the file
	subDirs := []string{
		filepath.Join("general", userID.(string)),
		filepath.Join("translations", userID.(string)),
		filepath.Join("projects", userID.(string)),
	}

	var fileInfo *services.UploadedFile
	var fullPath string

	for _, subDir := range subDirs {
		extensions := h.fileUploadService.GetAllowedTypes()
		for _, ext := range extensions {
			fileName := fileID + ext
			testPath := filepath.Join("./uploads", subDir, fileName)
			
			if info, err := h.fileUploadService.GetFileInfo(testPath); err == nil {
				fileInfo = info
				fileInfo.ID = fileID
				fullPath = testPath
				break
			}
		}
		if fileInfo != nil {
			break
		}
	}

	if fileInfo == nil {
		utils.NotFound(c, "File not found")
		return
	}

	// Set download headers
	c.Header("Content-Type", "application/octet-stream")
	c.Header("Content-Disposition", "attachment; filename=\""+fileInfo.OriginalName+"\"")
	c.Header("Content-Length", strconv.FormatInt(fileInfo.FileSize, 10))

	// Serve the file for download
	c.File(fullPath)
}

package handlers

import (
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"adc-multi-languages/database"
	"adc-multi-languages/models"
	"adc-multi-languages/utils"
)

// SlugLookupHandler handles slug-based lookup requests
type SlugLookupHandler struct{}

// NewSlugLookupHandler creates a new slug lookup handler
func NewSlugLookupHandler() *SlugLookupHandler {
	return &SlugLookupHandler{}
}

// OrganizationBySlugResponse represents the response structure for organization lookup by slug
type OrganizationBySlugResponse struct {
	ID                 string  `json:"id"`
	Name               string  `json:"name"`
	Slug               string  `json:"slug"`
	Description        *string `json:"description"`
	Website            *string `json:"website"`
	SubscriptionTier   string  `json:"subscription_tier"`
	AICreditsRemaining *int    `json:"ai_credits_remaining"`
	IsActive           bool    `json:"is_active"`
	CreatedAt          string  `json:"created_at"`
	UpdatedAt          string  `json:"updated_at"`
	MemberCount        int     `json:"member_count"`
	ProjectCount       int     `json:"project_count"`
	UserRole           *string `json:"user_role,omitempty"`
}

// ProjectBySlugResponse represents the response structure for project lookup by slug
type ProjectBySlugResponse struct {
	ID               string              `json:"id"`
	Name             string              `json:"name"`
	Slug             string              `json:"slug"`
	Description      *string             `json:"description"`
	DefaultLocale    string              `json:"default_locale"`
	IsActive         bool                `json:"is_active"`
	CreatedAt        string              `json:"created_at"`
	UpdatedAt        string              `json:"updated_at"`
	Organization     OrganizationSummary `json:"organization"`
	LocaleCount      int                 `json:"locale_count"`
	TranslationCount int                 `json:"translation_count"`
	KeyCount         int                 `json:"key_count"`
	UserRole         *string             `json:"user_role,omitempty"`
}

// OrganizationSummary represents a summary of organization information
type OrganizationSummary struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Slug string `json:"slug"`
}

// GetOrganizationBySlug handles GET /api/organizations/slug/:slug
func (h *SlugLookupHandler) GetOrganizationBySlug(c *gin.Context) {
	slug := c.Param("slug")

	if slug == "" {
		utils.BadRequest(c, "Organization slug is required")
		return
	}

	// Get user ID from context (optional for public organizations)
	userID, userExists := c.Get("user_id")
	var userUUID uuid.UUID
	var err error

	if userExists {
		userUUID, err = uuid.Parse(userID.(string))
		if err != nil {
			utils.InternalError(c, "Invalid user ID")
			return
		}
	}

	db := database.GetDB()

	// Get organization by slug
	var organization models.Organization
	if err := db.First(&organization, "slug = ?", slug).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Organization not found")
			return
		}
		utils.InternalError(c, "Failed to fetch organization")
		return
	}

	// Check if user has access to this organization (if user is authenticated)
	var userRole *string
	if userExists {
		var membership models.OrganizationMembership
		if err := db.First(&membership, "organization_id = ? AND user_id = ? AND is_active = ?",
			organization.ID, userUUID, true).Error; err == nil {
			userRole = &membership.Role
		} else if err != gorm.ErrRecordNotFound {
			utils.InternalError(c, "Failed to check organization membership")
			return
		}

		// If user is not a member and organization is private, deny access
		if userRole == nil {
			// For now, we'll assume all organizations are accessible
			// In a real implementation, you might have a "public" field
		}
	}

	// Get organization statistics
	memberCount := h.getOrganizationMemberCount(db, organization.ID)
	projectCount := h.getOrganizationProjectCount(db, organization.ID)

	response := OrganizationBySlugResponse{
		ID:                 organization.ID.String(),
		Name:               organization.Name,
		Slug:               organization.Slug,
		Description:        organization.Description,
		Website:            organization.Website,
		SubscriptionTier:   organization.SubscriptionTier,
		AICreditsRemaining: organization.AICreditsRemaining,
		IsActive:           true, // Organizations are active by default
		CreatedAt:          utils.FormatTimestamp(organization.CreatedAt),
		UpdatedAt:          utils.FormatTimestamp(organization.UpdatedAt),
		MemberCount:        memberCount,
		ProjectCount:       projectCount,
		UserRole:           userRole,
	}

	utils.Success(c, response)
}

// GetProjectBySlug handles GET /api/projects/slug/:slug
func (h *SlugLookupHandler) GetProjectBySlug(c *gin.Context) {
	slug := c.Param("slug")

	if slug == "" {
		utils.BadRequest(c, "Project slug is required")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	db := database.GetDB()

	// Get project by slug with organization
	var project models.Project
	if err := db.Preload("Organization").First(&project, "slug = ?", slug).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Project not found")
			return
		}
		utils.InternalError(c, "Failed to fetch project")
		return
	}

	// Check if user has access to the project's organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ? AND is_active = ?",
		project.OrganizationID, userUUID, true).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have access to this project")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Get project statistics
	localeCount := h.getProjectLocaleCount(db, project.ID)
	keyCount := h.getProjectKeyCount(db, project.ID)
	translationCount := h.getProjectTranslationCount(db, project.ID)

	organizationSummary := OrganizationSummary{
		ID:   project.Organization.ID.String(),
		Name: project.Organization.Name,
		Slug: project.Organization.Slug,
	}

	response := ProjectBySlugResponse{
		ID:          project.ID.String(),
		Name:        project.Name,
		Slug:        project.Slug,
		Description: project.Description,
		DefaultLocale: func() string {
			if project.DefaultLocale != nil {
				return *project.DefaultLocale
			}
			return ""
		}(),
		IsActive:         true, // Projects are active by default
		CreatedAt:        utils.FormatTimestamp(project.CreatedAt),
		UpdatedAt:        utils.FormatTimestamp(project.UpdatedAt),
		Organization:     organizationSummary,
		LocaleCount:      localeCount,
		TranslationCount: translationCount,
		KeyCount:         keyCount,
		UserRole:         &membership.Role,
	}

	utils.Success(c, response)
}

// CheckSlugAvailability handles GET /api/organizations/slug/:slug/available
func (h *SlugLookupHandler) CheckOrganizationSlugAvailability(c *gin.Context) {
	slug := c.Param("slug")

	if slug == "" {
		utils.BadRequest(c, "Slug is required")
		return
	}

	// Validate slug format (basic validation)
	if len(slug) < 3 || len(slug) > 50 {
		utils.BadRequest(c, "Slug must be between 3 and 50 characters")
		return
	}

	db := database.GetDB()

	// Check if slug is already taken
	var count int64
	if err := db.Model(&models.Organization{}).Where("slug = ?", slug).Count(&count).Error; err != nil {
		utils.InternalError(c, "Failed to check slug availability")
		return
	}

	available := count == 0

	response := map[string]interface{}{
		"slug":      slug,
		"available": available,
		"message":   "",
	}

	if !available {
		response["message"] = "This slug is already taken"
	} else {
		response["message"] = "This slug is available"
	}

	utils.Success(c, response)
}

// CheckProjectSlugAvailability handles GET /api/projects/slug/:slug/available
func (h *SlugLookupHandler) CheckProjectSlugAvailability(c *gin.Context) {
	slug := c.Param("slug")
	orgID := c.Query("organization_id")

	if slug == "" {
		utils.BadRequest(c, "Slug is required")
		return
	}

	// Validate slug format
	if len(slug) < 3 || len(slug) > 50 {
		utils.BadRequest(c, "Slug must be between 3 and 50 characters")
		return
	}

	db := database.GetDB()

	query := db.Model(&models.Project{}).Where("slug = ?", slug)

	// If organization ID is provided, check within that organization
	if orgID != "" {
		orgUUID, err := uuid.Parse(orgID)
		if err != nil {
			utils.BadRequest(c, "Invalid organization ID format")
			return
		}
		query = query.Where("organization_id = ?", orgUUID)
	}

	var count int64
	if err := query.Count(&count).Error; err != nil {
		utils.InternalError(c, "Failed to check slug availability")
		return
	}

	available := count == 0

	response := map[string]interface{}{
		"slug":      slug,
		"available": available,
		"message":   "",
		"scope":     "global",
	}

	if orgID != "" {
		response["scope"] = "organization"
		response["organization_id"] = orgID
	}

	if !available {
		if orgID != "" {
			response["message"] = "This slug is already taken within this organization"
		} else {
			response["message"] = "This slug is already taken"
		}
	} else {
		response["message"] = "This slug is available"
	}

	utils.Success(c, response)
}

// Helper methods for calculating statistics

func (h *SlugLookupHandler) getOrganizationMemberCount(db *gorm.DB, orgID uuid.UUID) int {
	var count int64
	db.Model(&models.OrganizationMembership{}).
		Where("organization_id = ? AND is_active = ?", orgID, true).
		Count(&count)
	return int(count)
}

func (h *SlugLookupHandler) getOrganizationProjectCount(db *gorm.DB, orgID uuid.UUID) int {
	var count int64
	db.Model(&models.Project{}).
		Where("organization_id = ?", orgID).
		Count(&count)
	return int(count)
}

func (h *SlugLookupHandler) getProjectLocaleCount(db *gorm.DB, projectID uuid.UUID) int {
	var count int64
	db.Model(&models.ProjectLocale{}).
		Where("project_id = ?", projectID).
		Count(&count)
	return int(count)
}

func (h *SlugLookupHandler) getProjectKeyCount(db *gorm.DB, projectID uuid.UUID) int {
	var count int64
	db.Model(&models.TranslationKey{}).
		Where("project_id = ?", projectID).
		Count(&count)
	return int(count)
}

func (h *SlugLookupHandler) getProjectTranslationCount(db *gorm.DB, projectID uuid.UUID) int {
	var count int64
	db.Model(&models.Translation{}).
		Joins("JOIN translation_keys ON translations.translation_key_id = translation_keys.id").
		Where("translation_keys.project_id = ?", projectID).
		Count(&count)
	return int(count)
}

package handlers

import (
	"net/http"

	"adc-multi-languages/database"
	"adc-multi-languages/models"
	"adc-multi-languages/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stripe/stripe-go/v76"
	"github.com/stripe/stripe-go/v76/checkout/session"
	"github.com/stripe/stripe-go/v76/customer"
)

type StripeCheckoutHandler struct{}

func NewStripeCheckoutHandler() *StripeCheckoutHandler {
	return &StripeCheckoutHandler{}
}

// CreateCheckoutSessionRequest represents the request to create a checkout session
type CreateCheckoutSessionRequest struct {
	PriceID        string `json:"priceId" binding:"required"`
	OrganizationID string `json:"organizationId" binding:"required"`
	SuccessURL     string `json:"successUrl" binding:"required"`
	CancelURL      string `json:"cancelUrl" binding:"required"`
}

// CreateCheckoutSessionResponse represents the response from creating a checkout session
type CreateCheckoutSessionResponse struct {
	URL       string `json:"url"`
	SessionID string `json:"sessionId"`
}

// CreateCheckoutSession handles POST /api/stripe/create-checkout-session
func (h *StripeCheckoutHandler) CreateCheckoutSession(c *gin.Context) {
	var req CreateCheckoutSessionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Validate organization ID format
	orgUUID, err := uuid.Parse(req.OrganizationID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	// Get organization from database
	db := database.GetDB()
	var organization models.Organization
	if err := db.First(&organization, orgUUID).Error; err != nil {
		utils.NotFound(c, "Organization not found")
		return
	}

	// Get user from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	// Verify user has access to this organization
	var membership models.OrganizationMembership
	if err := db.Where("organization_id = ? AND user_id = ?", orgUUID, userID).First(&membership).Error; err != nil {
		utils.Forbidden(c, "User does not have access to this organization")
		return
	}

	// Verify user has admin or owner role
	if membership.Role != "admin" && membership.Role != "owner" {
		utils.Forbidden(c, "Only organization admins and owners can create subscriptions")
		return
	}

	// Create or get Stripe customer for the organization
	var stripeCustomerID string
	if organization.StripeCustomerID != nil {
		stripeCustomerID = *organization.StripeCustomerID
	} else {
		// Get user email for Stripe customer
		var user models.User
		if err := db.First(&user, userID).Error; err != nil {
			utils.InternalError(c, "Failed to get user information")
			return
		}

		// Create new Stripe customer
		customerParams := &stripe.CustomerParams{
			Email: stripe.String(user.Email),
			Name:  stripe.String(organization.Name),
			Metadata: map[string]string{
				"organization_id": organization.ID.String(),
			},
		}

		stripeCustomer, err := customer.New(customerParams)
		if err != nil {
			utils.Logger.Error("Failed to create Stripe customer: " + err.Error())
			utils.InternalError(c, "Failed to create Stripe customer")
			return
		}

		stripeCustomerID = stripeCustomer.ID

		// Update organization with Stripe customer ID
		organization.StripeCustomerID = &stripeCustomerID
		if err := db.Save(&organization).Error; err != nil {
			utils.Logger.Error("Failed to save Stripe customer ID: " + err.Error())
			// Continue anyway, as the customer was created successfully
		}
	}

	// Create checkout session parameters
	params := &stripe.CheckoutSessionParams{
		SuccessURL: stripe.String(req.SuccessURL),
		CancelURL:  stripe.String(req.CancelURL),
		Mode:       stripe.String(string(stripe.CheckoutSessionModeSubscription)),
		LineItems: []*stripe.CheckoutSessionLineItemParams{
			{
				Price:    stripe.String(req.PriceID),
				Quantity: stripe.Int64(1),
			},
		},
		ClientReferenceID: stripe.String(req.OrganizationID),
		Customer:          stripe.String(stripeCustomerID),
		Metadata: map[string]string{
			"organization_id": req.OrganizationID,
		},
		BillingAddressCollection: stripe.String("auto"),
		PhoneNumberCollection: &stripe.CheckoutSessionPhoneNumberCollectionParams{
			Enabled: stripe.Bool(true),
		},
		AllowPromotionCodes: stripe.Bool(true),
		AutomaticTax: &stripe.CheckoutSessionAutomaticTaxParams{
			Enabled: stripe.Bool(true),
		},
	}

	// Create the checkout session
	checkoutSession, err := session.New(params)
	if err != nil {
		utils.Logger.Error("Failed to create Stripe checkout session: " + err.Error())
		utils.InternalError(c, "Failed to create checkout session")
		return
	}

	if checkoutSession.URL == "" {
		utils.Logger.Error("Stripe checkout session created but no URL returned")
		utils.InternalError(c, "Failed to generate checkout URL")
		return
	}

	// Log successful checkout session creation
	utils.Logger.Info("Created Stripe checkout session: " + checkoutSession.ID + " for organization: " + organization.ID.String())

	// Return the checkout session URL and ID
	response := CreateCheckoutSessionResponse{
		URL:       checkoutSession.URL,
		SessionID: checkoutSession.ID,
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Checkout session created successfully",
		"data":    response,
	})
}

// GetCheckoutSession handles GET /api/stripe/checkout-session/:sessionId
func (h *StripeCheckoutHandler) GetCheckoutSession(c *gin.Context) {
	sessionID := c.Param("sessionId")
	if sessionID == "" {
		utils.BadRequest(c, "Session ID is required")
		return
	}

	// Retrieve the checkout session from Stripe
	checkoutSession, err := session.Get(sessionID, nil)
	if err != nil {
		utils.Logger.Error("Failed to retrieve Stripe checkout session: " + err.Error())
		utils.NotFound(c, "Checkout session not found")
		return
	}

	// Return the checkout session details
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Checkout session retrieved successfully",
		"data": gin.H{
			"id":                  checkoutSession.ID,
			"status":              checkoutSession.Status,
			"payment_status":      checkoutSession.PaymentStatus,
			"customer":            checkoutSession.Customer,
			"subscription":        checkoutSession.Subscription,
			"client_reference_id": checkoutSession.ClientReferenceID,
			"metadata":            checkoutSession.Metadata,
		},
	})
}

// ListCheckoutSessions handles GET /api/stripe/checkout-sessions (for admin/debugging)
func (h *StripeCheckoutHandler) ListCheckoutSessions(c *gin.Context) {
	// This endpoint is for admin use only
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		utils.Forbidden(c, "Admin access required")
		return
	}

	// Get query parameters
	limit := int64(10) // Default limit
	if limitParam := c.Query("limit"); limitParam != "" {
		if parsedLimit, err := utils.ParseInt64(limitParam); err == nil && parsedLimit > 0 && parsedLimit <= 100 {
			limit = parsedLimit
		}
	}

	// List checkout sessions from Stripe
	params := &stripe.CheckoutSessionListParams{}
	params.Limit = stripe.Int64(limit)

	iter := session.List(params)
	var sessions []map[string]interface{}

	for iter.Next() {
		s := iter.CheckoutSession()
		sessions = append(sessions, map[string]interface{}{
			"id":                  s.ID,
			"status":              s.Status,
			"payment_status":      s.PaymentStatus,
			"customer":            s.Customer,
			"subscription":        s.Subscription,
			"client_reference_id": s.ClientReferenceID,
			"created":             s.Created,
			"metadata":            s.Metadata,
		})
	}

	if err := iter.Err(); err != nil {
		utils.Logger.Error("Failed to list Stripe checkout sessions: " + err.Error())
		utils.InternalError(c, "Failed to retrieve checkout sessions")
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Checkout sessions retrieved successfully",
		"data": gin.H{
			"sessions": sessions,
			"count":    len(sessions),
		},
	})
}

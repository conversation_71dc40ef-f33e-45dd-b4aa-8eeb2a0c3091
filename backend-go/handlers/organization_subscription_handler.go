package handlers

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"adc-multi-languages/database"
	"adc-multi-languages/models"
	"adc-multi-languages/utils"
)

// OrganizationSubscriptionHandler handles organization subscription-related requests
type OrganizationSubscriptionHandler struct{}

// NewOrganizationSubscriptionHandler creates a new organization subscription handler
func NewOrganizationSubscriptionHandler() *OrganizationSubscriptionHandler {
	return &OrganizationSubscriptionHandler{}
}

// CreateSubscriptionRequest represents the request structure for creating a subscription
type CreateSubscriptionRequest struct {
	SubscriptionPlanID string `json:"subscription_plan_id" binding:"required"`
	BillingPeriod      string `json:"billing_period" binding:"required"` // monthly, yearly
}

// SubscriptionResponse represents the response structure for subscriptions
type SubscriptionResponse struct {
	ID                   string                      `json:"id"`
	OrganizationID       string                      `json:"organization_id"`
	SubscriptionPlan     OrgSubscriptionPlanResponse `json:"subscription_plan"`
	Status               string                      `json:"status"`
	BillingPeriod        string                      `json:"billing_period"`
	CurrentPeriodStart   string                      `json:"current_period_start"`
	CurrentPeriodEnd     string                      `json:"current_period_end"`
	CancelAtPeriodEnd    bool                        `json:"cancel_at_period_end"`
	StripeSubscriptionID *string                     `json:"stripe_subscription_id"`
	TrialStart           *string                     `json:"trial_start"`
	TrialEnd             *string                     `json:"trial_end"`
	CreatedAt            string                      `json:"created_at"`
	UpdatedAt            string                      `json:"updated_at"`
}

// OrgSubscriptionPlanResponse represents the subscription plan in the response
type OrgSubscriptionPlanResponse struct {
	ID                        string   `json:"id"`
	Name                      string   `json:"name"`
	Description               string   `json:"description"`
	PriceMonthly              float64  `json:"price_monthly"`
	PriceYearly               *float64 `json:"price_yearly"`
	AICreditsMonthlyAllowance int      `json:"ai_credits_monthly_allowance"`
	MaxProjects               *int     `json:"max_projects"`
	MaxUsers                  *int     `json:"max_users"`
	Features                  []string `json:"features"`
}

// GetOrganizationSubscription handles GET /api/organizations/:orgId/subscription
func (h *OrganizationSubscriptionHandler) GetOrganizationSubscription(c *gin.Context) {
	orgID := c.Param("orgId")

	// Validate organization ID
	orgUUID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	db := database.GetDB()

	// Check if user has access to the organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ? AND is_active = ?",
		orgUUID, userUUID, true).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Get organization subscription
	var subscription models.OrganizationSubscription
	if err := db.Preload("SubscriptionPlan").First(&subscription, "organization_id = ?", orgUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "No active subscription found for this organization")
			return
		}
		utils.InternalError(c, "Failed to fetch organization subscription")
		return
	}

	response := h.convertToSubscriptionResponse(subscription)
	utils.Success(c, response)
}

// CreateOrganizationSubscription handles POST /api/organizations/:orgId/subscription
func (h *OrganizationSubscriptionHandler) CreateOrganizationSubscription(c *gin.Context) {
	orgID := c.Param("orgId")

	// Validate organization ID
	orgUUID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	var req CreateSubscriptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Validate billing period
	if req.BillingPeriod != "monthly" && req.BillingPeriod != "yearly" {
		utils.BadRequest(c, "Billing period must be 'monthly' or 'yearly'")
		return
	}

	// Validate subscription plan ID
	planUUID, err := uuid.Parse(req.SubscriptionPlanID)
	if err != nil {
		utils.BadRequest(c, "Invalid subscription plan ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	db := database.GetDB()

	// Check if user is an admin of the organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ? AND is_active = ? AND role = ?",
		orgUUID, userUUID, true, "admin").Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have admin access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Check if subscription plan exists
	var plan models.SubscriptionPlan
	if err := db.First(&plan, "id = ? AND is_active = ?", planUUID, true).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Subscription plan not found or inactive")
			return
		}
		utils.InternalError(c, "Failed to fetch subscription plan")
		return
	}

	// Check if organization already has an active subscription
	var existingSubscription models.OrganizationSubscription
	if err := db.First(&existingSubscription, "organization_id = ? AND status = ?", orgUUID, "active").Error; err == nil {
		utils.BadRequest(c, "Organization already has an active subscription")
		return
	} else if err != gorm.ErrRecordNotFound {
		utils.InternalError(c, "Failed to check existing subscription")
		return
	}

	// Calculate period dates
	now := utils.GetCurrentTime()
	var periodEnd time.Time
	if req.BillingPeriod == "monthly" {
		periodEnd = now.AddDate(0, 1, 0)
	} else {
		periodEnd = now.AddDate(1, 0, 0)
	}

	// Create subscription
	subscription := models.OrganizationSubscription{
		OrganizationID:     orgUUID,
		SubscriptionPlanID: planUUID,
		Status:             "active",
		BillingPeriod:      req.BillingPeriod,
		CurrentPeriodStart: now,
		CurrentPeriodEnd:   periodEnd,
		CancelAtPeriodEnd:  false,
	}

	if err := db.Create(&subscription).Error; err != nil {
		utils.InternalError(c, "Failed to create subscription")
		return
	}

	// Reload with subscription plan
	if err := db.Preload("SubscriptionPlan").First(&subscription, subscription.ID).Error; err != nil {
		utils.InternalError(c, "Failed to reload subscription")
		return
	}

	response := h.convertToSubscriptionResponse(subscription)
	utils.SuccessWithMessage(c, response, "Subscription created successfully")
}

// CancelOrganizationSubscription handles DELETE /api/organizations/:orgId/subscription
func (h *OrganizationSubscriptionHandler) CancelOrganizationSubscription(c *gin.Context) {
	orgID := c.Param("orgId")

	// Validate organization ID
	orgUUID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	db := database.GetDB()

	// Check if user is an admin of the organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ? AND is_active = ? AND role = ?",
		orgUUID, userUUID, true, "admin").Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have admin access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Get active subscription
	var subscription models.OrganizationSubscription
	if err := db.First(&subscription, "organization_id = ? AND status = ?", orgUUID, "active").Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "No active subscription found for this organization")
			return
		}
		utils.InternalError(c, "Failed to fetch subscription")
		return
	}

	// Mark subscription for cancellation at period end
	if err := db.Model(&subscription).Update("cancel_at_period_end", true).Error; err != nil {
		utils.InternalError(c, "Failed to cancel subscription")
		return
	}

	utils.SuccessWithMessage(c, nil, "Subscription will be cancelled at the end of the current billing period")
}

// VerifyOrganizationSubscription handles PUT /api/organizations/:orgId/subscription/verify
func (h *OrganizationSubscriptionHandler) VerifyOrganizationSubscription(c *gin.Context) {
	orgID := c.Param("orgId")

	// Validate organization ID
	orgUUID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	db := database.GetDB()

	// Get subscription
	var subscription models.OrganizationSubscription
	if err := db.Preload("SubscriptionPlan").First(&subscription, "organization_id = ?", orgUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "No subscription found for this organization")
			return
		}
		utils.InternalError(c, "Failed to fetch subscription")
		return
	}

	// Check if subscription has expired
	now := utils.GetCurrentTime()
	if subscription.CurrentPeriodEnd.Before(now) && subscription.Status == "active" {
		// Mark as past due
		if err := db.Model(&subscription).Update("status", "past_due").Error; err != nil {
			utils.InternalError(c, "Failed to update subscription status")
			return
		}
		subscription.Status = "past_due"
	}

	response := h.convertToSubscriptionResponse(subscription)
	utils.Success(c, response)
}

// convertToSubscriptionResponse converts a subscription model to response format
func (h *OrganizationSubscriptionHandler) convertToSubscriptionResponse(subscription models.OrganizationSubscription) SubscriptionResponse {
	// Parse features JSON
	features := []string{}
	if subscription.SubscriptionPlan.Features != "" {
		// In a real implementation, you'd parse the JSON
		// For now, we'll use a simple split or default values
		features = []string{"basic_features"} // Placeholder
	}

	planResponse := OrgSubscriptionPlanResponse{
		ID:                        subscription.SubscriptionPlan.ID.String(),
		Name:                      subscription.SubscriptionPlan.Name,
		Description:               subscription.SubscriptionPlan.Description,
		PriceMonthly:              subscription.SubscriptionPlan.PriceMonthly,
		PriceYearly:               subscription.SubscriptionPlan.PriceYearly,
		AICreditsMonthlyAllowance: subscription.SubscriptionPlan.AICreditsMonthlyAllowance,
		MaxProjects:               subscription.SubscriptionPlan.MaxProjects,
		MaxUsers:                  subscription.SubscriptionPlan.MaxUsers,
		Features:                  features,
	}

	response := SubscriptionResponse{
		ID:                   subscription.ID.String(),
		OrganizationID:       subscription.OrganizationID.String(),
		SubscriptionPlan:     planResponse,
		Status:               subscription.Status,
		BillingPeriod:        subscription.BillingPeriod,
		CurrentPeriodStart:   utils.FormatTimestamp(subscription.CurrentPeriodStart),
		CurrentPeriodEnd:     utils.FormatTimestamp(subscription.CurrentPeriodEnd),
		CancelAtPeriodEnd:    subscription.CancelAtPeriodEnd,
		StripeSubscriptionID: subscription.StripeSubscriptionID,
		CreatedAt:            utils.FormatTimestamp(subscription.CreatedAt),
		UpdatedAt:            utils.FormatTimestamp(subscription.UpdatedAt),
	}

	if subscription.TrialStart != nil {
		trialStart := utils.FormatTimestamp(*subscription.TrialStart)
		response.TrialStart = &trialStart
	}

	if subscription.TrialEnd != nil {
		trialEnd := utils.FormatTimestamp(*subscription.TrialEnd)
		response.TrialEnd = &trialEnd
	}

	return response
}

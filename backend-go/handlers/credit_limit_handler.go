package handlers

import (
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"adc-multi-languages/database"
	"adc-multi-languages/models"
	"adc-multi-languages/utils"
)

// CreditLimitHandler handles credit limit-related requests
type CreditLimitHandler struct{}

// NewCreditLimitHandler creates a new credit limit handler
func NewCreditLimitHandler() *CreditLimitHandler {
	return &CreditLimitHandler{}
}

// CreditLimitRequest represents the request structure for updating credit limits
type CreditLimitRequest struct {
	MonthlyLimit         *int  `json:"monthly_limit"`
	WarningThreshold     *int  `json:"warning_threshold"`
	AutoPurchaseEnabled  *bool `json:"auto_purchase_enabled"`
	AutoPurchaseAmount   *int  `json:"auto_purchase_amount"`
}

// CreditLimitResponse represents the response structure for credit limits
type CreditLimitResponse struct {
	ID                   string `json:"id"`
	OrganizationID       string `json:"organization_id"`
	MonthlyLimit         *int   `json:"monthly_limit"`
	WarningThreshold     *int   `json:"warning_threshold"`
	AutoPurchaseEnabled  bool   `json:"auto_purchase_enabled"`
	AutoPurchaseAmount   *int   `json:"auto_purchase_amount"`
	CurrentUsage         int    `json:"current_usage"`
	RemainingCredits     int    `json:"remaining_credits"`
	WarningTriggered     bool   `json:"warning_triggered"`
	CreatedAt            string `json:"created_at"`
	UpdatedAt            string `json:"updated_at"`
}

// GetCreditLimit handles GET /api/organizations/:orgId/credit-limit
func (h *CreditLimitHandler) GetCreditLimit(c *gin.Context) {
	orgID := c.Param("orgId")

	// Validate organization ID
	orgUUID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	db := database.GetDB()

	// Check if user has access to the organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ? AND is_active = ?",
		orgUUID, userUUID, true).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Get or create credit limit
	var creditLimit models.CreditLimit
	err = db.First(&creditLimit, "organization_id = ?", orgUUID).Error
	
	if err == gorm.ErrRecordNotFound {
		// Create default credit limit
		creditLimit = models.CreditLimit{
			OrganizationID:      orgUUID,
			MonthlyLimit:        nil, // No limit by default
			WarningThreshold:    nil,
			AutoPurchaseEnabled: false,
			AutoPurchaseAmount:  nil,
		}

		if err := db.Create(&creditLimit).Error; err != nil {
			utils.InternalError(c, "Failed to create default credit limit")
			return
		}
	} else if err != nil {
		utils.InternalError(c, "Failed to fetch credit limit")
		return
	}

	// Get current usage for this month
	currentUsage := h.getCurrentMonthUsage(db, orgUUID)
	
	// Calculate remaining credits
	remainingCredits := 0
	if creditLimit.MonthlyLimit != nil {
		remainingCredits = *creditLimit.MonthlyLimit - currentUsage
		if remainingCredits < 0 {
			remainingCredits = 0
		}
	}

	// Check if warning should be triggered
	warningTriggered := false
	if creditLimit.WarningThreshold != nil && creditLimit.MonthlyLimit != nil {
		usagePercentage := float64(currentUsage) / float64(*creditLimit.MonthlyLimit) * 100
		warningTriggered = usagePercentage >= float64(*creditLimit.WarningThreshold)
	}

	response := CreditLimitResponse{
		ID:                  creditLimit.ID.String(),
		OrganizationID:      creditLimit.OrganizationID.String(),
		MonthlyLimit:        creditLimit.MonthlyLimit,
		WarningThreshold:    creditLimit.WarningThreshold,
		AutoPurchaseEnabled: creditLimit.AutoPurchaseEnabled,
		AutoPurchaseAmount:  creditLimit.AutoPurchaseAmount,
		CurrentUsage:        currentUsage,
		RemainingCredits:    remainingCredits,
		WarningTriggered:    warningTriggered,
		CreatedAt:           utils.FormatTimestamp(creditLimit.CreatedAt),
		UpdatedAt:           utils.FormatTimestamp(creditLimit.UpdatedAt),
	}

	utils.Success(c, response)
}

// UpdateCreditLimit handles PUT /api/organizations/:orgId/credit-limit
func (h *CreditLimitHandler) UpdateCreditLimit(c *gin.Context) {
	orgID := c.Param("orgId")

	// Validate organization ID
	orgUUID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	var req CreditLimitRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	db := database.GetDB()

	// Check if user is an admin of the organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ? AND is_active = ? AND role = ?",
		orgUUID, userUUID, true, "admin").Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have admin access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Validate request
	if req.MonthlyLimit != nil && *req.MonthlyLimit < 0 {
		utils.BadRequest(c, "Monthly limit cannot be negative")
		return
	}

	if req.WarningThreshold != nil && (*req.WarningThreshold < 0 || *req.WarningThreshold > 100) {
		utils.BadRequest(c, "Warning threshold must be between 0 and 100")
		return
	}

	if req.AutoPurchaseAmount != nil && *req.AutoPurchaseAmount <= 0 {
		utils.BadRequest(c, "Auto purchase amount must be positive")
		return
	}

	// Get or create credit limit
	var creditLimit models.CreditLimit
	err = db.First(&creditLimit, "organization_id = ?", orgUUID).Error
	
	if err == gorm.ErrRecordNotFound {
		// Create new credit limit
		creditLimit = models.CreditLimit{
			OrganizationID:      orgUUID,
			MonthlyLimit:        req.MonthlyLimit,
			WarningThreshold:    req.WarningThreshold,
			AutoPurchaseEnabled: req.AutoPurchaseEnabled != nil && *req.AutoPurchaseEnabled,
			AutoPurchaseAmount:  req.AutoPurchaseAmount,
		}

		if err := db.Create(&creditLimit).Error; err != nil {
			utils.InternalError(c, "Failed to create credit limit")
			return
		}
	} else if err != nil {
		utils.InternalError(c, "Failed to fetch credit limit")
		return
	} else {
		// Update existing credit limit
		updates := map[string]interface{}{}
		
		if req.MonthlyLimit != nil {
			updates["monthly_limit"] = req.MonthlyLimit
		}
		if req.WarningThreshold != nil {
			updates["warning_threshold"] = req.WarningThreshold
		}
		if req.AutoPurchaseEnabled != nil {
			updates["auto_purchase_enabled"] = *req.AutoPurchaseEnabled
		}
		if req.AutoPurchaseAmount != nil {
			updates["auto_purchase_amount"] = req.AutoPurchaseAmount
		}

		if err := db.Model(&creditLimit).Updates(updates).Error; err != nil {
			utils.InternalError(c, "Failed to update credit limit")
			return
		}

		// Reload the updated record
		if err := db.First(&creditLimit, "organization_id = ?", orgUUID).Error; err != nil {
			utils.InternalError(c, "Failed to reload credit limit")
			return
		}
	}

	// Get current usage for response
	currentUsage := h.getCurrentMonthUsage(db, orgUUID)
	
	// Calculate remaining credits
	remainingCredits := 0
	if creditLimit.MonthlyLimit != nil {
		remainingCredits = *creditLimit.MonthlyLimit - currentUsage
		if remainingCredits < 0 {
			remainingCredits = 0
		}
	}

	// Check if warning should be triggered
	warningTriggered := false
	if creditLimit.WarningThreshold != nil && creditLimit.MonthlyLimit != nil {
		usagePercentage := float64(currentUsage) / float64(*creditLimit.MonthlyLimit) * 100
		warningTriggered = usagePercentage >= float64(*creditLimit.WarningThreshold)
	}

	response := CreditLimitResponse{
		ID:                  creditLimit.ID.String(),
		OrganizationID:      creditLimit.OrganizationID.String(),
		MonthlyLimit:        creditLimit.MonthlyLimit,
		WarningThreshold:    creditLimit.WarningThreshold,
		AutoPurchaseEnabled: creditLimit.AutoPurchaseEnabled,
		AutoPurchaseAmount:  creditLimit.AutoPurchaseAmount,
		CurrentUsage:        currentUsage,
		RemainingCredits:    remainingCredits,
		WarningTriggered:    warningTriggered,
		CreatedAt:           utils.FormatTimestamp(creditLimit.CreatedAt),
		UpdatedAt:           utils.FormatTimestamp(creditLimit.UpdatedAt),
	}

	utils.SuccessWithMessage(c, response, "Credit limit updated successfully")
}

// getCurrentMonthUsage calculates the current month's credit usage for an organization
func (h *CreditLimitHandler) getCurrentMonthUsage(db *gorm.DB, orgID uuid.UUID) int {
	var totalUsage int64

	// Get the start of the current month
	now := utils.GetCurrentTime()
	startOfMonth := utils.GetStartOfMonth(now)

	// Sum up all credit usage for this month
	db.Model(&models.AICreditUsage{}).
		Where("organization_id = ? AND created_at >= ?", orgID, startOfMonth).
		Select("COALESCE(SUM(credits_used), 0)").
		Scan(&totalUsage)

	return int(totalUsage)
}

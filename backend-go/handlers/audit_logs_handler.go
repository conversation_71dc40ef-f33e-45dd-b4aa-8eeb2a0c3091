package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"adc-multi-languages/database"
	"adc-multi-languages/models"
	"adc-multi-languages/utils"
)

// AuditLogsHandler handles audit logs-related requests
type AuditLogsHandler struct{}

// NewAuditLogsHandler creates a new audit logs handler
func NewAuditLogsHandler() *AuditLogsHandler {
	return &AuditLogsHandler{}
}

// AuditLogResponse represents the response structure for audit logs
type AuditLogResponse struct {
	ID           string  `json:"id"`
	UserID       string  `json:"user_id"`
	UserEmail    string  `json:"user_email"`
	Action       string  `json:"action"`
	ResourceType string  `json:"resource_type"`
	ResourceID   *string `json:"resource_id"`
	Details      string  `json:"details"`
	IPAddress    *string `json:"ip_address"`
	UserAgent    *string `json:"user_agent"`
	Success      bool    `json:"success"`
	ErrorMessage *string `json:"error_message"`
	CreatedAt    string  `json:"created_at"`
}

// GetOrganizationAuditLogs handles GET /api/organizations/:orgId/audit-logs
func (h *AuditLogsHandler) GetOrganizationAuditLogs(c *gin.Context) {
	orgID := c.Param("orgId")

	// Validate organization ID
	orgUUID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	db := database.GetDB()

	// Check if user has admin access to the organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ? AND is_active = ? AND role = ?",
		orgUUID, userUUID, true, "admin").Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have admin access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 50
	}

	// Parse filter parameters
	action := c.Query("action")
	resourceType := c.Query("resource_type")
	userFilter := c.Query("user_id")
	fromDate := c.Query("from_date")
	toDate := c.Query("to_date")

	// Build query for audit logs
	query := db.Model(&models.AuditLog{}).
		Joins("JOIN organization_memberships om ON audit_logs.user_id = om.user_id").
		Where("om.organization_id = ? AND om.is_active = ?", orgUUID, true)

	// Apply filters
	if action != "" {
		query = query.Where("audit_logs.action = ?", action)
	}
	if resourceType != "" {
		query = query.Where("audit_logs.resource_type = ?", resourceType)
	}
	if userFilter != "" {
		if userFilterUUID, err := uuid.Parse(userFilter); err == nil {
			query = query.Where("audit_logs.user_id = ?", userFilterUUID)
		}
	}
	if fromDate != "" {
		if parsedDate, err := utils.ParseDateString(fromDate); err == nil {
			query = query.Where("audit_logs.created_at >= ?", parsedDate)
		}
	}
	if toDate != "" {
		if parsedDate, err := utils.ParseDateString(toDate); err == nil {
			query = query.Where("audit_logs.created_at <= ?", utils.GetEndOfDay(parsedDate))
		}
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		utils.InternalError(c, "Failed to count audit logs")
		return
	}

	// Get audit logs with pagination
	var auditLogs []models.AuditLog
	offset := (page - 1) * limit
	if err := query.Preload("User").
		Order("audit_logs.created_at DESC").
		Offset(offset).Limit(limit).
		Find(&auditLogs).Error; err != nil {
		utils.InternalError(c, "Failed to fetch audit logs")
		return
	}

	// Convert to response format
	var responses []AuditLogResponse
	for _, log := range auditLogs {
		response := AuditLogResponse{
			ID:           log.ID.String(),
			UserID:       log.UserID.String(),
			UserEmail:    log.User.Email,
			Action:       log.Action,
			ResourceType: log.ResourceType,
			Details:      log.Details,
			IPAddress:    log.IPAddress,
			UserAgent:    log.UserAgent,
			Success:      log.Success,
			ErrorMessage: log.ErrorMessage,
			CreatedAt:    utils.FormatTimestamp(log.CreatedAt),
		}

		if log.ResourceID != nil {
			resourceID := log.ResourceID.String()
			response.ResourceID = &resourceID
		}

		responses = append(responses, response)
	}

	utils.SuccessWithPagination(c, responses, uint32(page), uint32(limit), uint64(total))
}

// GetAPIKeyAuditLogs handles GET /api/organizations/:orgId/api-keys/:id/audit-logs
func (h *AuditLogsHandler) GetAPIKeyAuditLogs(c *gin.Context) {
	orgID := c.Param("orgId")
	apiKeyID := c.Param("id")

	// Validate IDs
	orgUUID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	apiKeyUUID, err := uuid.Parse(apiKeyID)
	if err != nil {
		utils.BadRequest(c, "Invalid API key ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	db := database.GetDB()

	// Check if user has access to the organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ? AND is_active = ?",
		orgUUID, userUUID, true).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Check if API key exists and belongs to the organization
	var apiKey models.APIKey
	if err := db.First(&apiKey, "id = ? AND organization_id = ?", apiKeyUUID, orgUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "API key not found")
			return
		}
		utils.InternalError(c, "Failed to fetch API key")
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 50
	}

	// Parse filter parameters
	action := c.Query("action")
	fromDate := c.Query("from_date")
	toDate := c.Query("to_date")

	// Build query for API key specific audit logs
	query := db.Model(&models.AuditLog{}).
		Where("resource_type = ? AND resource_id = ?", "api_key", apiKeyUUID)

	// Apply filters
	if action != "" {
		query = query.Where("action = ?", action)
	}
	if fromDate != "" {
		if parsedDate, err := utils.ParseDateString(fromDate); err == nil {
			query = query.Where("created_at >= ?", parsedDate)
		}
	}
	if toDate != "" {
		if parsedDate, err := utils.ParseDateString(toDate); err == nil {
			query = query.Where("created_at <= ?", utils.GetEndOfDay(parsedDate))
		}
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		utils.InternalError(c, "Failed to count audit logs")
		return
	}

	// Get audit logs with pagination
	var auditLogs []models.AuditLog
	offset := (page - 1) * limit
	if err := query.Preload("User").
		Order("created_at DESC").
		Offset(offset).Limit(limit).
		Find(&auditLogs).Error; err != nil {
		utils.InternalError(c, "Failed to fetch audit logs")
		return
	}

	// Convert to response format
	var responses []AuditLogResponse
	for _, log := range auditLogs {
		response := AuditLogResponse{
			ID:           log.ID.String(),
			UserID:       log.UserID.String(),
			UserEmail:    log.User.Email,
			Action:       log.Action,
			ResourceType: log.ResourceType,
			Details:      log.Details,
			IPAddress:    log.IPAddress,
			UserAgent:    log.UserAgent,
			Success:      log.Success,
			ErrorMessage: log.ErrorMessage,
			CreatedAt:    utils.FormatTimestamp(log.CreatedAt),
		}

		if log.ResourceID != nil {
			resourceID := log.ResourceID.String()
			response.ResourceID = &resourceID
		}

		responses = append(responses, response)
	}

	// Add API key information to response
	responseData := map[string]interface{}{
		"api_key": map[string]interface{}{
			"id":   apiKey.ID.String(),
			"name": apiKey.Name,
		},
		"audit_logs": responses,
		"pagination": map[string]interface{}{
			"page":  page,
			"limit": limit,
			"total": total,
		},
	}

	utils.Success(c, responseData)
}

// GetAuditLogActions handles GET /api/audit-logs/actions
func (h *AuditLogsHandler) GetAuditLogActions(c *gin.Context) {
	actions := map[string][]string{
		"authentication": {
			"login",
			"logout",
			"password_reset",
			"email_verification",
			"failed_login",
		},
		"organization": {
			"create_organization",
			"update_organization",
			"delete_organization",
			"add_member",
			"remove_member",
			"update_member_role",
		},
		"project": {
			"create_project",
			"update_project",
			"delete_project",
			"add_locale",
			"remove_locale",
		},
		"translation": {
			"create_translation_key",
			"update_translation_key",
			"delete_translation_key",
			"create_translation",
			"update_translation",
			"delete_translation",
		},
		"api_key": {
			"create_api_key",
			"update_api_key",
			"delete_api_key",
			"regenerate_api_key",
			"api_key_usage",
		},
		"ai_credits": {
			"purchase_credits",
			"use_credits",
			"update_credit_limit",
		},
		"subscription": {
			"create_subscription",
			"update_subscription",
			"cancel_subscription",
		},
	}

	utils.Success(c, actions)
}

// GetAuditLogResourceTypes handles GET /api/audit-logs/resource-types
func (h *AuditLogsHandler) GetAuditLogResourceTypes(c *gin.Context) {
	resourceTypes := []string{
		"user",
		"organization",
		"project",
		"translation_key",
		"translation",
		"api_key",
		"permission_group",
		"subscription",
		"ai_credits",
		"locale",
	}

	utils.Success(c, resourceTypes)
}

package handlers

import (
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"adc-multi-languages/database"
	"adc-multi-languages/models"
	"adc-multi-languages/services"
	"adc-multi-languages/utils"
)

// TestHandler handles test-related requests (development only)
type TestHandler struct {
	emailService *services.EmailService
}

// NewTestHandler creates a new test handler
func NewTestHandler() *TestHandler {
	return &TestHandler{
		emailService: services.NewEmailService(),
	}
}

// TestEmailRequest represents the request structure for testing email
type TestEmailRequest struct {
	Email string `json:"email" binding:"required,email"`
	Type  string `json:"type" binding:"required"` // "password_reset" or "email_verification"
}

// TestEmail handles POST /api/test/email
func (h *TestHandler) TestEmail(c *gin.Context) {
	var req TestEmailRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Generate a test token
	testToken := uuid.New().String()

	var err error
	switch req.Type {
	case "password_reset":
		err = h.emailService.SendPasswordResetEmail(req.Email, "Test User", testToken)
	case "email_verification":
		err = h.emailService.SendEmailVerificationEmail(req.Email, "Test User", testToken)
	default:
		utils.BadRequest(c, "Invalid email type. Use 'password_reset' or 'email_verification'")
		return
	}

	if err != nil {
		utils.InternalError(c, "Failed to send test email: "+err.Error())
		return
	}

	utils.SuccessWithMessage(c, map[string]interface{}{
		"email":      req.Email,
		"type":       req.Type,
		"test_token": testToken,
	}, "Test email sent successfully")
}

// TestVerificationEmail handles POST /api/test/verification-email
func (h *TestHandler) TestVerificationEmail(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	// Get user from database
	db := database.GetDB()
	var user models.User
	if err := db.First(&user, "id = ?", userUUID).Error; err != nil {
		utils.InternalError(c, "Failed to fetch user")
		return
	}

	// Generate verification token
	verificationToken := uuid.New().String()

	// Send verification email
	userName := "User"
	if user.FirstName != nil {
		userName = *user.FirstName
	}

	err = h.emailService.SendEmailVerificationEmail(user.Email, userName, verificationToken)
	if err != nil {
		utils.InternalError(c, "Failed to send verification email: "+err.Error())
		return
	}

	utils.SuccessWithMessage(c, map[string]interface{}{
		"email":              user.Email,
		"verification_token": verificationToken,
	}, "Verification email sent successfully")
}

// TestDatabaseConnection handles GET /api/test/database
func (h *TestHandler) TestDatabaseConnection(c *gin.Context) {
	db := database.GetDB()

	// Test basic connection
	sqlDB, err := db.DB()
	if err != nil {
		utils.InternalError(c, "Failed to get database instance: "+err.Error())
		return
	}

	if err := sqlDB.Ping(); err != nil {
		utils.InternalError(c, "Database ping failed: "+err.Error())
		return
	}

	// Test a simple query
	var count int64
	if err := db.Model(&models.User{}).Count(&count).Error; err != nil {
		utils.InternalError(c, "Failed to query database: "+err.Error())
		return
	}

	utils.Success(c, map[string]interface{}{
		"status":     "connected",
		"user_count": count,
		"message":    "Database connection is healthy",
	})
}

// TestCreateSampleData handles POST /api/test/sample-data
func (h *TestHandler) TestCreateSampleData(c *gin.Context) {
	db := database.GetDB()

	// Create sample locales if they don't exist
	locales := []models.Locale{
		{Code: "en", Name: "English", NativeName: "English", Direction: "ltr", IsActive: true},
		{Code: "es", Name: "Spanish", NativeName: "Español", Direction: "ltr", IsActive: true},
		{Code: "fr", Name: "French", NativeName: "Français", Direction: "ltr", IsActive: true},
		{Code: "de", Name: "German", NativeName: "Deutsch", Direction: "ltr", IsActive: true},
		{Code: "ja", Name: "Japanese", NativeName: "日本語", Direction: "ltr", IsActive: true},
		{Code: "ar", Name: "Arabic", NativeName: "العربية", Direction: "rtl", IsActive: true},
	}

	var createdLocales []models.Locale
	for _, locale := range locales {
		var existingLocale models.Locale
		if err := db.Where("code = ?", locale.Code).First(&existingLocale).Error; err != nil {
			// Locale doesn't exist, create it
			if err := db.Create(&locale).Error; err != nil {
				utils.InternalError(c, "Failed to create locale: "+err.Error())
				return
			}
			createdLocales = append(createdLocales, locale)
		}
	}

	// Create sample subscription plans if they don't exist
	plans := []models.SubscriptionPlan{
		{
			Name:                      "Free",
			Description:               "Basic plan for individuals",
			PriceMonthly:              0.0,
			AICreditsMonthlyAllowance: 1000,
			MaxProjects:               intPtr(3),
			MaxUsers:                  intPtr(1),
			Features:                  `["basic_translation", "3_projects", "1000_ai_credits"]`,
			IsActive:                  true,
		},
		{
			Name:                      "Pro",
			Description:               "Professional plan for small teams",
			PriceMonthly:              29.99,
			PriceYearly:               &[]float64{299.99}[0],
			AICreditsMonthlyAllowance: 10000,
			MaxProjects:               &[]int{25}[0],
			MaxUsers:                  &[]int{5}[0],
			Features:                  `["advanced_translation", "25_projects", "10000_ai_credits", "team_collaboration"]`,
			IsActive:                  true,
		},
		{
			Name:                      "Enterprise",
			Description:               "Enterprise plan for large organizations",
			PriceMonthly:              99.99,
			PriceYearly:               &[]float64{999.99}[0],
			AICreditsMonthlyAllowance: 50000,
			Features:                  `["unlimited_projects", "unlimited_users", "50000_ai_credits", "priority_support", "custom_integrations"]`,
			IsActive:                  true,
		},
	}

	var createdPlans []models.SubscriptionPlan
	for _, plan := range plans {
		var existingPlan models.SubscriptionPlan
		if err := db.Where("name = ?", plan.Name).First(&existingPlan).Error; err != nil {
			// Plan doesn't exist, create it
			if err := db.Create(&plan).Error; err != nil {
				utils.InternalError(c, "Failed to create subscription plan: "+err.Error())
				return
			}
			createdPlans = append(createdPlans, plan)
		}
	}

	utils.SuccessWithMessage(c, map[string]interface{}{
		"created_locales": len(createdLocales),
		"created_plans":   len(createdPlans),
		"locales":         createdLocales,
		"plans":           createdPlans,
	}, "Sample data created successfully")
}

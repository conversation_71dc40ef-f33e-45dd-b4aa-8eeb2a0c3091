package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"adc-multi-languages/database"
	"adc-multi-languages/models"
	"adc-multi-languages/services"
	"adc-multi-languages/utils"
)

// AITranslationHandler handles AI translation-related requests
type AITranslationHandler struct {
	translationService *services.TranslationService
	cacheService       *services.CacheService
}

// NewAITranslationHandler creates a new AI translation handler
func NewAITranslationHandler() *AITranslationHandler {
	return &AITranslationHandler{
		translationService: services.NewTranslationService(),
		cacheService:       services.GetCacheService(),
	}
}

// TranslateTextRequest represents the request structure for text translation
type TranslateTextRequest struct {
	Text         string `json:"text" binding:"required"`
	SourceLang   string `json:"source_lang" binding:"required"`
	TargetLang   string `json:"target_lang" binding:"required"`
	Context      string `json:"context"`
	Provider     string `json:"provider"`
	Tone         string `json:"tone"`
	PreserveHTML bool   `json:"preserve_html"`
	ProjectID    string `json:"project_id"`
}

// DetectLanguageRequest represents the request structure for language detection
type DetectLanguageRequest struct {
	Text     string `json:"text" binding:"required"`
	Provider string `json:"provider"`
}

// TranslateText handles POST /api/ai/translate
func (h *AITranslationHandler) TranslateText(c *gin.Context) {
	var req TranslateTextRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	db := database.GetDB()

	// Get user and check if active
	var user models.User
	if err := db.First(&user, "id = ?", userUUID).Error; err != nil {
		utils.InternalError(c, "Failed to fetch user")
		return
	}

	// If project ID is provided, validate access and get organization
	var organizationID uuid.UUID
	if req.ProjectID != "" {
		projectUUID, err := uuid.Parse(req.ProjectID)
		if err != nil {
			utils.BadRequest(c, "Invalid project ID format")
			return
		}

		var project models.Project
		if err := db.First(&project, "id = ?", projectUUID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				utils.NotFound(c, "Project not found")
				return
			}
			utils.InternalError(c, "Failed to fetch project")
			return
		}

		// Check if user has access to the project's organization
		var membership models.OrganizationMembership
		if err := db.First(&membership, "organization_id = ? AND user_id = ? AND is_active = ?",
			project.OrganizationID, userUUID, true).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				utils.Forbidden(c, "User does not have access to this project")
				return
			}
			utils.InternalError(c, "Failed to check organization membership")
			return
		}

		organizationID = project.OrganizationID
	} else {
		// If no project specified, we need to find a default organization for the user
		var membership models.OrganizationMembership
		if err := db.First(&membership, "user_id = ? AND is_active = ?", userUUID, true).Error; err != nil {
			utils.BadRequest(c, "No organization found for user. Please specify a project_id.")
			return
		}
		organizationID = membership.OrganizationID
	}

	// Check AI credits
	var organization models.Organization
	if err := db.First(&organization, "id = ?", organizationID).Error; err != nil {
		utils.InternalError(c, "Failed to fetch organization")
		return
	}

	// Calculate credits needed (rough estimate: 1 credit per 10 characters)
	creditsNeeded := (len(req.Text) + 9) / 10
	if creditsNeeded < 1 {
		creditsNeeded = 1
	}

	remainingCredits := 0
	if organization.AICreditsRemaining != nil {
		remainingCredits = *organization.AICreditsRemaining
	}

	if remainingCredits < creditsNeeded {
		utils.BadRequest(c, "Insufficient AI credits")
		return
	}

	// Perform translation
	translationReq := services.TranslationRequest{
		Text:         req.Text,
		SourceLang:   req.SourceLang,
		TargetLang:   req.TargetLang,
		Context:      req.Context,
		Provider:     req.Provider,
		Tone:         req.Tone,
		PreserveHTML: req.PreserveHTML,
	}

	response, err := h.translationService.Translate(translationReq)
	if err != nil {
		utils.BadRequest(c, "Translation failed: "+err.Error())
		return
	}

	// Deduct credits and log usage
	tx := db.Begin()

	// Update organization credits
	newBalance := remainingCredits - creditsNeeded
	if err := tx.Model(&organization).Update("ai_credits_remaining", newBalance).Error; err != nil {
		tx.Rollback()
		utils.InternalError(c, "Failed to update AI credits")
		return
	}

	// Log credit transaction
	creditTransaction := models.AICreditTransaction{
		OrganizationID:  organizationID,
		TransactionType: "usage",
		Amount:          -creditsNeeded,
		BalanceBefore:   remainingCredits,
		BalanceAfter:    newBalance,
		Description:     "AI translation",
		PerformedBy:     &userUUID,
	}

	if err := tx.Create(&creditTransaction).Error; err != nil {
		tx.Rollback()
		utils.InternalError(c, "Failed to log credit transaction")
		return
	}

	// Log usage details
	var projectUUID *uuid.UUID
	if req.ProjectID != "" {
		parsed, _ := uuid.Parse(req.ProjectID)
		projectUUID = &parsed
	}

	creditUsage := models.AICreditUsage{
		OrganizationID:   organizationID,
		UserID:           &userUUID,
		ProjectID:        projectUUID,
		CreditsUsed:      creditsNeeded,
		Operation:        "translate",
		SourceLocale:     &req.SourceLang,
		TargetLocale:     &req.TargetLang,
		TextLength:       &response.CharacterCount,
		ModelUsed:        &response.Provider,
		RemainingCredits: newBalance,
	}

	if err := tx.Create(&creditUsage).Error; err != nil {
		tx.Rollback()
		utils.InternalError(c, "Failed to log credit usage")
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		utils.InternalError(c, "Failed to complete transaction")
		return
	}

	// Add billing information to response
	responseData := map[string]interface{}{
		"translation":       response,
		"credits_used":      creditsNeeded,
		"remaining_credits": newBalance,
		"organization_id":   organizationID.String(),
	}

	utils.SuccessWithMessage(c, responseData, "Translation completed successfully")
}

// DetectLanguage handles POST /api/ai/detect-language
func (h *AITranslationHandler) DetectLanguage(c *gin.Context) {
	var req DetectLanguageRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Get user ID from context (for authentication)
	_, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	// Detect language
	language, confidence, err := h.translationService.DetectLanguage(req.Text, req.Provider)
	if err != nil {
		utils.BadRequest(c, "Language detection failed: "+err.Error())
		return
	}

	responseData := map[string]interface{}{
		"detected_language": language,
		"confidence":        confidence,
		"provider":          req.Provider,
		"text_length":       len(req.Text),
	}

	utils.Success(c, responseData)
}

// GetSupportedLanguages handles GET /api/ai/supported-languages
func (h *AITranslationHandler) GetSupportedLanguages(c *gin.Context) {
	provider := c.DefaultQuery("provider", "openai")

	// Check cache first
	var languages []string
	if h.cacheService.GetCachedLocales(&languages) {
		utils.Success(c, map[string]interface{}{
			"provider":  provider,
			"languages": languages,
			"cached":    true,
		})
		return
	}

	// Get from translation service
	languages, err := h.translationService.GetSupportedLanguages(provider)
	if err != nil {
		utils.BadRequest(c, "Failed to get supported languages: "+err.Error())
		return
	}

	// Cache the result
	h.cacheService.CacheLocales(languages)

	responseData := map[string]interface{}{
		"provider":  provider,
		"languages": languages,
		"count":     len(languages),
		"cached":    false,
	}

	utils.Success(c, responseData)
}

// GetTranslationHistory handles GET /api/ai/translation-history
func (h *AITranslationHandler) GetTranslationHistory(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	db := database.GetDB()

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// Parse filter parameters
	organizationID := c.Query("organization_id")
	projectID := c.Query("project_id")
	operation := c.Query("operation")

	// Build query
	query := db.Model(&models.AICreditUsage{}).Where("user_id = ?", userUUID)

	if organizationID != "" {
		if orgUUID, err := uuid.Parse(organizationID); err == nil {
			query = query.Where("organization_id = ?", orgUUID)
		}
	}

	if projectID != "" {
		if projUUID, err := uuid.Parse(projectID); err == nil {
			query = query.Where("project_id = ?", projUUID)
		}
	}

	if operation != "" {
		query = query.Where("operation = ?", operation)
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		utils.InternalError(c, "Failed to count translation history")
		return
	}

	// Get usage records with pagination
	var usageRecords []models.AICreditUsage
	offset := (page - 1) * limit
	if err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&usageRecords).Error; err != nil {
		utils.InternalError(c, "Failed to fetch translation history")
		return
	}

	// Convert to response format
	var responses []map[string]interface{}
	for _, usage := range usageRecords {
		response := map[string]interface{}{
			"id":                usage.ID.String(),
			"credits_used":      usage.CreditsUsed,
			"operation":         usage.Operation,
			"source_locale":     usage.SourceLocale,
			"target_locale":     usage.TargetLocale,
			"text_length":       usage.TextLength,
			"model_used":        usage.ModelUsed,
			"remaining_credits": usage.RemainingCredits,
			"created_at":        utils.FormatTimestamp(usage.CreatedAt),
		}

		if usage.OrganizationID != uuid.Nil {
			response["organization_id"] = usage.OrganizationID.String()
		}

		if usage.ProjectID != nil {
			response["project_id"] = usage.ProjectID.String()
		}

		responses = append(responses, response)
	}

	utils.SuccessWithPagination(c, responses, uint32(page), uint32(limit), uint64(total))
}

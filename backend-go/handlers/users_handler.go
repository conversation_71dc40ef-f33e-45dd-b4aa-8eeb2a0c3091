package handlers

import (
	"time"

	"adc-multi-languages/database"
	"adc-multi-languages/middleware"
	"adc-multi-languages/models"
	"adc-multi-languages/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// UserHandler handles user-related routes
type UserHandler struct{}

// NewUserHandler creates a new user handler
func NewUserHandler() *UserHandler {
	return &UserHandler{}
}

// Request structures
type UpdateProfileRequest struct {
	FullName          *string `json:"full_name"`
	PreferredLanguage *string `json:"preferred_language"`
	ProfilePictureURL *string `json:"profile_picture_url"`
	Timezone          *string `json:"timezone"`
}

// Response structures
type UserProfileResponse struct {
	ID                string     `json:"id"`
	Email             string     `json:"email"`
	Username          string     `json:"username"`
	FullName          *string    `json:"full_name"`
	ProfileImageURL   *string    `json:"profile_image_url"`
	PreferredLanguage *string    `json:"preferred_language"`
	Timezone          *string    `json:"timezone"`
	EmailVerified     bool       `json:"email_verified"`
	IsActive          bool       `json:"is_active"`
	LastLoginAt       *time.Time `json:"last_login_at"`
	CreatedAt         time.Time  `json:"created_at"`
	UpdatedAt         time.Time  `json:"updated_at"`
}

// GetProfile handles GET /api/users/profile
func (h *UserHandler) GetProfile(c *gin.Context) {
	// Get authenticated user from context
	user, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Find user by ID
	var dbUser models.User
	if err := db.Where("id = ?", user.ID).First(&dbUser).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "User profile not found")
			return
		}
		utils.LogError(c, "Database error getting user profile", err, nil)
		utils.InternalServerError(c, "Failed to retrieve user profile")
		return
	}

	// Convert to response format
	response := UserProfileResponse{
		ID:                dbUser.ID.String(),
		Email:             dbUser.Email,
		Username:          getUsername(dbUser),
		FullName:          dbUser.FirstName,
		ProfileImageURL:   dbUser.ProfilePictureURL,
		PreferredLanguage: dbUser.Language,
		Timezone:          dbUser.Timezone,
		EmailVerified:     dbUser.EmailVerified,
		IsActive:          dbUser.IsActive,
		LastLoginAt:       dbUser.LastLoginAt,
		CreatedAt:         dbUser.CreatedAt,
		UpdatedAt:         dbUser.UpdatedAt,
	}

	utils.Success(c, response)
}

// UpdateProfile handles PUT /api/users/profile
func (h *UserHandler) UpdateProfile(c *gin.Context) {
	// Get authenticated user from context
	user, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	var req UpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Find user by ID
	var dbUser models.User
	if err := db.Where("id = ?", user.ID).First(&dbUser).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "User profile not found")
			return
		}
		utils.LogError(c, "Database error finding user", err, nil)
		utils.InternalServerError(c, "Failed to find user profile")
		return
	}

	// Prepare update data
	updates := map[string]interface{}{
		"updated_at": time.Now(),
	}

	// Only update fields that are provided
	if req.FullName != nil {
		updates["first_name"] = req.FullName
	}
	if req.PreferredLanguage != nil {
		updates["language"] = req.PreferredLanguage
	}
	if req.ProfilePictureURL != nil {
		updates["profile_picture_url"] = req.ProfilePictureURL
	}
	if req.Timezone != nil {
		updates["timezone"] = req.Timezone
	}

	// Update user
	if err := db.Model(&dbUser).Updates(updates).Error; err != nil {
		utils.LogError(c, "Failed to update user profile", err, nil)
		utils.InternalServerError(c, "Failed to update profile")
		return
	}

	// Reload user to get updated data
	if err := db.Where("id = ?", user.ID).First(&dbUser).Error; err != nil {
		utils.LogError(c, "Failed to reload updated user", err, nil)
		utils.InternalServerError(c, "Failed to retrieve updated profile")
		return
	}

	// Convert to response format
	response := UserProfileResponse{
		ID:                dbUser.ID.String(),
		Email:             dbUser.Email,
		Username:          getUsername(dbUser),
		FullName:          dbUser.FirstName,
		ProfileImageURL:   dbUser.ProfilePictureURL,
		PreferredLanguage: dbUser.Language,
		Timezone:          dbUser.Timezone,
		EmailVerified:     dbUser.EmailVerified,
		IsActive:          dbUser.IsActive,
		LastLoginAt:       dbUser.LastLoginAt,
		CreatedAt:         dbUser.CreatedAt,
		UpdatedAt:         dbUser.UpdatedAt,
	}

	utils.SuccessWithMessage(c, response, "Profile updated successfully")
}

// GetUser handles GET /api/users/:id
func (h *UserHandler) GetUser(c *gin.Context) {
	userIDStr := c.Param("id")

	// Parse UUID
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		utils.BadRequest(c, "Invalid user ID format")
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Find user by ID
	var dbUser models.User
	if err := db.Where("id = ? AND is_active = ?", userID, true).First(&dbUser).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "User not found")
			return
		}
		utils.LogError(c, "Database error getting user", err, nil)
		utils.InternalServerError(c, "Failed to retrieve user")
		return
	}

	// Convert to response format (public profile - limited info)
	response := UserProfileResponse{
		ID:                dbUser.ID.String(),
		Email:             dbUser.Email,
		Username:          getUsername(dbUser),
		FullName:          dbUser.FirstName,
		ProfileImageURL:   dbUser.ProfilePictureURL,
		PreferredLanguage: dbUser.Language,
		EmailVerified:     dbUser.EmailVerified,
		CreatedAt:         dbUser.CreatedAt,
		// Note: Not including sensitive info like LastLoginAt, IsActive for public profile
	}

	utils.Success(c, response)
}

// Helper functions

// getUsername returns the username or email as fallback
func getUsername(user models.User) string {
	if user.Username != nil && *user.Username != "" {
		return *user.Username
	}
	return user.Email
}

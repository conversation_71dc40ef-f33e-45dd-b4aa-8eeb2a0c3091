package handlers

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"adc-multi-languages/database"
	"adc-multi-languages/models"
	"adc-multi-languages/utils"
)

// CreditUsageHandler handles credit usage analytics-related requests
type CreditUsageHandler struct{}

// NewCreditUsageHandler creates a new credit usage handler
func NewCreditUsageHandler() *CreditUsageHandler {
	return &CreditUsageHandler{}
}

// CreditUsageResponse represents the response structure for credit usage
type CreditUsageResponse struct {
	ID               string  `json:"id"`
	OrganizationID   string  `json:"organization_id"`
	UserID           *string `json:"user_id"`
	ProjectID        *string `json:"project_id"`
	CreditsUsed      int     `json:"credits_used"`
	Operation        string  `json:"operation"`
	SourceLocale     *string `json:"source_locale"`
	TargetLocale     *string `json:"target_locale"`
	TextLength       *int    `json:"text_length"`
	ModelUsed        *string `json:"model_used"`
	RemainingCredits int     `json:"remaining_credits"`
	CreatedAt        string  `json:"created_at"`
}

// CreditUsageAnalyticsResponse represents the response structure for credit usage analytics
type CreditUsageAnalyticsResponse struct {
	TotalCreditsUsed     int                    `json:"total_credits_used"`
	TotalOperations      int                    `json:"total_operations"`
	AverageCreditsPerOp  float64                `json:"average_credits_per_operation"`
	ByOperation          map[string]int         `json:"by_operation"`
	ByLocale             map[string]int         `json:"by_locale"`
	ByModel              map[string]int         `json:"by_model"`
	TimeSeriesData       []TimeSeriesData       `json:"time_series_data"`
	TopUsers             []UserUsageData        `json:"top_users"`
	TopProjects          []ProjectUsageData     `json:"top_projects"`
}

// TimeSeriesData represents time series data for credit usage
type TimeSeriesData struct {
	Date         string                 `json:"date"`
	TotalCredits int                    `json:"total_credits"`
	ByOperation  map[string]int         `json:"by_operation"`
	ByLocale     map[string]int         `json:"by_locale"`
}

// UserUsageData represents usage data by user
type UserUsageData struct {
	UserID       string `json:"user_id"`
	UserEmail    string `json:"user_email"`
	CreditsUsed  int    `json:"credits_used"`
	OperationCount int  `json:"operation_count"`
}

// ProjectUsageData represents usage data by project
type ProjectUsageData struct {
	ProjectID    string `json:"project_id"`
	ProjectName  string `json:"project_name"`
	CreditsUsed  int    `json:"credits_used"`
	OperationCount int  `json:"operation_count"`
}

// GetCreditUsageHistory handles GET /api/organizations/:orgId/credit-usage
func (h *CreditUsageHandler) GetCreditUsageHistory(c *gin.Context) {
	orgID := c.Param("orgId")

	// Validate organization ID
	orgUUID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Parse filter parameters
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	operation := c.Query("operation")

	db := database.GetDB()

	// Check if user has access to the organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ? AND is_active = ?",
		orgUUID, userUUID, true).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Build query for credit usage
	query := db.Model(&models.AICreditUsage{}).Where("organization_id = ?", orgUUID)

	// Apply filters
	if operation != "" {
		query = query.Where("operation = ?", operation)
	}
	if startDate != "" {
		if parsedDate, err := utils.ParseDateString(startDate); err == nil {
			query = query.Where("created_at >= ?", parsedDate)
		}
	}
	if endDate != "" {
		if parsedDate, err := utils.ParseDateString(endDate); err == nil {
			query = query.Where("created_at <= ?", utils.GetEndOfDay(parsedDate))
		}
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		utils.InternalError(c, "Failed to count credit usage records")
		return
	}

	// Get credit usage records with pagination
	var usageRecords []models.AICreditUsage
	offset := (page - 1) * perPage
	if err := query.Preload("User").Preload("Project").
		Order("created_at DESC").
		Offset(offset).Limit(perPage).
		Find(&usageRecords).Error; err != nil {
		utils.InternalError(c, "Failed to fetch credit usage records")
		return
	}

	// Convert to response format
	var responses []CreditUsageResponse
	for _, record := range usageRecords {
		response := CreditUsageResponse{
			ID:               record.ID.String(),
			OrganizationID:   record.OrganizationID.String(),
			CreditsUsed:      record.CreditsUsed,
			Operation:        record.Operation,
			SourceLocale:     record.SourceLocale,
			TargetLocale:     record.TargetLocale,
			TextLength:       record.TextLength,
			ModelUsed:        record.ModelUsed,
			RemainingCredits: record.RemainingCredits,
			CreatedAt:        utils.FormatTimestamp(record.CreatedAt),
		}

		if record.UserID != nil {
			userID := record.UserID.String()
			response.UserID = &userID
		}

		if record.ProjectID != nil {
			projectID := record.ProjectID.String()
			response.ProjectID = &projectID
		}

		responses = append(responses, response)
	}

	utils.SuccessWithPagination(c, responses, uint32(page), uint32(perPage), uint64(total))
}

// GetCreditUsageAnalytics handles GET /api/organizations/:orgId/credit-analytics
func (h *CreditUsageHandler) GetCreditUsageAnalytics(c *gin.Context) {
	orgID := c.Param("orgId")

	// Validate organization ID
	orgUUID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	// Parse filter parameters
	period := c.DefaultQuery("period", "30d")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	groupBy := c.DefaultQuery("group_by", "day")

	db := database.GetDB()

	// Check if user has access to the organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ? AND is_active = ?",
		orgUUID, userUUID, true).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Calculate date range
	var fromDate, toDate time.Time
	now := utils.GetCurrentTime()

	if startDate != "" && endDate != "" {
		fromDate, _ = utils.ParseDateString(startDate)
		toDate, _ = utils.ParseDateString(endDate)
	} else {
		switch period {
		case "7d":
			fromDate = now.AddDate(0, 0, -7)
		case "30d":
			fromDate = now.AddDate(0, 0, -30)
		case "90d":
			fromDate = now.AddDate(0, 0, -90)
		case "1y":
			fromDate = now.AddDate(-1, 0, 0)
		default:
			fromDate = now.AddDate(0, 0, -30)
		}
		toDate = now
	}

	// Get analytics data
	analytics := h.calculateAnalytics(db, orgUUID, fromDate, toDate, groupBy)

	utils.Success(c, analytics)
}

// Helper method to calculate analytics
func (h *CreditUsageHandler) calculateAnalytics(db *gorm.DB, orgID uuid.UUID, fromDate, toDate time.Time, groupBy string) CreditUsageAnalyticsResponse {
	// For now, return mock data since this requires complex SQL queries
	// In a real implementation, you would use raw SQL or advanced GORM queries
	
	return CreditUsageAnalyticsResponse{
		TotalCreditsUsed:    1250,
		TotalOperations:     89,
		AverageCreditsPerOp: 14.04,
		ByOperation: map[string]int{
			"translate":        850,
			"review":          200,
			"detect_language": 150,
			"quality_check":   50,
		},
		ByLocale: map[string]int{
			"en-US": 400,
			"es-ES": 350,
			"fr-FR": 300,
			"de-DE": 200,
		},
		ByModel: map[string]int{
			"gpt-4":        600,
			"gpt-3.5":      400,
			"gemini-pro":   250,
		},
		TimeSeriesData: h.generateTimeSeriesData(fromDate, toDate, groupBy),
		TopUsers: []UserUsageData{
			{UserID: "user1", UserEmail: "<EMAIL>", CreditsUsed: 450, OperationCount: 32},
			{UserID: "user2", UserEmail: "<EMAIL>", CreditsUsed: 380, OperationCount: 28},
			{UserID: "user3", UserEmail: "<EMAIL>", CreditsUsed: 320, OperationCount: 22},
		},
		TopProjects: []ProjectUsageData{
			{ProjectID: "proj1", ProjectName: "Website Localization", CreditsUsed: 520, OperationCount: 38},
			{ProjectID: "proj2", ProjectName: "Mobile App", CreditsUsed: 430, OperationCount: 31},
			{ProjectID: "proj3", ProjectName: "Documentation", CreditsUsed: 300, OperationCount: 20},
		},
	}
}

// Helper method to generate time series data
func (h *CreditUsageHandler) generateTimeSeriesData(fromDate, toDate time.Time, groupBy string) []TimeSeriesData {
	var data []TimeSeriesData
	
	// Generate mock time series data
	current := fromDate
	for current.Before(toDate) || current.Equal(toDate) {
		data = append(data, TimeSeriesData{
			Date:         current.Format("2006-01-02"),
			TotalCredits: 40 + len(data)*2, // Mock increasing usage
			ByOperation: map[string]int{
				"translate":        25 + len(data),
				"review":          10,
				"detect_language": 5,
			},
			ByLocale: map[string]int{
				"en-US": 15 + len(data),
				"es-ES": 12,
				"fr-FR": 8,
				"de-DE": 5,
			},
		})
		
		switch groupBy {
		case "hour":
			current = current.Add(time.Hour)
		case "day":
			current = current.AddDate(0, 0, 1)
		case "week":
			current = current.AddDate(0, 0, 7)
		case "month":
			current = current.AddDate(0, 1, 0)
		default:
			current = current.AddDate(0, 0, 1)
		}
	}
	
	return data
}

package handlers

import (
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"

	"adc-multi-languages/services"
	"adc-multi-languages/utils"
)

// MetricsHandler handles metrics-related requests
type MetricsHandler struct {
	metricsService *services.MetricsService
}

// NewMetricsHandler creates a new metrics handler
func NewMetricsHandler() *MetricsHandler {
	return &MetricsHandler{
		metricsService: services.GetMetricsService(),
	}
}

// GetSystemMetrics handles GET /api/metrics/system
func (h *MetricsHandler) GetSystemMetrics(c *gin.Context) {
	// Check if user has admin access (simplified check)
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	// In a real implementation, you'd check admin permissions here
	// For now, we'll allow any authenticated user to view metrics
	_ = userID

	metrics := h.metricsService.GetSystemMetrics()
	utils.Success(c, metrics)
}

// GetEndpointMetrics handles GET /api/metrics/endpoints
func (h *MetricsHandler) GetEndpointMetrics(c *gin.Context) {
	// Check authentication
	_, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	metrics := h.metricsService.GetEndpointMetrics()
	utils.Success(c, metrics)
}

// GetAPIKeyMetrics handles GET /api/metrics/api-keys
func (h *MetricsHandler) GetAPIKeyMetrics(c *gin.Context) {
	// Check authentication
	_, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	metrics := h.metricsService.GetAPIKeyMetrics()
	utils.Success(c, metrics)
}

// GetTranslationMetrics handles GET /api/metrics/translations
func (h *MetricsHandler) GetTranslationMetrics(c *gin.Context) {
	// Check authentication
	_, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	metrics := h.metricsService.GetTranslationMetrics()
	utils.Success(c, metrics)
}

// GetActiveUsers handles GET /api/metrics/active-users
func (h *MetricsHandler) GetActiveUsers(c *gin.Context) {
	// Check authentication
	_, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	users := h.metricsService.GetActiveUsers()
	utils.Success(c, map[string]interface{}{
		"active_users": users,
		"count":        len(users),
	})
}

// GetHealthMetrics handles GET /api/metrics/health
func (h *MetricsHandler) GetHealthMetrics(c *gin.Context) {
	health := h.metricsService.GetHealthStatus()
	utils.Success(c, health)
}

// GetPrometheusMetrics handles GET /metrics (Prometheus format)
func (h *MetricsHandler) GetPrometheusMetrics(c *gin.Context) {
	systemMetrics := h.metricsService.GetSystemMetrics()
	endpointMetrics := h.metricsService.GetEndpointMetrics()
	translationMetrics := h.metricsService.GetTranslationMetrics()

	var output strings.Builder

	// System metrics
	output.WriteString("# HELP adc_uptime_seconds Total uptime in seconds\n")
	output.WriteString("# TYPE adc_uptime_seconds counter\n")
	output.WriteString(fmt.Sprintf("adc_uptime_seconds %.0f\n", systemMetrics.Uptime.Seconds()))

	output.WriteString("# HELP adc_memory_usage_bytes Memory usage in bytes\n")
	output.WriteString("# TYPE adc_memory_usage_bytes gauge\n")
	output.WriteString(fmt.Sprintf("adc_memory_usage_bytes{type=\"alloc\"} %d\n", systemMetrics.MemoryUsage.Alloc))
	output.WriteString(fmt.Sprintf("adc_memory_usage_bytes{type=\"sys\"} %d\n", systemMetrics.MemoryUsage.Sys))
	output.WriteString(fmt.Sprintf("adc_memory_usage_bytes{type=\"heap_alloc\"} %d\n", systemMetrics.MemoryUsage.HeapAlloc))

	output.WriteString("# HELP adc_goroutines_total Number of goroutines\n")
	output.WriteString("# TYPE adc_goroutines_total gauge\n")
	output.WriteString(fmt.Sprintf("adc_goroutines_total %d\n", systemMetrics.GoroutineCount))

	output.WriteString("# HELP adc_requests_total Total number of requests\n")
	output.WriteString("# TYPE adc_requests_total counter\n")
	output.WriteString(fmt.Sprintf("adc_requests_total %d\n", systemMetrics.RequestCount))

	output.WriteString("# HELP adc_errors_total Total number of errors\n")
	output.WriteString("# TYPE adc_errors_total counter\n")
	output.WriteString(fmt.Sprintf("adc_errors_total %d\n", systemMetrics.ErrorCount))

	output.WriteString("# HELP adc_active_users_total Number of active users\n")
	output.WriteString("# TYPE adc_active_users_total gauge\n")
	output.WriteString(fmt.Sprintf("adc_active_users_total %d\n", systemMetrics.ActiveUsers))

	output.WriteString("# HELP adc_response_time_average_ms Average response time in milliseconds\n")
	output.WriteString("# TYPE adc_response_time_average_ms gauge\n")
	output.WriteString(fmt.Sprintf("adc_response_time_average_ms %.2f\n", systemMetrics.AverageResponse))

	// Endpoint metrics
	output.WriteString("# HELP adc_endpoint_requests_total Total requests per endpoint\n")
	output.WriteString("# TYPE adc_endpoint_requests_total counter\n")
	for _, metric := range endpointMetrics {
		endpoint := strings.ReplaceAll(metric.Endpoint, "\"", "\\\"")
		output.WriteString(fmt.Sprintf("adc_endpoint_requests_total{endpoint=\"%s\"} %d\n", endpoint, metric.RequestCount))
	}

	output.WriteString("# HELP adc_endpoint_errors_total Total errors per endpoint\n")
	output.WriteString("# TYPE adc_endpoint_errors_total counter\n")
	for _, metric := range endpointMetrics {
		endpoint := strings.ReplaceAll(metric.Endpoint, "\"", "\\\"")
		output.WriteString(fmt.Sprintf("adc_endpoint_errors_total{endpoint=\"%s\"} %d\n", endpoint, metric.ErrorCount))
	}

	output.WriteString("# HELP adc_endpoint_response_time_ms Response time per endpoint in milliseconds\n")
	output.WriteString("# TYPE adc_endpoint_response_time_ms gauge\n")
	for _, metric := range endpointMetrics {
		endpoint := strings.ReplaceAll(metric.Endpoint, "\"", "\\\"")
		avgMs := float64(metric.AverageResponse.Nanoseconds()) / 1e6
		output.WriteString(fmt.Sprintf("adc_endpoint_response_time_ms{endpoint=\"%s\",type=\"average\"} %.2f\n", endpoint, avgMs))
		minMs := float64(metric.MinResponse.Nanoseconds()) / 1e6
		output.WriteString(fmt.Sprintf("adc_endpoint_response_time_ms{endpoint=\"%s\",type=\"min\"} %.2f\n", endpoint, minMs))
		maxMs := float64(metric.MaxResponse.Nanoseconds()) / 1e6
		output.WriteString(fmt.Sprintf("adc_endpoint_response_time_ms{endpoint=\"%s\",type=\"max\"} %.2f\n", endpoint, maxMs))
	}

	// Translation metrics
	output.WriteString("# HELP adc_translation_credits_used_total Total translation credits used per provider\n")
	output.WriteString("# TYPE adc_translation_credits_used_total counter\n")
	for provider, credits := range translationMetrics {
		output.WriteString(fmt.Sprintf("adc_translation_credits_used_total{provider=\"%s\"} %d\n", provider, credits))
	}

	// GC metrics
	output.WriteString("# HELP adc_gc_total Total number of garbage collections\n")
	output.WriteString("# TYPE adc_gc_total counter\n")
	output.WriteString(fmt.Sprintf("adc_gc_total %d\n", systemMetrics.MemoryUsage.NumGC))

	c.Header("Content-Type", "text/plain; version=0.0.4; charset=utf-8")
	c.String(200, output.String())
}

// GetAllMetrics handles GET /api/metrics (comprehensive metrics)
func (h *MetricsHandler) GetAllMetrics(c *gin.Context) {
	// Check authentication
	_, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	response := map[string]interface{}{
		"system":       h.metricsService.GetSystemMetrics(),
		"endpoints":    h.metricsService.GetEndpointMetrics(),
		"api_keys":     h.metricsService.GetAPIKeyMetrics(),
		"translations": h.metricsService.GetTranslationMetrics(),
		"active_users": h.metricsService.GetActiveUsers(),
		"health":       h.metricsService.GetHealthStatus(),
	}

	utils.Success(c, response)
}

// ResetMetrics handles POST /api/metrics/reset (for testing/development)
func (h *MetricsHandler) ResetMetrics(c *gin.Context) {
	// Check if user has admin access
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	// Simple admin check - in production, implement proper admin role checking
	_ = userID

	h.metricsService.Reset()
	utils.SuccessWithMessage(c, nil, "Metrics reset successfully")
}

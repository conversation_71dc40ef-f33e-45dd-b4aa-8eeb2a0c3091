package handlers

import (
	"encoding/json"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"adc-multi-languages/database"
	"adc-multi-languages/models"
	"adc-multi-languages/utils"
)

// PermissionGroupHandler handles permission group-related requests
type PermissionGroupHandler struct{}

// NewPermissionGroupHandler creates a new permission group handler
func NewPermissionGroupHandler() *PermissionGroupHandler {
	return &PermissionGroupHandler{}
}

// CreatePermissionGroupRequest represents the request structure for creating a permission group
type CreatePermissionGroupRequest struct {
	Name        string   `json:"name" binding:"required"`
	Description string   `json:"description"`
	Permissions []string `json:"permissions" binding:"required"`
}

// UpdatePermissionGroupRequest represents the request structure for updating a permission group
type UpdatePermissionGroupRequest struct {
	Name        *string  `json:"name"`
	Description *string  `json:"description"`
	Permissions []string `json:"permissions"`
}

// PermissionGroupResponse represents the response structure for permission group data
type PermissionGroupResponse struct {
	ID             string   `json:"id"`
	Name           string   `json:"name"`
	Description    string   `json:"description"`
	OrganizationID string   `json:"organization_id"`
	Permissions    []string `json:"permissions"`
	CreatedAt      string   `json:"created_at"`
	UpdatedAt      string   `json:"updated_at"`
	CreatedBy      string   `json:"created_by"`
}

// toPermissionGroupResponse converts a permission group model to response format
func toPermissionGroupResponse(group models.PermissionGroup) PermissionGroupResponse {
	// Parse permissions JSON
	var permissions []string
	if err := json.Unmarshal([]byte(group.Permissions), &permissions); err != nil {
		permissions = []string{}
	}

	return PermissionGroupResponse{
		ID:             group.ID.String(),
		Name:           group.Name,
		Description:    group.Description,
		OrganizationID: group.OrganizationID.String(),
		Permissions:    permissions,
		CreatedAt:      utils.FormatTimestamp(group.CreatedAt),
		UpdatedAt:      utils.FormatTimestamp(group.UpdatedAt),
		CreatedBy:      group.CreatedBy.String(),
	}
}

// ListPermissionGroups handles GET /api/organizations/:orgId/permission-groups
func (h *PermissionGroupHandler) ListPermissionGroups(c *gin.Context) {
	db := database.GetDB()
	orgID := c.Param("orgId")

	// Parse organization UUID
	organizationID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	// Check if user has access to the organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ?", organizationID, userUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// Parse filter parameters
	search := c.Query("search")

	// Build query
	query := db.Model(&models.PermissionGroup{}).Where("organization_id = ?", organizationID)

	if search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		utils.InternalError(c, "Failed to count permission groups")
		return
	}

	// Get permission groups with pagination
	var groups []models.PermissionGroup
	offset := (page - 1) * limit
	if err := query.Order("name ASC").Offset(offset).Limit(limit).Find(&groups).Error; err != nil {
		utils.InternalError(c, "Failed to fetch permission groups")
		return
	}

	// Convert to response format
	var responses []PermissionGroupResponse
	for _, group := range groups {
		responses = append(responses, toPermissionGroupResponse(group))
	}

	// Create pagination response
	utils.SuccessWithPagination(c, responses, uint32(page), uint32(limit), uint64(total))
}

// GetPermissionGroup handles GET /api/organizations/:orgId/permission-groups/:id
func (h *PermissionGroupHandler) GetPermissionGroup(c *gin.Context) {
	db := database.GetDB()
	orgID := c.Param("orgId")
	groupID := c.Param("id")

	// Parse UUIDs
	organizationID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	id, err := uuid.Parse(groupID)
	if err != nil {
		utils.BadRequest(c, "Invalid permission group ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	// Check if user has access to the organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ?", organizationID, userUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Find permission group
	var group models.PermissionGroup
	if err := db.First(&group, "id = ? AND organization_id = ?", id, organizationID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Permission group not found")
			return
		}
		utils.InternalError(c, "Failed to fetch permission group")
		return
	}

	utils.Success(c, toPermissionGroupResponse(group))
}

// CreatePermissionGroup handles POST /api/organizations/:orgId/permission-groups
func (h *PermissionGroupHandler) CreatePermissionGroup(c *gin.Context) {
	db := database.GetDB()
	orgID := c.Param("orgId")
	var req CreatePermissionGroupRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Parse organization UUID
	organizationID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	// Check if user has admin access to the organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ? AND role IN ?",
		organizationID, userUUID, []string{"admin", "owner"}).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have admin access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Check if permission group name already exists in this organization
	var existingGroup models.PermissionGroup
	if err := db.First(&existingGroup, "organization_id = ? AND name = ?", organizationID, req.Name).Error; err == nil {
		utils.Conflict(c, "Permission group with this name already exists")
		return
	}

	// Convert permissions to JSON
	permissionsJSON, err := json.Marshal(req.Permissions)
	if err != nil {
		utils.InternalError(c, "Failed to process permissions")
		return
	}

	// Create permission group
	group := models.PermissionGroup{
		Name:           req.Name,
		Description:    req.Description,
		OrganizationID: organizationID,
		Permissions:    string(permissionsJSON),
		CreatedBy:      userUUID,
	}

	if err := db.Create(&group).Error; err != nil {
		utils.InternalError(c, "Failed to create permission group")
		return
	}

	utils.SuccessWithMessage(c, toPermissionGroupResponse(group), "Permission group created successfully")
}

package handlers

import (
	"adc-multi-languages/utils"

	"github.com/gin-gonic/gin"
)

// APIHandler handles general API routes
type APIHandler struct{}

// NewAPIHandler creates a new API handler
func NewAPIHandler() *APIHandler {
	return &APIHandler{}
}

// Message represents a simple message structure
type Message struct {
	Message string `json:"message"`
}

// HelloRequest represents the request structure for hello endpoint
type HelloRequest struct {
	Name string `json:"name,omitempty"`
}

// HelloResponse represents the response structure for hello endpoint
type HelloResponse struct {
	Message string `json:"message"`
	Name    string `json:"name,omitempty"`
}

// EchoRequest represents the request structure for echo endpoint
type EchoRequest struct {
	Message string `json:"message" binding:"required"`
}

// EchoResponse represents the response structure for echo endpoint
type EchoResponse struct {
	Message string `json:"message"`
}

// Hello handles GET /api/hello
func (h *APIHandler) Hello(c *gin.Context) {
	response := HelloResponse{
		Message: "Hello, world!",
	}
	
	utils.SuccessWithMessage(c, response, "Hello from the API")
}

// HelloWithName handles GET /api/hello/:name
func (h *APIHandler) HelloWithName(c *gin.Context) {
	name := c.Param("name")
	
	response := HelloResponse{
		Message: "Hello, " + name + "!",
		Name:    name,
	}
	
	utils.SuccessWithMessage(c, response, "Hello from the API")
}

// Echo handles POST /api/echo
func (h *APIHandler) Echo(c *gin.Context) {
	var req EchoRequest
	
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}
	
	response := EchoResponse{
		Message: req.Message,
	}
	
	utils.SuccessWithMessage(c, response, "Echo successful")
}

// ErrorExample handles GET /api/error-example
func (h *APIHandler) ErrorExample(c *gin.Context) {
	errorType := c.Query("type")
	
	switch errorType {
	case "bad_request":
		utils.BadRequest(c, "This is a bad request error example")
	case "unauthorized":
		utils.Unauthorized(c, "This is an unauthorized error example")
	case "forbidden":
		utils.Forbidden(c, "This is a forbidden error example")
	case "not_found":
		utils.NotFound(c, "This is a not found error example")
	case "conflict":
		utils.Conflict(c, "This is a conflict error example")
	case "internal_error":
		utils.InternalServerError(c, "This is an internal server error example")
	case "validation":
		fieldErrors := []utils.FieldError{
			{Field: "email", Message: "Email is required"},
			{Field: "password", Message: "Password must be at least 8 characters"},
		}
		utils.ValidationError(c, fieldErrors)
	default:
		utils.BadRequest(c, "Invalid error type. Available types: bad_request, unauthorized, forbidden, not_found, conflict, internal_error, validation")
	}
}

// Health handles GET /health
func (h *APIHandler) Health(c *gin.Context) {
	response := map[string]interface{}{
		"status":    "healthy",
		"timestamp": utils.GetCurrentTimestamp(),
		"version":   "1.0.0",
	}
	
	utils.Success(c, response)
}

// Index handles GET /
func (h *APIHandler) Index(c *gin.Context) {
	response := map[string]interface{}{
		"message": "ADC Multi-Languages API",
		"version": "1.0.0",
		"status":  "running",
	}
	
	utils.Success(c, response)
}

package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"adc-multi-languages/database"
	"adc-multi-languages/models"
	"adc-multi-languages/utils"
)

// LocaleHandler handles locale-related requests
type LocaleHandler struct{}

// NewLocaleHandler creates a new locale handler
func NewLocaleHandler() *LocaleHandler {
	return &LocaleHandler{}
}

// CreateLocaleRequest represents the request structure for creating a locale
type CreateLocaleRequest struct {
	Code       string `json:"code" binding:"required"`
	Name       string `json:"name" binding:"required"`
	NativeName string `json:"native_name" binding:"required"`
	Direction  string `json:"direction"`
	IsActive   *bool  `json:"is_active"`
}

// UpdateLocaleRequest represents the request structure for updating a locale
type UpdateLocaleRequest struct {
	Name       *string `json:"name"`
	NativeName *string `json:"native_name"`
	Direction  *string `json:"direction"`
	IsActive   *bool   `json:"is_active"`
}

// LocaleResponse represents the response structure for locale data
type LocaleResponse struct {
	ID         string `json:"id"`
	Code       string `json:"code"`
	Name       string `json:"name"`
	NativeName string `json:"native_name"`
	IsActive   bool   `json:"is_active"`
	Direction  string `json:"direction"`
	CreatedAt  string `json:"created_at"`
	UpdatedAt  string `json:"updated_at"`
}

// toLocaleResponse converts a locale model to response format
func toLocaleResponse(locale models.Locale) LocaleResponse {
	return LocaleResponse{
		ID:         locale.ID.String(),
		Code:       locale.Code,
		Name:       locale.Name,
		NativeName: locale.NativeName,
		IsActive:   locale.IsActive,
		Direction:  locale.Direction,
		CreatedAt:  utils.FormatTimestamp(locale.CreatedAt),
		UpdatedAt:  utils.FormatTimestamp(locale.UpdatedAt),
	}
}

// ListLocales handles GET /api/locales
func (h *LocaleHandler) ListLocales(c *gin.Context) {
	db := database.GetDB()

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// Parse filter parameters
	isActive := c.Query("is_active")
	search := c.Query("search")

	// Build query
	query := db.Model(&models.Locale{})

	// Apply filters
	if isActive != "" {
		if isActive == "true" {
			query = query.Where("is_active = ?", true)
		} else if isActive == "false" {
			query = query.Where("is_active = ?", false)
		}
	}

	if search != "" {
		query = query.Where("name ILIKE ? OR native_name ILIKE ? OR code ILIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		utils.InternalError(c, "Failed to count locales")
		return
	}

	// Get locales with pagination
	var locales []models.Locale
	offset := (page - 1) * limit
	if err := query.Order("name ASC").Offset(offset).Limit(limit).Find(&locales).Error; err != nil {
		utils.InternalError(c, "Failed to fetch locales")
		return
	}

	// Convert to response format
	var responses []LocaleResponse
	for _, locale := range locales {
		responses = append(responses, toLocaleResponse(locale))
	}

	// Create pagination response
	utils.SuccessWithPagination(c, responses, uint32(page), uint32(limit), uint64(total))
}

// GetLocale handles GET /api/locales/:id
func (h *LocaleHandler) GetLocale(c *gin.Context) {
	db := database.GetDB()
	localeID := c.Param("id")

	// Parse UUID
	id, err := uuid.Parse(localeID)
	if err != nil {
		utils.BadRequest(c, "Invalid locale ID format")
		return
	}

	// Find locale
	var locale models.Locale
	if err := db.First(&locale, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Locale not found")
			return
		}
		utils.InternalError(c, "Failed to fetch locale")
		return
	}

	utils.Success(c, toLocaleResponse(locale))
}

// CreateLocale handles POST /api/locales
func (h *LocaleHandler) CreateLocale(c *gin.Context) {
	db := database.GetDB()
	var req CreateLocaleRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Check if locale code already exists
	var existingLocale models.Locale
	if err := db.First(&existingLocale, "code = ?", req.Code).Error; err == nil {
		utils.Conflict(c, "Locale with this code already exists")
		return
	}

	// Set defaults
	direction := req.Direction
	if direction == "" {
		direction = "ltr"
	}

	isActive := true
	if req.IsActive != nil {
		isActive = *req.IsActive
	}

	// Create locale
	locale := models.Locale{
		Code:       req.Code,
		Name:       req.Name,
		NativeName: req.NativeName,
		Direction:  direction,
		IsActive:   isActive,
	}

	if err := db.Create(&locale).Error; err != nil {
		utils.InternalError(c, "Failed to create locale")
		return
	}

	utils.SuccessWithMessage(c, toLocaleResponse(locale), "Locale created successfully")
}

// UpdateLocale handles PUT /api/locales/:id
func (h *LocaleHandler) UpdateLocale(c *gin.Context) {
	db := database.GetDB()
	localeID := c.Param("id")
	var req UpdateLocaleRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Parse UUID
	id, err := uuid.Parse(localeID)
	if err != nil {
		utils.BadRequest(c, "Invalid locale ID format")
		return
	}

	// Find locale
	var locale models.Locale
	if err := db.First(&locale, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Locale not found")
			return
		}
		utils.InternalError(c, "Failed to fetch locale")
		return
	}

	// Update fields
	if req.Name != nil {
		locale.Name = *req.Name
	}
	if req.NativeName != nil {
		locale.NativeName = *req.NativeName
	}
	if req.Direction != nil {
		locale.Direction = *req.Direction
	}
	if req.IsActive != nil {
		locale.IsActive = *req.IsActive
	}

	// Save changes
	if err := db.Save(&locale).Error; err != nil {
		utils.InternalError(c, "Failed to update locale")
		return
	}

	utils.SuccessWithMessage(c, toLocaleResponse(locale), "Locale updated successfully")
}

// DeleteLocale handles DELETE /api/locales/:id
func (h *LocaleHandler) DeleteLocale(c *gin.Context) {
	db := database.GetDB()
	localeID := c.Param("id")

	// Parse UUID
	id, err := uuid.Parse(localeID)
	if err != nil {
		utils.BadRequest(c, "Invalid locale ID format")
		return
	}

	// Find locale
	var locale models.Locale
	if err := db.First(&locale, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Locale not found")
			return
		}
		utils.InternalError(c, "Failed to fetch locale")
		return
	}

	// Check if locale is being used
	var translationCount int64
	if err := db.Model(&models.Translation{}).Where("locale_id = ?", id).Count(&translationCount).Error; err != nil {
		utils.InternalError(c, "Failed to check locale usage")
		return
	}

	if translationCount > 0 {
		utils.BadRequest(c, "Cannot delete locale that is being used in translations")
		return
	}

	// Delete locale
	if err := db.Delete(&locale).Error; err != nil {
		utils.InternalError(c, "Failed to delete locale")
		return
	}

	utils.SuccessWithMessage(c, nil, "Locale deleted successfully")
}

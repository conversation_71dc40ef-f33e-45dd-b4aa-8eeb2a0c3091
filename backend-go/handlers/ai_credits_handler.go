package handlers

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"adc-multi-languages/database"
	"adc-multi-languages/models"
	"adc-multi-languages/utils"
)

// AICreditHandler handles AI credit-related requests
type AICreditHandler struct{}

// NewAICreditHandler creates a new AI credit handler
func NewAICreditHandler() *AICreditHandler {
	return &AICreditHandler{}
}

// AICreditResponse represents the response structure for AI credit data
type AICreditResponse struct {
	OrganizationID        string `json:"organization_id"`
	MonthlyAllowance      int    `json:"monthly_allowance"`
	RemainingCredits      int    `json:"remaining_credits"`
	UsedCredits           int    `json:"used_credits"`
	ResetDate             string `json:"reset_date"`
	SubscriptionTier      string `json:"subscription_tier"`
	CanPurchaseAdditional bool   `json:"can_purchase_additional"`
}

// AICreditTransactionResponse represents the response structure for AI credit transaction data
type AICreditTransactionResponse struct {
	ID              string  `json:"id"`
	TransactionType string  `json:"transaction_type"`
	Amount          int     `json:"amount"`
	BalanceBefore   int     `json:"balance_before"`
	BalanceAfter    int     `json:"balance_after"`
	Description     string  `json:"description"`
	Reference       *string `json:"reference"`
	CreatedAt       string  `json:"created_at"`
	PerformedBy     *string `json:"performed_by"`
}

// AICreditUsageResponse represents the response structure for AI credit usage data
type AICreditUsageResponse struct {
	ID               string  `json:"id"`
	CreditsUsed      int     `json:"credits_used"`
	Operation        string  `json:"operation"`
	SourceLocale     *string `json:"source_locale"`
	TargetLocale     *string `json:"target_locale"`
	TextLength       *int    `json:"text_length"`
	ModelUsed        *string `json:"model_used"`
	RemainingCredits int     `json:"remaining_credits"`
	CreatedAt        string  `json:"created_at"`
	UserID           *string `json:"user_id"`
	ProjectID        *string `json:"project_id"`
}

// toAICreditTransactionResponse converts a transaction model to response format
func toAICreditTransactionResponse(transaction models.AICreditTransaction) AICreditTransactionResponse {
	response := AICreditTransactionResponse{
		ID:              transaction.ID.String(),
		TransactionType: transaction.TransactionType,
		Amount:          transaction.Amount,
		BalanceBefore:   transaction.BalanceBefore,
		BalanceAfter:    transaction.BalanceAfter,
		Description:     transaction.Description,
		Reference:       transaction.Reference,
		CreatedAt:       utils.FormatTimestamp(transaction.CreatedAt),
	}

	if transaction.PerformedBy != nil {
		performedBy := transaction.PerformedBy.String()
		response.PerformedBy = &performedBy
	}

	return response
}

// toAICreditUsageResponse converts a usage model to response format
func toAICreditUsageResponse(usage models.AICreditUsage) AICreditUsageResponse {
	response := AICreditUsageResponse{
		ID:               usage.ID.String(),
		CreditsUsed:      usage.CreditsUsed,
		Operation:        usage.Operation,
		SourceLocale:     usage.SourceLocale,
		TargetLocale:     usage.TargetLocale,
		TextLength:       usage.TextLength,
		ModelUsed:        usage.ModelUsed,
		RemainingCredits: usage.RemainingCredits,
		CreatedAt:        utils.FormatTimestamp(usage.CreatedAt),
	}

	if usage.UserID != nil {
		userID := usage.UserID.String()
		response.UserID = &userID
	}

	if usage.ProjectID != nil {
		projectID := usage.ProjectID.String()
		response.ProjectID = &projectID
	}

	return response
}

// GetAICredits handles GET /api/organizations/:orgId/ai-credits
func (h *AICreditHandler) GetAICredits(c *gin.Context) {
	db := database.GetDB()
	orgID := c.Param("orgId")

	// Parse organization UUID
	organizationID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	// Check if user has access to the organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ?", organizationID, userUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Get organization details
	var organization models.Organization
	if err := db.First(&organization, "id = ?", organizationID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Organization not found")
			return
		}
		utils.InternalError(c, "Failed to fetch organization")
		return
	}

	// Calculate used credits for current period
	var usedCredits int64
	if organization.AICreditsResetDate != nil {
		db.Model(&models.AICreditUsage{}).
			Where("organization_id = ? AND created_at >= ?", organizationID, *organization.AICreditsResetDate).
			Select("COALESCE(SUM(credits_used), 0)").
			Scan(&usedCredits)
	}

	// Prepare response
	monthlyAllowance := 1000 // Default
	if organization.AICreditsMonthlyAllowance != nil {
		monthlyAllowance = *organization.AICreditsMonthlyAllowance
	}

	remainingCredits := 0
	if organization.AICreditsRemaining != nil {
		remainingCredits = *organization.AICreditsRemaining
	}

	resetDate := time.Now().AddDate(0, 1, 0).Format("2006-01-02T15:04:05Z07:00")
	if organization.AICreditsResetDate != nil {
		resetDate = utils.FormatTimestamp(*organization.AICreditsResetDate)
	}

	response := AICreditResponse{
		OrganizationID:        organizationID.String(),
		MonthlyAllowance:      monthlyAllowance,
		RemainingCredits:      remainingCredits,
		UsedCredits:           int(usedCredits),
		ResetDate:             resetDate,
		SubscriptionTier:      organization.SubscriptionTier,
		CanPurchaseAdditional: organization.SubscriptionTier != "free",
	}

	utils.Success(c, response)
}

// GetAICreditsHistory handles GET /api/organizations/:orgId/ai-credits/history
func (h *AICreditHandler) GetAICreditsHistory(c *gin.Context) {
	db := database.GetDB()
	orgID := c.Param("orgId")

	// Parse organization UUID
	organizationID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	// Check if user has access to the organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ?", organizationID, userUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// Parse filter parameters
	transactionType := c.Query("type")

	// Build query
	query := db.Model(&models.AICreditTransaction{}).Where("organization_id = ?", organizationID)

	if transactionType != "" {
		query = query.Where("transaction_type = ?", transactionType)
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		utils.InternalError(c, "Failed to count transactions")
		return
	}

	// Get transactions with pagination
	var transactions []models.AICreditTransaction
	offset := (page - 1) * limit
	if err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&transactions).Error; err != nil {
		utils.InternalError(c, "Failed to fetch transactions")
		return
	}

	// Convert to response format
	var responses []AICreditTransactionResponse
	for _, transaction := range transactions {
		responses = append(responses, toAICreditTransactionResponse(transaction))
	}

	// Create pagination response
	utils.SuccessWithPagination(c, responses, uint32(page), uint32(limit), uint64(total))
}

// GetAICreditsUsage handles GET /api/organizations/:orgId/ai-credits/usage
func (h *AICreditHandler) GetAICreditsUsage(c *gin.Context) {
	db := database.GetDB()
	orgID := c.Param("orgId")

	// Parse organization UUID
	organizationID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	// Check if user has access to the organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ?", organizationID, userUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// Build query
	query := db.Model(&models.AICreditUsage{}).Where("organization_id = ?", organizationID)

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		utils.InternalError(c, "Failed to count usage records")
		return
	}

	// Get usage records with pagination
	var usageRecords []models.AICreditUsage
	offset := (page - 1) * limit
	if err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&usageRecords).Error; err != nil {
		utils.InternalError(c, "Failed to fetch usage records")
		return
	}

	// Convert to response format
	var responses []AICreditUsageResponse
	for _, usage := range usageRecords {
		responses = append(responses, toAICreditUsageResponse(usage))
	}

	// Create pagination response
	utils.SuccessWithPagination(c, responses, uint32(page), uint32(limit), uint64(total))
}

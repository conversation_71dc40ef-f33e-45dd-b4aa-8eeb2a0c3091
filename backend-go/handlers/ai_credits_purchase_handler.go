package handlers

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"adc-multi-languages/database"
	"adc-multi-languages/models"
	"adc-multi-languages/utils"
)

// AICreditsPurchaseHandler handles AI credits purchase-related requests
type AICreditsPurchaseHandler struct{}

// NewAICreditsPurchaseHandler creates a new AI credits purchase handler
func NewAICreditsPurchaseHandler() *AICreditsPurchaseHandler {
	return &AICreditsPurchaseHandler{}
}

// PurchaseCreditsRequest represents the request structure for purchasing credits
type PurchaseCreditsRequest struct {
	Amount      int    `json:"amount" binding:"required,min=100"`
	PaymentType string `json:"payment_type" binding:"required"` // stripe, paypal, etc.
}

// CreditsPricingTier represents a pricing tier for credits
type CreditsPricingTier struct {
	MinCredits   int     `json:"min_credits"`
	MaxCredits   *int    `json:"max_credits"`
	PricePerUnit float64 `json:"price_per_unit"`
	Discount     float64 `json:"discount"`
	Popular      bool    `json:"popular"`
}

// CreditsPricingResponse represents the response structure for credits pricing
type CreditsPricingResponse struct {
	Currency string               `json:"currency"`
	Tiers    []CreditsPricingTier `json:"tiers"`
	BaseRate float64              `json:"base_rate"`
}

// PurchaseCreditsResponse represents the response structure for credit purchase
type PurchaseCreditsResponse struct {
	TransactionID  string  `json:"transaction_id"`
	Amount         int     `json:"amount"`
	TotalCost      float64 `json:"total_cost"`
	Currency       string  `json:"currency"`
	PaymentType    string  `json:"payment_type"`
	Status         string  `json:"status"`
	PaymentURL     *string `json:"payment_url,omitempty"`
	OrganizationID string  `json:"organization_id"`
	NewBalance     int     `json:"new_balance"`
	CreatedAt      string  `json:"created_at"`
}

// PurchaseAICredits handles POST /api/organizations/:orgId/ai-credits/purchase
func (h *AICreditsPurchaseHandler) PurchaseAICredits(c *gin.Context) {
	orgID := c.Param("orgId")

	// Validate organization ID
	orgUUID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	var req PurchaseCreditsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Validate payment type
	validPaymentTypes := map[string]bool{
		"stripe": true,
		"paypal": true,
	}
	if !validPaymentTypes[req.PaymentType] {
		utils.BadRequest(c, "Invalid payment type. Supported types: stripe, paypal")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	db := database.GetDB()

	// Check if user has access to the organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ? AND is_active = ?",
		orgUUID, userUUID, true).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Check if user has billing permissions (admin or billing role)
	if membership.Role != "admin" && membership.Role != "billing" {
		utils.Forbidden(c, "User does not have billing permissions")
		return
	}

	// Get organization
	var organization models.Organization
	if err := db.First(&organization, "id = ?", orgUUID).Error; err != nil {
		utils.InternalError(c, "Failed to fetch organization")
		return
	}

	// Calculate pricing
	pricing := h.calculatePricing(req.Amount)
	totalCost := pricing.TotalCost

	// Start transaction
	tx := db.Begin()

	// Create credit transaction record
	transaction := models.AICreditTransaction{
		OrganizationID:  orgUUID,
		TransactionType: "purchase",
		Amount:          req.Amount,
		BalanceBefore:   0, // Will be updated
		BalanceAfter:    0, // Will be updated
		Description:     "AI credits purchase",
		PerformedBy:     &userUUID,
	}

	// Get current balance
	currentBalance := 0
	if organization.AICreditsRemaining != nil {
		currentBalance = *organization.AICreditsRemaining
	}

	transaction.BalanceBefore = currentBalance
	transaction.BalanceAfter = currentBalance + req.Amount

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		utils.InternalError(c, "Failed to create transaction record")
		return
	}

	// Update organization credits
	newBalance := currentBalance + req.Amount
	if err := tx.Model(&organization).Update("ai_credits_remaining", newBalance).Error; err != nil {
		tx.Rollback()
		utils.InternalError(c, "Failed to update organization credits")
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		utils.InternalError(c, "Failed to complete transaction")
		return
	}

	// In a real implementation, you would integrate with payment processors here
	// For now, we'll simulate a successful payment
	var paymentURL *string
	if req.PaymentType == "stripe" {
		// Create Stripe payment session
		url := "https://checkout.stripe.com/pay/cs_test_example"
		paymentURL = &url
	}

	response := PurchaseCreditsResponse{
		TransactionID:  transaction.ID.String(),
		Amount:         req.Amount,
		TotalCost:      totalCost,
		Currency:       "USD",
		PaymentType:    req.PaymentType,
		Status:         "pending", // In real implementation, this would be updated by webhooks
		PaymentURL:     paymentURL,
		OrganizationID: orgUUID.String(),
		NewBalance:     newBalance,
		CreatedAt:      utils.FormatTimestamp(transaction.CreatedAt),
	}

	utils.SuccessWithMessage(c, response, "Credit purchase initiated successfully")
}

// GetAICreditsPricing handles GET /api/ai-credits/pricing
func (h *AICreditsPurchaseHandler) GetAICreditsPricing(c *gin.Context) {
	pricing := CreditsPricingResponse{
		Currency: "USD",
		BaseRate: 0.001, // $0.001 per credit
		Tiers: []CreditsPricingTier{
			{
				MinCredits:   100,
				MaxCredits:   &[]int{999}[0],
				PricePerUnit: 0.001,
				Discount:     0,
				Popular:      false,
			},
			{
				MinCredits:   1000,
				MaxCredits:   &[]int{4999}[0],
				PricePerUnit: 0.0009,
				Discount:     10,
				Popular:      true,
			},
			{
				MinCredits:   5000,
				MaxCredits:   &[]int{9999}[0],
				PricePerUnit: 0.0008,
				Discount:     20,
				Popular:      false,
			},
			{
				MinCredits:   10000,
				MaxCredits:   nil, // No upper limit
				PricePerUnit: 0.0007,
				Discount:     30,
				Popular:      false,
			},
		},
	}

	utils.Success(c, pricing)
}

// PricingCalculation represents the result of pricing calculation
type PricingCalculation struct {
	Amount       int     `json:"amount"`
	PricePerUnit float64 `json:"price_per_unit"`
	Subtotal     float64 `json:"subtotal"`
	Discount     float64 `json:"discount"`
	TotalCost    float64 `json:"total_cost"`
	Tier         string  `json:"tier"`
}

// calculatePricing calculates the pricing for a given amount of credits
func (h *AICreditsPurchaseHandler) calculatePricing(amount int) PricingCalculation {
	var tier CreditsPricingTier
	var tierName string

	// Determine pricing tier
	switch {
	case amount >= 10000:
		tier = CreditsPricingTier{PricePerUnit: 0.0007, Discount: 30}
		tierName = "Enterprise"
	case amount >= 5000:
		tier = CreditsPricingTier{PricePerUnit: 0.0008, Discount: 20}
		tierName = "Business"
	case amount >= 1000:
		tier = CreditsPricingTier{PricePerUnit: 0.0009, Discount: 10}
		tierName = "Professional"
	default:
		tier = CreditsPricingTier{PricePerUnit: 0.001, Discount: 0}
		tierName = "Starter"
	}

	subtotal := float64(amount) * tier.PricePerUnit
	discountAmount := subtotal * (tier.Discount / 100)
	totalCost := subtotal - discountAmount

	return PricingCalculation{
		Amount:       amount,
		PricePerUnit: tier.PricePerUnit,
		Subtotal:     subtotal,
		Discount:     discountAmount,
		TotalCost:    totalCost,
		Tier:         tierName,
	}
}

// GetPricingCalculation handles GET /api/ai-credits/pricing/calculate?amount=1000
func (h *AICreditsPurchaseHandler) GetPricingCalculation(c *gin.Context) {
	amountStr := c.Query("amount")
	if amountStr == "" {
		utils.BadRequest(c, "Amount parameter is required")
		return
	}

	amount := 0
	if _, err := fmt.Sscanf(amountStr, "%d", &amount); err != nil {
		utils.BadRequest(c, "Invalid amount format")
		return
	}

	if amount < 100 {
		utils.BadRequest(c, "Minimum purchase amount is 100 credits")
		return
	}

	calculation := h.calculatePricing(amount)
	utils.Success(c, calculation)
}

package handlers

import (
	"encoding/json"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"adc-multi-languages/database"
	"adc-multi-languages/models"
	"adc-multi-languages/utils"
)

// SubscriptionPlanHandler handles subscription plan-related requests
type SubscriptionPlanHandler struct{}

// NewSubscriptionPlanHandler creates a new subscription plan handler
func NewSubscriptionPlanHandler() *SubscriptionPlanHandler {
	return &SubscriptionPlanHandler{}
}

// CreateSubscriptionPlanRequest represents the request structure for creating a subscription plan
type CreateSubscriptionPlanRequest struct {
	Name                      string   `json:"name" binding:"required"`
	Description               string   `json:"description"`
	PriceMonthly              float64  `json:"price_monthly" binding:"required"`
	PriceYearly               *float64 `json:"price_yearly"`
	AICreditsMonthlyAllowance int      `json:"ai_credits_monthly_allowance" binding:"required"`
	MaxProjects               *int     `json:"max_projects"`
	MaxUsers                  *int     `json:"max_users"`
	Features                  []string `json:"features"`
	StripePriceIDMonthly      *string  `json:"stripe_price_id_monthly"`
	StripePriceIDYearly       *string  `json:"stripe_price_id_yearly"`
}

// UpdateSubscriptionPlanRequest represents the request structure for updating a subscription plan
type UpdateSubscriptionPlanRequest struct {
	Name                      *string  `json:"name"`
	Description               *string  `json:"description"`
	PriceMonthly              *float64 `json:"price_monthly"`
	PriceYearly               *float64 `json:"price_yearly"`
	AICreditsMonthlyAllowance *int     `json:"ai_credits_monthly_allowance"`
	MaxProjects               *int     `json:"max_projects"`
	MaxUsers                  *int     `json:"max_users"`
	Features                  []string `json:"features"`
	IsActive                  *bool    `json:"is_active"`
	StripePriceIDMonthly      *string  `json:"stripe_price_id_monthly"`
	StripePriceIDYearly       *string  `json:"stripe_price_id_yearly"`
}

// SubscriptionPlanResponse represents the response structure for subscription plan data
type SubscriptionPlanResponse struct {
	ID                        string   `json:"id"`
	Name                      string   `json:"name"`
	Description               string   `json:"description"`
	PriceMonthly              float64  `json:"price_monthly"`
	PriceYearly               *float64 `json:"price_yearly"`
	AICreditsMonthlyAllowance int      `json:"ai_credits_monthly_allowance"`
	MaxProjects               *int     `json:"max_projects"`
	MaxUsers                  *int     `json:"max_users"`
	Features                  []string `json:"features"`
	IsActive                  bool     `json:"is_active"`
	StripePriceIDMonthly      *string  `json:"stripe_price_id_monthly"`
	StripePriceIDYearly       *string  `json:"stripe_price_id_yearly"`
	CreatedAt                 string   `json:"created_at"`
	UpdatedAt                 string   `json:"updated_at"`
}

// toSubscriptionPlanResponse converts a subscription plan model to response format
func toSubscriptionPlanResponse(plan models.SubscriptionPlan) SubscriptionPlanResponse {
	// Parse features JSON
	var features []string
	if err := json.Unmarshal([]byte(plan.Features), &features); err != nil {
		features = []string{}
	}

	return SubscriptionPlanResponse{
		ID:                        plan.ID.String(),
		Name:                      plan.Name,
		Description:               plan.Description,
		PriceMonthly:              plan.PriceMonthly,
		PriceYearly:               plan.PriceYearly,
		AICreditsMonthlyAllowance: plan.AICreditsMonthlyAllowance,
		MaxProjects:               plan.MaxProjects,
		MaxUsers:                  plan.MaxUsers,
		Features:                  features,
		IsActive:                  plan.IsActive,
		StripePriceIDMonthly:      plan.StripePriceIDMonthly,
		StripePriceIDYearly:       plan.StripePriceIDYearly,
		CreatedAt:                 utils.FormatTimestamp(plan.CreatedAt),
		UpdatedAt:                 utils.FormatTimestamp(plan.UpdatedAt),
	}
}

// ListSubscriptionPlans handles GET /api/subscription-plans
func (h *SubscriptionPlanHandler) ListSubscriptionPlans(c *gin.Context) {
	db := database.GetDB()

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// Parse filter parameters
	isActive := c.Query("is_active")
	includeInactive := c.Query("include_inactive") == "true"

	// Build query
	query := db.Model(&models.SubscriptionPlan{})

	// By default, only show active plans unless specifically requested
	if !includeInactive {
		if isActive == "" {
			query = query.Where("is_active = ?", true)
		} else if isActive == "true" {
			query = query.Where("is_active = ?", true)
		} else if isActive == "false" {
			query = query.Where("is_active = ?", false)
		}
	} else {
		if isActive == "true" {
			query = query.Where("is_active = ?", true)
		} else if isActive == "false" {
			query = query.Where("is_active = ?", false)
		}
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		utils.InternalError(c, "Failed to count subscription plans")
		return
	}

	// Get subscription plans with pagination
	var plans []models.SubscriptionPlan
	offset := (page - 1) * limit
	if err := query.Order("price_monthly ASC").Offset(offset).Limit(limit).Find(&plans).Error; err != nil {
		utils.InternalError(c, "Failed to fetch subscription plans")
		return
	}

	// Convert to response format
	var responses []SubscriptionPlanResponse
	for _, plan := range plans {
		responses = append(responses, toSubscriptionPlanResponse(plan))
	}

	utils.SuccessWithPagination(c, responses, uint32(page), uint32(limit), uint64(total))
}

// GetSubscriptionPlan handles GET /api/subscription-plans/:id
func (h *SubscriptionPlanHandler) GetSubscriptionPlan(c *gin.Context) {
	db := database.GetDB()
	planID := c.Param("id")

	// Parse UUID
	id, err := uuid.Parse(planID)
	if err != nil {
		utils.BadRequest(c, "Invalid subscription plan ID format")
		return
	}

	// Find subscription plan
	var plan models.SubscriptionPlan
	if err := db.First(&plan, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Subscription plan not found")
			return
		}
		utils.InternalError(c, "Failed to fetch subscription plan")
		return
	}

	utils.Success(c, toSubscriptionPlanResponse(plan))
}

// CreateSubscriptionPlan handles POST /api/admin/subscription-plans
func (h *SubscriptionPlanHandler) CreateSubscriptionPlan(c *gin.Context) {
	// Check admin access (simplified check)
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	db := database.GetDB()
	var user models.User
	if err := db.First(&user, "id = ?", userID).Error; err != nil {
		utils.Forbidden(c, "Access denied")
		return
	}

	// Simple admin check
	if user.Email != "<EMAIL>" && user.Email != "<EMAIL>" {
		utils.Forbidden(c, "Admin access required")
		return
	}

	var req CreateSubscriptionPlanRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Check if plan name already exists
	var existingPlan models.SubscriptionPlan
	if err := db.First(&existingPlan, "name = ?", req.Name).Error; err == nil {
		utils.Conflict(c, "Subscription plan with this name already exists")
		return
	}

	// Convert features to JSON
	featuresJSON, err := json.Marshal(req.Features)
	if err != nil {
		utils.InternalError(c, "Failed to process features")
		return
	}

	// Create subscription plan
	plan := models.SubscriptionPlan{
		Name:                      req.Name,
		Description:               req.Description,
		PriceMonthly:              req.PriceMonthly,
		PriceYearly:               req.PriceYearly,
		AICreditsMonthlyAllowance: req.AICreditsMonthlyAllowance,
		MaxProjects:               req.MaxProjects,
		MaxUsers:                  req.MaxUsers,
		Features:                  string(featuresJSON),
		IsActive:                  true,
		StripePriceIDMonthly:      req.StripePriceIDMonthly,
		StripePriceIDYearly:       req.StripePriceIDYearly,
	}

	if err := db.Create(&plan).Error; err != nil {
		utils.InternalError(c, "Failed to create subscription plan")
		return
	}

	utils.SuccessWithMessage(c, toSubscriptionPlanResponse(plan), "Subscription plan created successfully")
}

// UpdateSubscriptionPlan handles PUT /api/admin/subscription-plans/:id
func (h *SubscriptionPlanHandler) UpdateSubscriptionPlan(c *gin.Context) {
	// Check admin access (simplified check)
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	db := database.GetDB()
	var user models.User
	if err := db.First(&user, "id = ?", userID).Error; err != nil {
		utils.Forbidden(c, "Access denied")
		return
	}

	// Simple admin check
	if user.Email != "<EMAIL>" && user.Email != "<EMAIL>" {
		utils.Forbidden(c, "Admin access required")
		return
	}

	planID := c.Param("id")
	var req UpdateSubscriptionPlanRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Parse UUID
	id, err := uuid.Parse(planID)
	if err != nil {
		utils.BadRequest(c, "Invalid subscription plan ID format")
		return
	}

	// Find subscription plan
	var plan models.SubscriptionPlan
	if err := db.First(&plan, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Subscription plan not found")
			return
		}
		utils.InternalError(c, "Failed to fetch subscription plan")
		return
	}

	// Update fields
	if req.Name != nil {
		plan.Name = *req.Name
	}
	if req.Description != nil {
		plan.Description = *req.Description
	}
	if req.PriceMonthly != nil {
		plan.PriceMonthly = *req.PriceMonthly
	}
	if req.PriceYearly != nil {
		plan.PriceYearly = req.PriceYearly
	}
	if req.AICreditsMonthlyAllowance != nil {
		plan.AICreditsMonthlyAllowance = *req.AICreditsMonthlyAllowance
	}
	if req.MaxProjects != nil {
		plan.MaxProjects = req.MaxProjects
	}
	if req.MaxUsers != nil {
		plan.MaxUsers = req.MaxUsers
	}
	if req.Features != nil {
		featuresJSON, err := json.Marshal(req.Features)
		if err != nil {
			utils.InternalError(c, "Failed to process features")
			return
		}
		plan.Features = string(featuresJSON)
	}
	if req.IsActive != nil {
		plan.IsActive = *req.IsActive
	}
	if req.StripePriceIDMonthly != nil {
		plan.StripePriceIDMonthly = req.StripePriceIDMonthly
	}
	if req.StripePriceIDYearly != nil {
		plan.StripePriceIDYearly = req.StripePriceIDYearly
	}

	// Save changes
	if err := db.Save(&plan).Error; err != nil {
		utils.InternalError(c, "Failed to update subscription plan")
		return
	}

	utils.SuccessWithMessage(c, toSubscriptionPlanResponse(plan), "Subscription plan updated successfully")
}

package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"adc-multi-languages/services"
	"adc-multi-languages/utils"
)

// BackgroundJobHandler handles background job-related requests
type BackgroundJobHandler struct {
	jobService *services.BackgroundJobService
}

// NewBackgroundJobHandler creates a new background job handler
func NewBackgroundJobHandler() *BackgroundJobHandler {
	return &BackgroundJobHandler{
		jobService: services.GetBackgroundJobService(),
	}
}

// CreateJobRequest represents the request structure for creating a job
type CreateJobRequest struct {
	Type    string                 `json:"type" binding:"required"`
	Payload map[string]interface{} `json:"payload"`
}

// CreateJob handles POST /api/jobs
func (h *BackgroundJobHandler) CreateJob(c *gin.Context) {
	var req CreateJobRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	// Create job
	job, err := h.jobService.EnqueueJob(req.Type, req.Payload, userID.(string))
	if err != nil {
		utils.BadRequest(c, "Failed to create job: "+err.Error())
		return
	}

	utils.SuccessWithMessage(c, job, "Job created successfully")
}

// GetJob handles GET /api/jobs/:id
func (h *BackgroundJobHandler) GetJob(c *gin.Context) {
	jobID := c.Param("id")

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	// Get job
	job, err := h.jobService.GetJob(jobID)
	if err != nil {
		utils.NotFound(c, "Job not found")
		return
	}

	// Check if user owns the job (or is admin)
	if job.UserID != userID.(string) {
		// In a real implementation, check if user is admin
		utils.Forbidden(c, "Access denied")
		return
	}

	utils.Success(c, job)
}

// GetUserJobs handles GET /api/jobs
func (h *BackgroundJobHandler) GetUserJobs(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// Get filter parameters
	jobType := c.Query("type")
	status := c.Query("status")

	// Get user jobs
	jobs := h.jobService.GetUserJobs(userID.(string))

	// Apply filters
	var filteredJobs []*services.Job
	for _, job := range jobs {
		if jobType != "" && job.Type != jobType {
			continue
		}
		if status != "" && string(job.Status) != status {
			continue
		}
		filteredJobs = append(filteredJobs, job)
	}

	// Apply pagination
	total := len(filteredJobs)
	start := (page - 1) * limit
	end := start + limit

	if start >= total {
		filteredJobs = []*services.Job{}
	} else {
		if end > total {
			end = total
		}
		filteredJobs = filteredJobs[start:end]
	}

	utils.SuccessWithPagination(c, filteredJobs, uint32(page), uint32(limit), uint64(total))
}

// CancelJob handles DELETE /api/jobs/:id
func (h *BackgroundJobHandler) CancelJob(c *gin.Context) {
	jobID := c.Param("id")

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	// Get job to check ownership
	job, err := h.jobService.GetJob(jobID)
	if err != nil {
		utils.NotFound(c, "Job not found")
		return
	}

	// Check if user owns the job (or is admin)
	if job.UserID != userID.(string) {
		// In a real implementation, check if user is admin
		utils.Forbidden(c, "Access denied")
		return
	}

	// Cancel job
	err = h.jobService.CancelJob(jobID)
	if err != nil {
		utils.BadRequest(c, "Failed to cancel job: "+err.Error())
		return
	}

	utils.SuccessWithMessage(c, nil, "Job cancelled successfully")
}

// GetJobTypes handles GET /api/jobs/types
func (h *BackgroundJobHandler) GetJobTypes(c *gin.Context) {
	jobTypes := map[string]interface{}{
		"email_send": map[string]interface{}{
			"description": "Send email messages",
			"parameters": map[string]string{
				"to":      "Email recipient (required)",
				"subject": "Email subject (required)",
				"body":    "Email body (required)",
			},
		},
		"file_cleanup": map[string]interface{}{
			"description": "Clean up old uploaded files",
			"parameters": map[string]string{
				"max_age_hours": "Maximum age of files to keep in hours (optional, default: 24)",
			},
		},
		"translation_batch": map[string]interface{}{
			"description": "Process batch translations",
			"parameters": map[string]string{
				"project_id":    "Project ID (required)",
				"source_locale": "Source locale (required)",
				"target_locale": "Target locale (required)",
				"keys":          "Array of translation keys (required)",
			},
		},
		"export_translations": map[string]interface{}{
			"description": "Export translations to file",
			"parameters": map[string]string{
				"project_id": "Project ID (required)",
				"locale_id":  "Locale ID (optional, exports all if not specified)",
				"format":     "Export format: json, csv, xlsx (optional, default: json)",
			},
		},
		"import_translations": map[string]interface{}{
			"description": "Import translations from file",
			"parameters": map[string]string{
				"project_id": "Project ID (required)",
				"file_path":  "Path to import file (required)",
				"format":     "File format: json, csv, xlsx (required)",
				"overwrite":  "Whether to overwrite existing translations (optional, default: false)",
			},
		},
		"generate_report": map[string]interface{}{
			"description": "Generate usage or analytics report",
			"parameters": map[string]string{
				"report_type":  "Type of report: usage, analytics, billing (required)",
				"date_from":    "Start date (required)",
				"date_to":      "End date (required)",
				"organization": "Organization ID (optional)",
			},
		},
	}

	utils.Success(c, jobTypes)
}

// GetJobStats handles GET /api/jobs/stats
func (h *BackgroundJobHandler) GetJobStats(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	// Get user jobs
	jobs := h.jobService.GetUserJobs(userID.(string))

	// Calculate statistics
	stats := map[string]interface{}{
		"total":     len(jobs),
		"pending":   0,
		"running":   0,
		"completed": 0,
		"failed":    0,
		"cancelled": 0,
		"by_type":   make(map[string]int),
	}

	for _, job := range jobs {
		switch job.Status {
		case services.JobStatusPending:
			stats["pending"] = stats["pending"].(int) + 1
		case services.JobStatusRunning:
			stats["running"] = stats["running"].(int) + 1
		case services.JobStatusCompleted:
			stats["completed"] = stats["completed"].(int) + 1
		case services.JobStatusFailed:
			stats["failed"] = stats["failed"].(int) + 1
		case services.JobStatusCancelled:
			stats["cancelled"] = stats["cancelled"].(int) + 1
		}

		byType := stats["by_type"].(map[string]int)
		byType[job.Type]++
	}

	utils.Success(c, stats)
}

// RetryJob handles POST /api/jobs/:id/retry
func (h *BackgroundJobHandler) RetryJob(c *gin.Context) {
	jobID := c.Param("id")

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	// Get job to check ownership
	job, err := h.jobService.GetJob(jobID)
	if err != nil {
		utils.NotFound(c, "Job not found")
		return
	}

	// Check if user owns the job (or is admin)
	if job.UserID != userID.(string) {
		// In a real implementation, check if user is admin
		utils.Forbidden(c, "Access denied")
		return
	}

	// Check if job can be retried
	if job.Status != services.JobStatusFailed {
		utils.BadRequest(c, "Only failed jobs can be retried")
		return
	}

	// Create a new job with the same parameters
	newJob, err := h.jobService.EnqueueJob(job.Type, job.Payload, job.UserID)
	if err != nil {
		utils.BadRequest(c, "Failed to retry job: "+err.Error())
		return
	}

	utils.SuccessWithMessage(c, newJob, "Job retried successfully")
}

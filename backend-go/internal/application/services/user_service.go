package services

import (
	"context"
	"fmt"

	"adc-multi-languages/internal/application/dtos"
	"adc-multi-languages/internal/domain/entities"
	"adc-multi-languages/internal/domain/repositories"
	"adc-multi-languages/internal/domain/valueobjects"
	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
)

// UserService defines the interface for user application services
type UserService interface {
	// User management
	CreateUser(ctx context.Context, req *dtos.CreateUserRequest) (*dtos.UserResponse, error)
	GetUserByID(ctx context.Context, id uuid.UUID) (*dtos.UserResponse, error)
	GetUserByEmail(ctx context.Context, email string) (*dtos.UserResponse, error)
	UpdateUser(ctx context.Context, id uuid.UUID, req *dtos.UpdateUserRequest) (*dtos.UserResponse, error)
	DeleteUser(ctx context.Context, id uuid.UUID) error
	ListUsers(ctx context.Context, req *dtos.UserListRequest) (*dtos.ListResponse, error)
	
	// Authentication
	AuthenticateUser(ctx context.Context, email, password string) (*dtos.UserResponse, error)
	ChangePassword(ctx context.Context, userID uuid.UUID, req *dtos.ChangePasswordRequest) error
	
	// Email verification
	RequestEmailVerification(ctx context.Context, userID uuid.UUID) error
	VerifyEmail(ctx context.Context, token string) error
	
	// Password reset
	RequestPasswordReset(ctx context.Context, email string) error
	ResetPassword(ctx context.Context, token string, newPassword string) error
	
	// User status management
	ActivateUser(ctx context.Context, userID uuid.UUID) error
	DeactivateUser(ctx context.Context, userID uuid.UUID) error
}

// userServiceImpl implements the UserService interface
type userServiceImpl struct {
	repoManager repositories.RepositoryManager
}

// NewUserService creates a new user service
func NewUserService(repoManager repositories.RepositoryManager) UserService {
	return &userServiceImpl{
		repoManager: repoManager,
	}
}

// CreateUser creates a new user
func (s *userServiceImpl) CreateUser(ctx context.Context, req *dtos.CreateUserRequest) (*dtos.UserResponse, error) {
	// Validate email format
	email, err := valueobjects.NewEmail(req.Email)
	if err != nil {
		return nil, fmt.Errorf("invalid email: %w", err)
	}

	// Check if email already exists
	exists, err := s.repoManager.Users().EmailExists(ctx, email.Value())
	if err != nil {
		return nil, fmt.Errorf("failed to check email existence: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("user with email %s already exists", email.Value())
	}

	// Check if username already exists (if provided)
	if req.Username != nil && *req.Username != "" {
		exists, err := s.repoManager.Users().UsernameExists(ctx, *req.Username)
		if err != nil {
			return nil, fmt.Errorf("failed to check username existence: %w", err)
		}
		if exists {
			return nil, fmt.Errorf("user with username %s already exists", *req.Username)
		}
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create user entity
	user := entities.NewUser(email.Value(), string(hashedPassword))
	
	// Set optional fields
	if req.Username != nil {
		user.SetUsername(*req.Username)
	}
	if req.FirstName != nil {
		user.FirstName = req.FirstName
	}
	if req.LastName != nil {
		user.LastName = req.LastName
	}
	if req.Timezone != nil {
		user.Timezone = req.Timezone
	}
	if req.Language != nil {
		user.Language = req.Language
	}

	// Save user
	if err := s.repoManager.Users().Create(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	return s.mapUserToResponse(user), nil
}

// GetUserByID retrieves a user by ID
func (s *userServiceImpl) GetUserByID(ctx context.Context, id uuid.UUID) (*dtos.UserResponse, error) {
	user, err := s.repoManager.Users().GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("user not found")
	}

	return s.mapUserToResponse(user), nil
}

// GetUserByEmail retrieves a user by email
func (s *userServiceImpl) GetUserByEmail(ctx context.Context, email string) (*dtos.UserResponse, error) {
	user, err := s.repoManager.Users().GetByEmail(ctx, email)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("user not found")
	}

	return s.mapUserToResponse(user), nil
}

// UpdateUser updates user information
func (s *userServiceImpl) UpdateUser(ctx context.Context, id uuid.UUID, req *dtos.UpdateUserRequest) (*dtos.UserResponse, error) {
	// Get existing user
	user, err := s.repoManager.Users().GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("user not found")
	}

	// Check username uniqueness if changing
	if req.Username != nil && *req.Username != "" {
		if user.Username == nil || *user.Username != *req.Username {
			exists, err := s.repoManager.Users().UsernameExists(ctx, *req.Username)
			if err != nil {
				return nil, fmt.Errorf("failed to check username existence: %w", err)
			}
			if exists {
				return nil, fmt.Errorf("username %s already exists", *req.Username)
			}
		}
	}

	// Update user profile
	user.UpdateProfile(req.FirstName, req.LastName, req.Timezone, req.Language, req.ProfilePictureURL)
	
	if req.Username != nil {
		if err := user.SetUsername(*req.Username); err != nil {
			return nil, fmt.Errorf("failed to set username: %w", err)
		}
	}

	// Save updated user
	if err := s.repoManager.Users().Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	return s.mapUserToResponse(user), nil
}

// DeleteUser deletes a user
func (s *userServiceImpl) DeleteUser(ctx context.Context, id uuid.UUID) error {
	// Check if user exists
	user, err := s.repoManager.Users().GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return fmt.Errorf("user not found")
	}

	// Soft delete user
	if err := s.repoManager.Users().SoftDelete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}

	return nil
}

// ListUsers lists users with filtering and pagination
func (s *userServiceImpl) ListUsers(ctx context.Context, req *dtos.UserListRequest) (*dtos.ListResponse, error) {
	// Build filters
	filters := repositories.UserFilters{
		Email:         req.Email,
		Username:      req.Username,
		IsActive:      req.IsActive,
		EmailVerified: req.EmailVerified,
	}

	// Create pagination
	pagination := repositories.NewPagination(req.GetPage(), req.GetPerPage())

	var users []*entities.User
	var total int64
	var err error

	// If search query is provided, use search method
	if req.Search != nil && *req.Search != "" {
		users, total, err = s.repoManager.Users().SearchByEmailOrUsername(ctx, *req.Search, pagination)
	} else {
		users, total, err = s.repoManager.Users().List(ctx, filters, pagination)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to list users: %w", err)
	}

	// Convert to response DTOs
	userResponses := make([]*dtos.UserResponse, len(users))
	for i, user := range users {
		userResponses[i] = s.mapUserToResponse(user)
	}

	return &dtos.ListResponse{
		Data:       userResponses,
		Pagination: dtos.NewPaginationResponse(pagination.Page, pagination.PerPage, total),
	}, nil
}

// AuthenticateUser authenticates a user with email and password
func (s *userServiceImpl) AuthenticateUser(ctx context.Context, email, password string) (*dtos.UserResponse, error) {
	// Get user by email
	user, err := s.repoManager.Users().GetByEmail(ctx, email)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("invalid credentials")
	}

	// Check if user is active
	if !user.IsActive {
		return nil, fmt.Errorf("user account is deactivated")
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(password)); err != nil {
		return nil, fmt.Errorf("invalid credentials")
	}

	// Update last login
	user.UpdateLastLogin()
	if err := s.repoManager.Users().Update(ctx, user); err != nil {
		// Log error but don't fail authentication
		// TODO: Add proper logging
	}

	return s.mapUserToResponse(user), nil
}

// ChangePassword changes a user's password
func (s *userServiceImpl) ChangePassword(ctx context.Context, userID uuid.UUID, req *dtos.ChangePasswordRequest) error {
	// Get user
	user, err := s.repoManager.Users().GetByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return fmt.Errorf("user not found")
	}

	// Verify current password
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.CurrentPassword)); err != nil {
		return fmt.Errorf("current password is incorrect")
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash new password: %w", err)
	}

	// Update password
	user.PasswordHash = string(hashedPassword)
	user.Touch()

	if err := s.repoManager.Users().Update(ctx, user); err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}

	return nil
}

// Placeholder implementations for other methods
func (s *userServiceImpl) RequestEmailVerification(ctx context.Context, userID uuid.UUID) error {
	// TODO: Implement email verification logic
	return fmt.Errorf("not implemented")
}

func (s *userServiceImpl) VerifyEmail(ctx context.Context, token string) error {
	// TODO: Implement email verification logic
	return fmt.Errorf("not implemented")
}

func (s *userServiceImpl) RequestPasswordReset(ctx context.Context, email string) error {
	// TODO: Implement password reset logic
	return fmt.Errorf("not implemented")
}

func (s *userServiceImpl) ResetPassword(ctx context.Context, token string, newPassword string) error {
	// TODO: Implement password reset logic
	return fmt.Errorf("not implemented")
}

func (s *userServiceImpl) ActivateUser(ctx context.Context, userID uuid.UUID) error {
	user, err := s.repoManager.Users().GetByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return fmt.Errorf("user not found")
	}

	user.Activate()
	return s.repoManager.Users().Update(ctx, user)
}

func (s *userServiceImpl) DeactivateUser(ctx context.Context, userID uuid.UUID) error {
	user, err := s.repoManager.Users().GetByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return fmt.Errorf("user not found")
	}

	user.Deactivate()
	return s.repoManager.Users().Update(ctx, user)
}

// mapUserToResponse converts a user entity to response DTO
func (s *userServiceImpl) mapUserToResponse(user *entities.User) *dtos.UserResponse {
	return &dtos.UserResponse{
		ID:                user.ID.String(),
		Email:             user.Email,
		Username:          user.Username,
		FirstName:         user.FirstName,
		LastName:          user.LastName,
		FullName:          user.GetFullName(),
		DisplayName:       user.GetDisplayName(),
		EmailVerified:     user.EmailVerified,
		EmailVerifiedAt:   user.EmailVerifiedAt,
		LastLoginAt:       user.LastLoginAt,
		IsActive:          user.IsActive,
		ProfilePictureURL: user.ProfilePictureURL,
		Timezone:          user.Timezone,
		Language:          user.Language,
		CreatedAt:         user.CreatedAt,
		UpdatedAt:         user.UpdatedAt,
	}
}
package dtos

// PaginationRequest represents pagination parameters in requests
type PaginationRequest struct {
	Page    int `json:"page" form:"page" validate:"min=1"`
	PerPage int `json:"per_page" form:"per_page" validate:"min=1,max=100"`
}

// GetPage returns the page number, defaulting to 1 if not set
func (p *PaginationRequest) GetPage() int {
	if p.Page <= 0 {
		return 1
	}
	return p.Page
}

// GetPerPage returns the per page count, defaulting to 10 if not set
func (p *PaginationRequest) GetPerPage() int {
	if p.PerPage <= 0 {
		return 10
	}
	if p.PerPage > 100 {
		return 100
	}
	return p.PerPage
}

// PaginationResponse represents pagination information in responses
type PaginationResponse struct {
	Page       int   `json:"page"`
	PerPage    int   `json:"per_page"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
	HasNext    bool  `json:"has_next"`
	HasPrev    bool  `json:"has_prev"`
}

// NewPaginationResponse creates a new pagination response
func NewPaginationResponse(page, perPage int, total int64) *PaginationResponse {
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))
	if totalPages == 0 {
		totalPages = 1
	}

	return &PaginationResponse{
		Page:       page,
		PerPage:    perPage,
		Total:      total,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	}
}

// ListResponse represents a generic list response with pagination
type ListResponse struct {
	Data       interface{}         `json:"data"`
	Pagination *PaginationResponse `json:"pagination"`
}

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error   string                 `json:"error"`
	Message string                 `json:"message"`
	Code    string                 `json:"code,omitempty"`
	Details map[string]interface{} `json:"details,omitempty"`
}

// SuccessResponse represents a success response
type SuccessResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
}

// ValidationErrorResponse represents validation error details
type ValidationErrorResponse struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Value   string `json:"value,omitempty"`
}

// SortRequest represents sorting parameters
type SortRequest struct {
	Field string `json:"field" form:"sort_field"`
	Order string `json:"order" form:"sort_order" validate:"omitempty,oneof=asc desc"`
}

// GetOrder returns the sort order, defaulting to "asc" if not set
func (s *SortRequest) GetOrder() string {
	if s.Order == "" {
		return "asc"
	}
	return s.Order
}

// DateRangeRequest represents date range filtering parameters
type DateRangeRequest struct {
	StartDate *string `json:"start_date" form:"start_date" validate:"omitempty,datetime=2006-01-02"`
	EndDate   *string `json:"end_date" form:"end_date" validate:"omitempty,datetime=2006-01-02"`
}
package dtos

import (
	"time"

	"github.com/google/uuid"
)

// CreateUserRequest represents a user creation request
type CreateUserRequest struct {
	Email     string  `json:"email" binding:"required,email"`
	Username  *string `json:"username" binding:"omitempty,min=3,max=50"`
	Password  string  `json:"password" binding:"required,min=8"`
	FirstName *string `json:"first_name" binding:"omitempty,max=100"`
	LastName  *string `json:"last_name" binding:"omitempty,max=100"`
}

// UpdateUserRequest represents a user update request
type UpdateUserRequest struct {
	Username          *string `json:"username" binding:"omitempty,min=3,max=50"`
	FirstName         *string `json:"first_name" binding:"omitempty,max=100"`
	LastName          *string `json:"last_name" binding:"omitempty,max=100"`
	ProfilePictureURL *string `json:"profile_picture_url" binding:"omitempty,url"`
	Timezone          *string `json:"timezone" binding:"omitempty,max=50"`
	Language          *string `json:"language" binding:"omitempty,min=2,max=10"`
}

// ChangePasswordRequest represents a password change request
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" binding:"required"`
	NewPassword     string `json:"new_password" binding:"required,min=8"`
}

// UserResponse represents a user in API responses
type UserResponse struct {
	ID                uuid.UUID  `json:"id"`
	Email             string     `json:"email"`
	Username          *string    `json:"username"`
	FirstName         *string    `json:"first_name"`
	LastName          *string    `json:"last_name"`
	FullName          string     `json:"full_name"`
	EmailVerified     bool       `json:"email_verified"`
	EmailVerifiedAt   *time.Time `json:"email_verified_at"`
	LastLoginAt       *time.Time `json:"last_login_at"`
	IsActive          bool       `json:"is_active"`
	ProfilePictureURL *string    `json:"profile_picture_url"`
	Timezone          *string    `json:"timezone"`
	Language          *string    `json:"language"`
	CreatedAt         time.Time  `json:"created_at"`
	UpdatedAt         time.Time  `json:"updated_at"`
}

// UserListRequest represents a user list request with filters
type UserListRequest struct {
	Page          int     `form:"page" binding:"omitempty,min=1"`
	PageSize      int     `form:"page_size" binding:"omitempty,min=1,max=100"`
	Search        string  `form:"search" binding:"omitempty,max=100"`
	IsActive      *bool   `form:"is_active"`
	EmailVerified *bool   `form:"email_verified"`
	OrderBy       string  `form:"order_by" binding:"omitempty,oneof=created_at updated_at email username"`
	Order         string  `form:"order" binding:"omitempty,oneof=asc desc ASC DESC"`
}

// UserListResponse represents a paginated user list response
type UserListResponse struct {
	Users      []UserResponse   `json:"users"`
	Pagination PaginationResult `json:"pagination"`
}

// PaginationResult contains pagination metadata
type PaginationResult struct {
	Total       int64 `json:"total"`
	TotalPages  int   `json:"total_pages"`
	CurrentPage int   `json:"current_page"`
	PageSize    int   `json:"page_size"`
	HasNext     bool  `json:"has_next"`
	HasPrev     bool  `json:"has_prev"`
}
package dtos

import (
	"time"
)

// CreateUserRequest represents a request to create a new user
type CreateUserRequest struct {
	Email           string  `json:"email" validate:"required,email"`
	Password        string  `json:"password" validate:"required,min=8"`
	Username        *string `json:"username" validate:"omitempty,min=3,max=50"`
	FirstName       *string `json:"first_name" validate:"omitempty,max=100"`
	LastName        *string `json:"last_name" validate:"omitempty,max=100"`
	Timezone        *string `json:"timezone" validate:"omitempty"`
	Language        *string `json:"language" validate:"omitempty,len=2"`
}

// UpdateUserRequest represents a request to update user information
type UpdateUserRequest struct {
	Username          *string `json:"username" validate:"omitempty,min=3,max=50"`
	FirstName         *string `json:"first_name" validate:"omitempty,max=100"`
	LastName          *string `json:"last_name" validate:"omitempty,max=100"`
	Timezone          *string `json:"timezone" validate:"omitempty"`
	Language          *string `json:"language" validate:"omitempty,len=2"`
	ProfilePictureURL *string `json:"profile_picture_url" validate:"omitempty,url"`
}

// UserResponse represents a user in API responses
type UserResponse struct {
	ID                string     `json:"id"`
	Email             string     `json:"email"`
	Username          *string    `json:"username"`
	FirstName         *string    `json:"first_name"`
	LastName          *string    `json:"last_name"`
	FullName          string     `json:"full_name"`
	DisplayName       string     `json:"display_name"`
	EmailVerified     bool       `json:"email_verified"`
	EmailVerifiedAt   *time.Time `json:"email_verified_at"`
	LastLoginAt       *time.Time `json:"last_login_at"`
	IsActive          bool       `json:"is_active"`
	ProfilePictureURL *string    `json:"profile_picture_url"`
	Timezone          *string    `json:"timezone"`
	Language          *string    `json:"language"`
	CreatedAt         time.Time  `json:"created_at"`
	UpdatedAt         time.Time  `json:"updated_at"`
}

// UserListRequest represents a request to list users with filters
type UserListRequest struct {
	PaginationRequest
	Email         *string `json:"email" form:"email"`
	Username      *string `json:"username" form:"username"`
	IsActive      *bool   `json:"is_active" form:"is_active"`
	EmailVerified *bool   `json:"email_verified" form:"email_verified"`
	Search        *string `json:"search" form:"search"`
}

// ChangePasswordRequest represents a request to change user password
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" validate:"required"`
	NewPassword     string `json:"new_password" validate:"required,min=8"`
}

// VerifyEmailRequest represents a request to verify email
type VerifyEmailRequest struct {
	Token string `json:"token" validate:"required"`
}

// RequestPasswordResetRequest represents a request to reset password
type RequestPasswordResetRequest struct {
	Email string `json:"email" validate:"required,email"`
}

// ResetPasswordRequest represents a request to reset password with token
type ResetPasswordRequest struct {
	Token       string `json:"token" validate:"required"`
	NewPassword string `json:"new_password" validate:"required,min=8"`
}
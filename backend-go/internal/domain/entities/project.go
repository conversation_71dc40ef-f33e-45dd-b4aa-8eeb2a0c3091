package entities

import (
	"fmt"

	"github.com/google/uuid"
)

// Project represents a project in the system
type Project struct {
	BaseEntity
	Name           string
	Slug           string
	Description    *string
	OrganizationID uuid.UUID
	DefaultLocale  *string
	IsPublic       *bool
	CreatedBy      *uuid.UUID
}

// NewProject creates a new project entity
func NewProject(name, slug string, organizationID uuid.UUID, createdBy *uuid.UUID) (*Project, error) {
	if !isValidSlug(slug) {
		return nil, fmt.Errorf("invalid slug format: must contain only lowercase letters, numbers, and hyphens")
	}

	return &Project{
		BaseEntity:     NewBaseEntity(),
		Name:           name,
		Slug:           slug,
		OrganizationID: organizationID,
		DefaultLocale:  stringPtr("en"),
		IsPublic:       boolPtr(false),
		CreatedBy:      createdBy,
	}, nil
}

// UpdateBasicInfo updates project's basic information
func (p *Project) UpdateBasicInfo(name, slug string, description *string) error {
	if slug != "" && !isValidSlug(slug) {
		return fmt.Errorf("invalid slug format")
	}

	if name != "" {
		p.Name = name
	}
	if slug != "" {
		p.Slug = slug
	}
	if description != nil {
		p.Description = description
	}
	p.Touch()
	return nil
}

// SetDefaultLocale sets the default locale for the project
func (p *Project) SetDefaultLocale(locale string) {
	p.DefaultLocale = &locale
	p.Touch()
}

// SetVisibility sets the project visibility
func (p *Project) SetVisibility(isPublic bool) {
	p.IsPublic = &isPublic
	p.Touch()
}

// IsOwnedBy checks if the project is owned by the given user
func (p *Project) IsOwnedBy(userID uuid.UUID) bool {
	return p.CreatedBy != nil && *p.CreatedBy == userID
}

// BelongsToOrganization checks if the project belongs to the given organization
func (p *Project) BelongsToOrganization(organizationID uuid.UUID) bool {
	return p.OrganizationID == organizationID
}

// ProjectLocale represents a locale associated with a project
type ProjectLocale struct {
	BaseEntity
	ProjectID uuid.UUID
	LocaleID  uuid.UUID
	IsSource  *bool
	IsActive  bool
}

// NewProjectLocale creates a new project locale association
func NewProjectLocale(projectID, localeID uuid.UUID, isSource bool) *ProjectLocale {
	return &ProjectLocale{
		BaseEntity: NewBaseEntity(),
		ProjectID:  projectID,
		LocaleID:   localeID,
		IsSource:   &isSource,
		IsActive:   true,
	}
}

// SetAsSource marks this locale as the source language for the project
func (pl *ProjectLocale) SetAsSource() {
	pl.IsSource = boolPtr(true)
	pl.Touch()
}

// SetAsTarget marks this locale as a target language for the project
func (pl *ProjectLocale) SetAsTarget() {
	pl.IsSource = boolPtr(false)
	pl.Touch()
}

// Activate activates the project locale
func (pl *ProjectLocale) Activate() {
	pl.IsActive = true
	pl.Touch()
}

// Deactivate deactivates the project locale
func (pl *ProjectLocale) Deactivate() {
	pl.IsActive = false
	pl.Touch()
}

// Resource represents a resource in a project
type Resource struct {
	BaseEntity
	ProjectID   uuid.UUID
	Name        string
	Type        string // e.g., "json", "yaml", "properties"
	Path        *string
	Description *string
	CreatedBy   *uuid.UUID
}

// NewResource creates a new resource entity
func NewResource(projectID uuid.UUID, name, resourceType string, createdBy *uuid.UUID) *Resource {
	return &Resource{
		BaseEntity: NewBaseEntity(),
		ProjectID:  projectID,
		Name:       name,
		Type:       resourceType,
		CreatedBy:  createdBy,
	}
}

// UpdateInfo updates resource information
func (r *Resource) UpdateInfo(name, resourceType string, path, description *string) {
	if name != "" {
		r.Name = name
	}
	if resourceType != "" {
		r.Type = resourceType
	}
	if path != nil {
		r.Path = path
	}
	if description != nil {
		r.Description = description
	}
	r.Touch()
}

// BelongsToProject checks if the resource belongs to the given project
func (r *Resource) BelongsToProject(projectID uuid.UUID) bool {
	return r.ProjectID == projectID
}

// IsOwnedBy checks if the resource is owned by the given user
func (r *Resource) IsOwnedBy(userID uuid.UUID) bool {
	return r.CreatedBy != nil && *r.CreatedBy == userID
}
package entities

import (
	"time"

	"github.com/google/uuid"
)

// BaseEntity contains common fields for all domain entities
type BaseEntity struct {
	ID        uuid.UUID
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt *time.Time
}

// NewBaseEntity creates a new base entity with generated ID and current timestamp
func NewBaseEntity() BaseEntity {
	now := time.Now()
	return BaseEntity{
		ID:        uuid.New(),
		CreatedAt: now,
		UpdatedAt: now,
	}
}

// IsDeleted returns true if the entity is soft deleted
func (b *BaseEntity) IsDeleted() bool {
	return b.DeletedAt != nil
}

// MarkAsDeleted soft deletes the entity
func (b *BaseEntity) MarkAsDeleted() {
	now := time.Now()
	b.DeletedAt = &now
	b.UpdatedAt = now
}

// Touch updates the UpdatedAt timestamp
func (b *BaseEntity) Touch() {
	b.UpdatedAt = time.Now()
}
package entities

import (
	"fmt"
	"regexp"
	"time"

	"github.com/google/uuid"
)

// Organization represents an organization in the system
type Organization struct {
	BaseEntity
	Name                      string
	Slug                      string
	Description               *string
	Website                   *string
	LogoURL                   *string
	OwnerID                   uuid.UUID
	SubscriptionTier          string
	SubscriptionStatus        *string
	SubscriptionTierID        *uuid.UUID
	SubscriptionAutoRenew     *bool
	BillingPeriodStart        *time.Time
	BillingPeriodEnd          *time.Time
	StripeCustomerID          *string
	StripeSubscriptionID      *string
	AICreditsMonthlyAllowance *int
	AICreditsRemaining        *int
	AICreditsResetDate        *time.Time
}

// NewOrganization creates a new organization entity
func NewOrganization(name, slug string, ownerID uuid.UUID) (*Organization, error) {
	if !isValidSlug(slug) {
		return nil, fmt.Errorf("invalid slug format: must contain only lowercase letters, numbers, and hyphens")
	}

	return &Organization{
		BaseEntity:                NewBaseEntity(),
		Name:                      name,
		Slug:                      slug,
		OwnerID:                   ownerID,
		SubscriptionTier:          "free",
		SubscriptionAutoRenew:     boolPtr(true),
		AICreditsMonthlyAllowance: intPtr(1000),
		AICreditsRemaining:        intPtr(1000),
		AICreditsResetDate:        timePtr(time.Now().AddDate(0, 1, 0)), // Next month
	}, nil
}

// UpdateBasicInfo updates organization's basic information
func (o *Organization) UpdateBasicInfo(name, slug string, description, website, logoURL *string) error {
	if slug != "" && !isValidSlug(slug) {
		return fmt.Errorf("invalid slug format")
	}

	if name != "" {
		o.Name = name
	}
	if slug != "" {
		o.Slug = slug
	}
	if description != nil {
		o.Description = description
	}
	if website != nil {
		o.Website = website
	}
	if logoURL != nil {
		o.LogoURL = logoURL
	}
	o.Touch()
	return nil
}

// UpdateSubscription updates organization's subscription information
func (o *Organization) UpdateSubscription(tier, status string, tierID *uuid.UUID, autoRenew *bool) {
	o.SubscriptionTier = tier
	if status != "" {
		o.SubscriptionStatus = &status
	}
	if tierID != nil {
		o.SubscriptionTierID = tierID
	}
	if autoRenew != nil {
		o.SubscriptionAutoRenew = autoRenew
	}
	o.Touch()
}

// SetBillingPeriod sets the billing period for the organization
func (o *Organization) SetBillingPeriod(start, end time.Time) {
	o.BillingPeriodStart = &start
	o.BillingPeriodEnd = &end
	o.Touch()
}

// SetStripeInfo sets Stripe-related information
func (o *Organization) SetStripeInfo(customerID, subscriptionID *string) {
	if customerID != nil {
		o.StripeCustomerID = customerID
	}
	if subscriptionID != nil {
		o.StripeSubscriptionID = subscriptionID
	}
	o.Touch()
}

// UpdateAICredits updates AI credits information
func (o *Organization) UpdateAICredits(monthlyAllowance, remaining *int, resetDate *time.Time) {
	if monthlyAllowance != nil {
		o.AICreditsMonthlyAllowance = monthlyAllowance
	}
	if remaining != nil {
		o.AICreditsRemaining = remaining
	}
	if resetDate != nil {
		o.AICreditsResetDate = resetDate
	}
	o.Touch()
}

// ConsumeAICredits consumes AI credits and returns remaining credits
func (o *Organization) ConsumeAICredits(amount int) error {
	if o.AICreditsRemaining == nil {
		return fmt.Errorf("AI credits not initialized")
	}
	
	if *o.AICreditsRemaining < amount {
		return fmt.Errorf("insufficient AI credits: need %d, have %d", amount, *o.AICreditsRemaining)
	}
	
	*o.AICreditsRemaining -= amount
	o.Touch()
	return nil
}

// AddAICredits adds AI credits to the organization
func (o *Organization) AddAICredits(amount int) {
	if o.AICreditsRemaining == nil {
		o.AICreditsRemaining = &amount
	} else {
		*o.AICreditsRemaining += amount
	}
	o.Touch()
}

// ResetMonthlyAICredits resets AI credits to monthly allowance
func (o *Organization) ResetMonthlyAICredits() {
	if o.AICreditsMonthlyAllowance != nil {
		o.AICreditsRemaining = intPtr(*o.AICreditsMonthlyAllowance)
		nextMonth := time.Now().AddDate(0, 1, 0)
		o.AICreditsResetDate = &nextMonth
		o.Touch()
	}
}

// GetRemainingAICredits returns the remaining AI credits
func (o *Organization) GetRemainingAICredits() int {
	if o.AICreditsRemaining == nil {
		return 0
	}
	return *o.AICreditsRemaining
}

// IsOwner checks if the given user ID is the owner of the organization
func (o *Organization) IsOwner(userID uuid.UUID) bool {
	return o.OwnerID == userID
}

// isValidSlug validates slug format
func isValidSlug(slug string) bool {
	// Slug should contain only lowercase letters, numbers, and hyphens
	// Should not start or end with hyphen
	match, _ := regexp.MatchString(`^[a-z0-9]+(-[a-z0-9]+)*$`, slug)
	return match && len(slug) >= 1 && len(slug) <= 50
}

// Helper functions
func boolPtr(b bool) *bool {
	return &b
}

func intPtr(i int) *int {
	return &i
}

func timePtr(t time.Time) *time.Time {
	return &t
}
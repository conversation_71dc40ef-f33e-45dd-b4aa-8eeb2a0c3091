package entities

import (
	"time"

	"adc-multi-languages/internal/domain/valueobjects"
	"github.com/google/uuid"
)

// User represents a user in the domain
type User struct {
	id                uuid.UUID
	email             valueobjects.Email
	username          *string
	passwordHash      string
	firstName         *string
	lastName          *string
	emailVerified     bool
	emailVerifiedAt   *time.Time
	lastLoginAt       *time.Time
	isActive          bool
	profilePictureURL *string
	timezone          *string
	language          *valueobjects.LocaleCode
	createdAt         time.Time
	updatedAt         time.Time
}

// New<PERSON><PERSON> creates a new User entity
func NewUser(email valueobjects.Email, passwordHash string) (*User, error) {
	now := time.Now()
	
	return &User{
		id:            uuid.New(),
		email:         email,
		passwordHash:  passwordHash,
		emailVerified: false,
		isActive:      true,
		createdAt:     now,
		updatedAt:     now,
	}, nil
}

// ReconstructUser reconstructs a User entity from persistence
func ReconstructUser(
	id uuid.UUID,
	email valueobjects.Email,
	username *string,
	passwordHash string,
	firstName *string,
	lastName *string,
	emailVerified bool,
	emailVerifiedAt *time.Time,
	lastLoginAt *time.Time,
	isActive bool,
	profilePictureURL *string,
	timezone *string,
	language *valueobjects.LocaleCode,
	createdAt time.Time,
	updatedAt time.Time,
) *User {
	return &User{
		id:                id,
		email:             email,
		username:          username,
		passwordHash:      passwordHash,
		firstName:         firstName,
		lastName:          lastName,
		emailVerified:     emailVerified,
		emailVerifiedAt:   emailVerifiedAt,
		lastLoginAt:       lastLoginAt,
		isActive:          isActive,
		profilePictureURL: profilePictureURL,
		timezone:          timezone,
		language:          language,
		createdAt:         createdAt,
		updatedAt:         updatedAt,
	}
}

// Getters
func (u *User) ID() uuid.UUID {
	return u.id
}

func (u *User) Email() valueobjects.Email {
	return u.email
}

func (u *User) Username() *string {
	return u.username
}

func (u *User) PasswordHash() string {
	return u.passwordHash
}

func (u *User) FirstName() *string {
	return u.firstName
}

func (u *User) LastName() *string {
	return u.lastName
}

func (u *User) EmailVerified() bool {
	return u.emailVerified
}

func (u *User) EmailVerifiedAt() *time.Time {
	return u.emailVerifiedAt
}

func (u *User) LastLoginAt() *time.Time {
	return u.lastLoginAt
}

func (u *User) IsActive() bool {
	return u.isActive
}

func (u *User) ProfilePictureURL() *string {
	return u.profilePictureURL
}

func (u *User) Timezone() *string {
	return u.timezone
}

func (u *User) Language() *valueobjects.LocaleCode {
	return u.language
}

func (u *User) CreatedAt() time.Time {
	return u.createdAt
}

func (u *User) UpdatedAt() time.Time {
	return u.updatedAt
}

// Business methods
func (u *User) UpdateProfile(firstName, lastName *string, profilePictureURL, timezone *string) {
	u.firstName = firstName
	u.lastName = lastName
	u.profilePictureURL = profilePictureURL
	u.timezone = timezone
	u.updatedAt = time.Now()
}

func (u *User) SetUsername(username string) {
	u.username = &username
	u.updatedAt = time.Now()
}

func (u *User) SetLanguage(language valueobjects.LocaleCode) {
	u.language = &language
	u.updatedAt = time.Now()
}

func (u *User) VerifyEmail() {
	now := time.Now()
	u.emailVerified = true
	u.emailVerifiedAt = &now
	u.updatedAt = now
}

func (u *User) UpdateLastLogin() {
	now := time.Now()
	u.lastLoginAt = &now
	u.updatedAt = now
}

func (u *User) ChangePassword(newPasswordHash string) {
	u.passwordHash = newPasswordHash
	u.updatedAt = time.Now()
}

func (u *User) Activate() {
	u.isActive = true
	u.updatedAt = time.Now()
}

func (u *User) Deactivate() {
	u.isActive = false
	u.updatedAt = time.Now()
}

// FullName returns the user's full name if available
func (u *User) FullName() string {
	if u.firstName != nil && u.lastName != nil {
		return *u.firstName + " " + *u.lastName
	}
	if u.firstName != nil {
		return *u.firstName
	}
	if u.lastName != nil {
		return *u.lastName
	}
	if u.username != nil {
		return *u.username
	}
	return u.email.Value()
}

// CanLogin checks if the user can login
func (u *User) CanLogin() bool {
	return u.isActive && u.emailVerified
}
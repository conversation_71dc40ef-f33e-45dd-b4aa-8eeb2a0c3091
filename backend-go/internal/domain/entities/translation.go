package entities

import (
	"fmt"
	"time"

	"github.com/google/uuid"
)

// TranslationKey represents a translation key in the system
type TranslationKey struct {
	BaseEntity
	ResourceID    *uuid.UUID
	ProjectID     *uuid.UUID
	KeyName       string
	Description   *string
	Context       *string
	IsPlural      *bool
	MaxLength     *int
	ScreenshotURL *string
	CreatedBy     *uuid.UUID
}

// NewTranslationKey creates a new translation key entity
func NewTranslationKey(keyName string, projectID *uuid.UUID, createdBy *uuid.UUID) *TranslationKey {
	return &TranslationKey{
		BaseEntity: NewBaseEntity(),
		KeyName:    keyName,
		ProjectID:  projectID,
		IsPlural:   boolPtr(false),
		CreatedBy:  createdBy,
	}
}

// UpdateInfo updates translation key information
func (tk *TranslationKey) UpdateInfo(description, context *string, isPlural *bool, maxLength *int, screenshotURL *string) {
	if description != nil {
		tk.Description = description
	}
	if context != nil {
		tk.Context = context
	}
	if isPlural != nil {
		tk.IsPlural = isPlural
	}
	if maxLength != nil {
		tk.MaxLength = maxLength
	}
	if screenshotURL != nil {
		tk.ScreenshotURL = screenshotURL
	}
	tk.Touch()
}

// SetResource associates the translation key with a resource
func (tk *TranslationKey) SetResource(resourceID uuid.UUID) {
	tk.ResourceID = &resourceID
	tk.Touch()
}

// BelongsToProject checks if the translation key belongs to the given project
func (tk *TranslationKey) BelongsToProject(projectID uuid.UUID) bool {
	return tk.ProjectID != nil && *tk.ProjectID == projectID
}

// BelongsToResource checks if the translation key belongs to the given resource
func (tk *TranslationKey) BelongsToResource(resourceID uuid.UUID) bool {
	return tk.ResourceID != nil && *tk.ResourceID == resourceID
}

// Translation represents a translation for a specific key and locale
type Translation struct {
	BaseEntity
	KeyID      uuid.UUID
	LocaleID   uuid.UUID
	Content    string
	IsFuzzy    *bool
	IsReviewed *bool
	ReviewedBy *uuid.UUID
	ReviewedAt *time.Time
	CreatedBy  *uuid.UUID
}

// NewTranslation creates a new translation entity
func NewTranslation(keyID, localeID uuid.UUID, content string, createdBy *uuid.UUID) *Translation {
	return &Translation{
		BaseEntity: NewBaseEntity(),
		KeyID:      keyID,
		LocaleID:   localeID,
		Content:    content,
		IsFuzzy:    boolPtr(false),
		IsReviewed: boolPtr(false),
		CreatedBy:  createdBy,
	}
}

// UpdateContent updates the translation content
func (t *Translation) UpdateContent(content string) {
	t.Content = content
	t.IsFuzzy = boolPtr(false) // Reset fuzzy status when content is updated
	t.IsReviewed = boolPtr(false) // Reset review status when content is updated
	t.ReviewedBy = nil
	t.ReviewedAt = nil
	t.Touch()
}

// MarkAsFuzzy marks the translation as fuzzy
func (t *Translation) MarkAsFuzzy() {
	t.IsFuzzy = boolPtr(true)
	t.Touch()
}

// MarkAsExact marks the translation as exact (not fuzzy)
func (t *Translation) MarkAsExact() {
	t.IsFuzzy = boolPtr(false)
	t.Touch()
}

// Approve approves the translation by a reviewer
func (t *Translation) Approve(reviewerID uuid.UUID) {
	t.IsReviewed = boolPtr(true)
	t.ReviewedBy = &reviewerID
	now := time.Now()
	t.ReviewedAt = &now
	t.Touch()
}

// Reject rejects the translation by a reviewer
func (t *Translation) Reject(reviewerID uuid.UUID) {
	t.IsReviewed = boolPtr(false)
	t.ReviewedBy = &reviewerID
	now := time.Now()
	t.ReviewedAt = &now
	t.Touch()
}

// ResetReviewStatus resets the review status of the translation
func (t *Translation) ResetReviewStatus() {
	t.IsReviewed = boolPtr(false)
	t.ReviewedBy = nil
	t.ReviewedAt = nil
	t.Touch()
}

// IsApproved returns true if the translation is approved
func (t *Translation) IsApproved() bool {
	return t.IsReviewed != nil && *t.IsReviewed
}

// IsFuzzyTranslation returns true if the translation is fuzzy
func (t *Translation) IsFuzzyTranslation() bool {
	return t.IsFuzzy != nil && *t.IsFuzzy
}

// ValidateContent validates the translation content
func (t *Translation) ValidateContent(maxLength *int) error {
	if t.Content == "" {
		return fmt.Errorf("translation content cannot be empty")
	}

	if maxLength != nil && len(t.Content) > *maxLength {
		return fmt.Errorf("translation content exceeds maximum length of %d characters", *maxLength)
	}

	return nil
}

// TranslationHistory represents the history of changes to a translation
type TranslationHistory struct {
	BaseEntity
	TranslationID uuid.UUID
	Content       string
	Action        string // created, updated, deleted
	PerformedBy   *uuid.UUID
}

// NewTranslationHistory creates a new translation history entry
func NewTranslationHistory(translationID uuid.UUID, content, action string, performedBy *uuid.UUID) *TranslationHistory {
	return &TranslationHistory{
		BaseEntity:    NewBaseEntity(),
		TranslationID: translationID,
		Content:       content,
		Action:        action,
		PerformedBy:   performedBy,
	}
}

// Locale represents a locale/language in the system
type Locale struct {
	BaseEntity
	Code       string // e.g., "en", "es", "fr"
	Name       string // e.g., "English", "Spanish", "French"
	NativeName string // e.g., "English", "Español", "Français"
	IsActive   bool
	Direction  string // "ltr" or "rtl"
}

// NewLocale creates a new locale entity
func NewLocale(code, name, nativeName string) *Locale {
	return &Locale{
		BaseEntity: NewBaseEntity(),
		Code:       code,
		Name:       name,
		NativeName: nativeName,
		IsActive:   true,
		Direction:  "ltr",
	}
}

// Activate activates the locale
func (l *Locale) Activate() {
	l.IsActive = true
	l.Touch()
}

// Deactivate deactivates the locale
func (l *Locale) Deactivate() {
	l.IsActive = false
	l.Touch()
}

// SetDirection sets the text direction for the locale
func (l *Locale) SetDirection(direction string) error {
	if direction != "ltr" && direction != "rtl" {
		return fmt.Errorf("invalid direction: must be 'ltr' or 'rtl'")
	}
	l.Direction = direction
	l.Touch()
	return nil
}

// UpdateInfo updates locale information
func (l *Locale) UpdateInfo(name, nativeName *string) {
	if name != nil {
		l.Name = *name
	}
	if nativeName != nil {
		l.NativeName = *nativeName
	}
	l.Touch()
}
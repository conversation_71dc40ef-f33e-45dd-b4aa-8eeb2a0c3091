package services

import (
	"context"

	"adc-multi-languages/internal/domain/entities"
	"github.com/google/uuid"
)

// TranslationDomainService defines domain-level translation business logic
type TranslationDomainService interface {
	// Translation validation and business rules
	ValidateTranslation(ctx context.Context, translation *entities.Translation, key *entities.TranslationKey) error
	
	// AI translation integration
	GenerateAITranslation(ctx context.Context, sourceText, sourceLocale, targetLocale string, options AITranslationOptions) (*AITranslationResult, error)
	
	// Translation quality checks
	CheckTranslationQuality(ctx context.Context, translation *entities.Translation, sourceTranslation *entities.Translation) (*QualityReport, error)
	
	// Bulk operations
	ValidateBulkTranslations(ctx context.Context, translations []*entities.Translation, keys []*entities.TranslationKey) error
	
	// Translation progress calculation
	CalculateProjectProgress(ctx context.Context, projectID uuid.UUID, localeID uuid.UUID) (*TranslationProgress, error)
}

// AITranslationOptions defines options for AI translation
type AITranslationOptions struct {
	Provider     string // openai, google, azure
	Context      *string
	Tone         *string // formal, casual, technical
	PreserveHTML bool
	MaxLength    *int
}

// AITranslationResult represents the result of AI translation
type AITranslationResult struct {
	TranslatedText   string
	Confidence       float64
	AlternativeTexts []string
	DetectedLanguage *string
	Provider         string
	CostInCredits    int
}

// QualityReport represents translation quality assessment
type QualityReport struct {
	Score              float64 // 0-100
	Issues             []QualityIssue
	Suggestions        []string
	RequiresReview     bool
	EstimatedAccuracy  float64
}

// QualityIssue represents a specific quality issue
type QualityIssue struct {
	Type        string // length_mismatch, missing_variables, formatting_error, etc.
	Severity    string // low, medium, high, critical
	Description string
	Position    *int // character position where issue occurs
}

// TranslationProgress represents translation progress for a project/locale
type TranslationProgress struct {
	ProjectID      uuid.UUID
	LocaleID       uuid.UUID
	TotalKeys      int64
	TranslatedKeys int64
	ReviewedKeys   int64
	FuzzyKeys      int64
	
	// Calculated percentages
	CompletionRate float64 // TranslatedKeys / TotalKeys * 100
	ReviewRate     float64 // ReviewedKeys / TranslatedKeys * 100
	QualityRate    float64 // (TranslatedKeys - FuzzyKeys) / TranslatedKeys * 100
}
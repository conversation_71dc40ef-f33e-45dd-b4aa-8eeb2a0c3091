package services

import (
	"context"

	"adc-multi-languages/internal/domain/entities"
	"github.com/google/uuid"
)

// OrganizationDomainService defines domain-level organization business logic
type OrganizationDomainService interface {
	// Organization validation and business rules
	ValidateOrganizationCreation(ctx context.Context, org *entities.Organization) error
	ValidateOrganizationUpdate(ctx context.Context, org *entities.Organization, updates map[string]interface{}) error
	
	// Membership management
	CanUserJoinOrganization(ctx context.Context, userID, organizationID uuid.UUID) (bool, error)
	CanUserLeaveOrganization(ctx context.Context, userID, organizationID uuid.UUID) (bool, error)
	
	// Permission checks
	CanUserManageOrganization(ctx context.Context, userID, organizationID uuid.UUID) (bool, error)
	CanUserViewOrganization(ctx context.Context, userID, organizationID uuid.UUID) (bool, error)
	
	// Subscription and billing
	CanUpgradeSubscription(ctx context.Context, organizationID uuid.UUID, newTier string) (bool, error)
	CalculateSubscriptionCost(ctx context.Context, organizationID uuid.UUID, newTier string, billingPeriod string) (*SubscriptionCost, error)
	
	// AI Credits management
	CanConsumeAICredits(ctx context.Context, organizationID uuid.UUID, amount int) (bool, error)
	CalculateCreditConsumption(ctx context.Context, operation string, textLength int, sourceLocale, targetLocale string) (int, error)
	
	// Organization limits and quotas
	CheckOrganizationLimits(ctx context.Context, organizationID uuid.UUID) (*OrganizationLimits, error)
	CanCreateProject(ctx context.Context, organizationID uuid.UUID) (bool, error)
	CanAddUser(ctx context.Context, organizationID uuid.UUID) (bool, error)
}

// SubscriptionCost represents the cost calculation for subscription changes
type SubscriptionCost struct {
	CurrentTier     string
	NewTier         string
	BillingPeriod   string
	MonthlyPrice    float64
	YearlyPrice     *float64
	ProrationAmount *float64
	TotalCost       float64
	EffectiveDate   string
	NextBillingDate string
}

// OrganizationLimits represents current usage vs limits for an organization
type OrganizationLimits struct {
	SubscriptionTier string
	
	// Project limits
	MaxProjects     *int
	CurrentProjects int
	CanCreateProject bool
	
	// User limits
	MaxUsers     *int
	CurrentUsers int
	CanAddUser   bool
	
	// AI Credits
	MonthlyAICredits  *int
	RemainingAICredits *int
	CreditResetDate   *string
	
	// Storage limits (if applicable)
	MaxStorageMB     *int
	CurrentStorageMB *int
	
	// API limits
	MaxAPICallsPerMonth     *int
	CurrentAPICallsThisMonth *int
}
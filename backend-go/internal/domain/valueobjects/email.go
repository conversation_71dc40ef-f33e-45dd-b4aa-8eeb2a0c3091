package valueobjects

import (
	"fmt"
	"regexp"
	"strings"
)

// Email represents a valid email address
type Email struct {
	value string
}

// NewEmail creates a new Email value object
func NewEmail(email string) (*Email, error) {
	if err := validateEmail(email); err != nil {
		return nil, err
	}
	
	return &Email{
		value: strings.ToLower(strings.TrimSpace(email)),
	}, nil
}

// Value returns the email string value
func (e Email) Value() string {
	return e.value
}

// String implements the Stringer interface
func (e Email) String() string {
	return e.value
}

// Equals checks if two emails are equal
func (e Email) Equals(other Email) bool {
	return e.value == other.value
}

// validateEmail validates email format
func validateEmail(email string) error {
	email = strings.TrimSpace(email)
	
	if email == "" {
		return fmt.Errorf("email cannot be empty")
	}
	
	if len(email) > 254 {
		return fmt.Errorf("email is too long")
	}
	
	emailRegex := regexp.MustCompile(`^[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,}$`)
	if !emailRegex.MatchString(strings.ToLower(email)) {
		return fmt.Errorf("invalid email format")
	}
	
	return nil
}
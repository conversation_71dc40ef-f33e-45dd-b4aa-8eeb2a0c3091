package valueobjects

import (
	"fmt"
	"regexp"
	"strings"
)

// LocaleCode represents a valid locale code (e.g., "en", "en-US", "es-ES")
type LocaleCode struct {
	value string
}

// NewLocaleCode creates a new LocaleCode value object
func NewLocaleCode(code string) (*LocaleCode, error) {
	if err := validateLocaleCode(code); err != nil {
		return nil, err
	}
	
	return &LocaleCode{
		value: strings.ToLower(strings.TrimSpace(code)),
	}, nil
}

// Value returns the locale code string value
func (l LocaleCode) Value() string {
	return l.value
}

// String implements the Stringer interface
func (l LocaleCode) String() string {
	return l.value
}

// Equals checks if two locale codes are equal
func (l LocaleCode) Equals(other LocaleCode) bool {
	return l.value == other.value
}

// Language returns the language part of the locale code
func (l LocaleCode) Language() string {
	parts := strings.Split(l.value, "-")
	return parts[0]
}

// Country returns the country part of the locale code (if present)
func (l LocaleCode) Country() string {
	parts := strings.Split(l.value, "-")
	if len(parts) > 1 {
		return parts[1]
	}
	return ""
}

// validateLocaleCode validates locale code format
func validateLocaleCode(code string) error {
	code = strings.TrimSpace(code)
	
	if code == "" {
		return fmt.Errorf("locale code cannot be empty")
	}
	
	if len(code) < 2 || len(code) > 10 {
		return fmt.Errorf("locale code must be between 2 and 10 characters")
	}
	
	// Locale code format: language[_country] or language[-country]
	// Examples: en, en-US, en_US, es-ES, zh-CN
	localeRegex := regexp.MustCompile(`^[a-z]{2,3}([-_][a-z]{2,3})?$`)
	if !localeRegex.MatchString(strings.ToLower(code)) {
		return fmt.Errorf("invalid locale code format")
	}
	
	return nil
}
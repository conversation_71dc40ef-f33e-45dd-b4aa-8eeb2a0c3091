package valueobjects

import (
	"fmt"
	"regexp"
	"strings"
)

// Slug represents a URL-friendly slug
type Slug struct {
	value string
}

// NewSlug creates a new Slug value object
func NewSlug(slug string) (*Slug, error) {
	if err := validateSlug(slug); err != nil {
		return nil, err
	}
	
	return &Slug{
		value: strings.ToLower(strings.TrimSpace(slug)),
	}, nil
}

// Value returns the slug string value
func (s Slug) Value() string {
	return s.value
}

// String implements the Stringer interface
func (s Slug) String() string {
	return s.value
}

// Equals checks if two slugs are equal
func (s Slug) Equals(other Slug) bool {
	return s.value == other.value
}

// validateSlug validates slug format
func validateSlug(slug string) error {
	slug = strings.TrimSpace(slug)
	
	if slug == "" {
		return fmt.Errorf("slug cannot be empty")
	}
	
	if len(slug) < 2 {
		return fmt.Errorf("slug must be at least 2 characters long")
	}
	
	if len(slug) > 100 {
		return fmt.Errorf("slug is too long (max 100 characters)")
	}
	
	// Slug must contain only lowercase letters, numbers, hyphens
	slugRegex := regexp.MustCompile(`^[a-z0-9-]+$`)
	if !slugRegex.MatchString(strings.ToLower(slug)) {
		return fmt.Errorf("slug can only contain lowercase letters, numbers, and hyphens")
	}
	
	// Cannot start or end with hyphen
	if strings.HasPrefix(slug, "-") || strings.HasSuffix(slug, "-") {
		return fmt.Errorf("slug cannot start or end with a hyphen")
	}
	
	// Cannot have consecutive hyphens
	if strings.Contains(slug, "--") {
		return fmt.Errorf("slug cannot contain consecutive hyphens")
	}
	
	return nil
}
package repositories

// Pagination defines pagination parameters
type Pagination struct {
	Page    int
	PerPage int
	Offset  int
	Limit   int
}

// NewPagination creates a new pagination instance
func NewPagination(page, perPage int) Pagination {
	if page < 1 {
		page = 1
	}
	if perPage < 1 {
		perPage = 10
	}
	if perPage > 100 {
		perPage = 100
	}

	offset := (page - 1) * perPage
	return Pagination{
		Page:    page,
		PerPage: perPage,
		Offset:  offset,
		Limit:   perPage,
	}
}

// SortOrder defines sort direction
type SortOrder string

const (
	SortOrderASC  SortOrder = "asc"
	SortOrderDESC SortOrder = "desc"
)

// SortOption defines sorting options
type SortOption struct {
	Field string
	Order SortOrder
}

// UnitOfWork defines the interface for managing transactions
type UnitOfWork interface {
	Begin() error
	Commit() error
	Rollback() error
	InTransaction() bool
}

// RepositoryManager defines the interface for managing all repositories
type RepositoryManager interface {
	// Repository getters
	Users() UserRepository
	Organizations() OrganizationRepository
	Projects() ProjectRepository
	ProjectLocales() ProjectLocaleRepository
	Resources() ResourceRepository
	TranslationKeys() TranslationKeyRepository
	Translations() TranslationRepository
	TranslationHistory() TranslationHistoryRepository
	Locales() LocaleRepository
	
	// Transaction management
	UnitOfWork() UnitOfWork
	WithTx(fn func(RepositoryManager) error) error
}
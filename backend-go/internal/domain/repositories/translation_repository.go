package repositories

import (
	"context"

	"adc-multi-languages/internal/domain/entities"
	"github.com/google/uuid"
)

// TranslationKeyRepository defines the interface for translation key data access
type TranslationKeyRepository interface {
	// Create operations
	Create(ctx context.Context, key *entities.TranslationKey) error
	
	// Read operations
	GetByID(ctx context.Context, id uuid.UUID) (*entities.TranslationKey, error)
	GetByKeyName(ctx context.Context, projectID uuid.UUID, keyName string) (*entities.TranslationKey, error)
	GetByProjectID(ctx context.Context, projectID uuid.UUID, pagination Pagination) ([]*entities.TranslationKey, int64, error)
	GetByResourceID(ctx context.Context, resourceID uuid.UUID, pagination Pagination) ([]*entities.TranslationKey, int64, error)
	List(ctx context.Context, filters TranslationKeyFilters, pagination Pagination) ([]*entities.TranslationKey, int64, error)
	
	// Update operations
	Update(ctx context.Context, key *entities.TranslationKey) error
	UpdatePartial(ctx context.Context, id uuid.UUID, updates map[string]interface{}) error
	
	// Delete operations
	Delete(ctx context.Context, id uuid.UUID) error
	SoftDelete(ctx context.Context, id uuid.UUID) error
	
	// Specialized queries
	KeyNameExistsInProject(ctx context.Context, projectID uuid.UUID, keyName string) (bool, error)
	KeyNameExistsInProjectExcluding(ctx context.Context, projectID uuid.UUID, keyName string, excludeID uuid.UUID) (bool, error)
	SearchKeys(ctx context.Context, projectID uuid.UUID, query string, pagination Pagination) ([]*entities.TranslationKey, int64, error)
	GetKeysWithoutTranslations(ctx context.Context, projectID, localeID uuid.UUID) ([]*entities.TranslationKey, error)
}

// TranslationRepository defines the interface for translation data access
type TranslationRepository interface {
	// Create operations
	Create(ctx context.Context, translation *entities.Translation) error
	
	// Read operations
	GetByID(ctx context.Context, id uuid.UUID) (*entities.Translation, error)
	GetByKeyAndLocale(ctx context.Context, keyID, localeID uuid.UUID) (*entities.Translation, error)
	GetByKeyID(ctx context.Context, keyID uuid.UUID) ([]*entities.Translation, error)
	GetByLocaleID(ctx context.Context, localeID uuid.UUID, pagination Pagination) ([]*entities.Translation, int64, error)
	GetByProjectAndLocale(ctx context.Context, projectID, localeID uuid.UUID, pagination Pagination) ([]*entities.Translation, int64, error)
	List(ctx context.Context, filters TranslationFilters, pagination Pagination) ([]*entities.Translation, int64, error)
	
	// Update operations
	Update(ctx context.Context, translation *entities.Translation) error
	UpdatePartial(ctx context.Context, id uuid.UUID, updates map[string]interface{}) error
	
	// Delete operations
	Delete(ctx context.Context, id uuid.UUID) error
	SoftDelete(ctx context.Context, id uuid.UUID) error
	
	// Specialized queries
	TranslationExists(ctx context.Context, keyID, localeID uuid.UUID) (bool, error)
	GetPendingReviews(ctx context.Context, projectID uuid.UUID, pagination Pagination) ([]*entities.Translation, int64, error)
	GetFuzzyTranslations(ctx context.Context, projectID uuid.UUID, pagination Pagination) ([]*entities.Translation, int64, error)
	GetTranslationProgress(ctx context.Context, projectID, localeID uuid.UUID) (*TranslationProgress, error)
	BulkUpdate(ctx context.Context, translations []*entities.Translation) error
	SearchTranslations(ctx context.Context, projectID uuid.UUID, query string, pagination Pagination) ([]*entities.Translation, int64, error)
	GetTranslationsByKeys(ctx context.Context, keyIDs []uuid.UUID, localeID uuid.UUID) ([]*entities.Translation, error)
}

// TranslationHistoryRepository defines the interface for translation history data access
type TranslationHistoryRepository interface {
	// Create operations
	Create(ctx context.Context, history *entities.TranslationHistory) error
	
	// Read operations
	GetByTranslationID(ctx context.Context, translationID uuid.UUID, pagination Pagination) ([]*entities.TranslationHistory, int64, error)
	GetByID(ctx context.Context, id uuid.UUID) (*entities.TranslationHistory, error)
	
	// Specialized queries
	GetRecentChanges(ctx context.Context, projectID uuid.UUID, limit int) ([]*entities.TranslationHistory, error)
	GetUserActivity(ctx context.Context, userID uuid.UUID, projectID *uuid.UUID, pagination Pagination) ([]*entities.TranslationHistory, int64, error)
}

// LocaleRepository defines the interface for locale data access
type LocaleRepository interface {
	// Create operations
	Create(ctx context.Context, locale *entities.Locale) error
	
	// Read operations
	GetByID(ctx context.Context, id uuid.UUID) (*entities.Locale, error)
	GetByCode(ctx context.Context, code string) (*entities.Locale, error)
	List(ctx context.Context, filters LocaleFilters, pagination Pagination) ([]*entities.Locale, int64, error)
	GetActive(ctx context.Context, pagination Pagination) ([]*entities.Locale, int64, error)
	
	// Update operations
	Update(ctx context.Context, locale *entities.Locale) error
	UpdatePartial(ctx context.Context, id uuid.UUID, updates map[string]interface{}) error
	
	// Delete operations
	Delete(ctx context.Context, id uuid.UUID) error
	SoftDelete(ctx context.Context, id uuid.UUID) error
	
	// Specialized queries
	CodeExists(ctx context.Context, code string) (bool, error)
	SearchLocales(ctx context.Context, query string, pagination Pagination) ([]*entities.Locale, int64, error)
}

// Filters for repositories
type TranslationKeyFilters struct {
	ProjectID   *uuid.UUID
	ResourceID  *uuid.UUID
	KeyName     *string
	IsPlural    *bool
	CreatedBy   *uuid.UUID
	HasContext  *bool
}

type TranslationFilters struct {
	KeyID      *uuid.UUID
	LocaleID   *uuid.UUID
	ProjectID  *uuid.UUID
	IsFuzzy    *bool
	IsReviewed *bool
	ReviewedBy *uuid.UUID
	CreatedBy  *uuid.UUID
	Content    *string
}

type LocaleFilters struct {
	Code      *string
	Name      *string
	IsActive  *bool
	Direction *string
}

// Statistics and progress tracking
type TranslationProgress struct {
	TotalKeys        int64
	TranslatedKeys   int64
	ReviewedKeys     int64
	FuzzyKeys        int64
	CompletionRate   float64
	ReviewRate       float64
}
package repositories

import (
	"context"

	"adc-multi-languages/internal/domain/entities"
	"github.com/google/uuid"
)

// ProjectRepository defines the interface for project data access
type ProjectRepository interface {
	// Create operations
	Create(ctx context.Context, project *entities.Project) error
	
	// Read operations
	GetByID(ctx context.Context, id uuid.UUID) (*entities.Project, error)
	GetBySlug(ctx context.Context, organizationID uuid.UUID, slug string) (*entities.Project, error)
	List(ctx context.Context, filters ProjectFilters, pagination Pagination) ([]*entities.Project, int64, error)
	GetByOrganizationID(ctx context.Context, organizationID uuid.UUID, pagination Pagination) ([]*entities.Project, int64, error)
	GetByCreatorID(ctx context.Context, creatorID uuid.UUID, pagination Pagination) ([]*entities.Project, int64, error)
	
	// Update operations
	Update(ctx context.Context, project *entities.Project) error
	UpdatePartial(ctx context.Context, id uuid.UUID, updates map[string]interface{}) error
	
	// Delete operations
	Delete(ctx context.Context, id uuid.UUID) error
	SoftDelete(ctx context.Context, id uuid.UUID) error
	
	// Specialized queries
	SlugExistsInOrganization(ctx context.Context, organizationID uuid.UUID, slug string) (bool, error)
	SlugExistsInOrganizationExcluding(ctx context.Context, organizationID uuid.UUID, slug string, excludeID uuid.UUID) (bool, error)
	GetPublicProjects(ctx context.Context, pagination Pagination) ([]*entities.Project, int64, error)
	GetProjectStats(ctx context.Context, projectID uuid.UUID) (*ProjectStats, error)
}

// ProjectLocaleRepository defines the interface for project locale data access
type ProjectLocaleRepository interface {
	// Create operations
	Create(ctx context.Context, projectLocale *entities.ProjectLocale) error
	
	// Read operations
	GetByID(ctx context.Context, id uuid.UUID) (*entities.ProjectLocale, error)
	GetByProjectAndLocale(ctx context.Context, projectID, localeID uuid.UUID) (*entities.ProjectLocale, error)
	GetByProjectID(ctx context.Context, projectID uuid.UUID) ([]*entities.ProjectLocale, error)
	
	// Update operations
	Update(ctx context.Context, projectLocale *entities.ProjectLocale) error
	
	// Delete operations
	Delete(ctx context.Context, id uuid.UUID) error
	DeleteByProjectAndLocale(ctx context.Context, projectID, localeID uuid.UUID) error
	
	// Specialized queries
	GetActiveLocalesForProject(ctx context.Context, projectID uuid.UUID) ([]*entities.ProjectLocale, error)
	GetSourceLocaleForProject(ctx context.Context, projectID uuid.UUID) (*entities.ProjectLocale, error)
	LocaleExistsInProject(ctx context.Context, projectID, localeID uuid.UUID) (bool, error)
}

// ResourceRepository defines the interface for resource data access
type ResourceRepository interface {
	// Create operations
	Create(ctx context.Context, resource *entities.Resource) error
	
	// Read operations
	GetByID(ctx context.Context, id uuid.UUID) (*entities.Resource, error)
	GetByProjectID(ctx context.Context, projectID uuid.UUID, pagination Pagination) ([]*entities.Resource, int64, error)
	GetByName(ctx context.Context, projectID uuid.UUID, name string) (*entities.Resource, error)
	
	// Update operations
	Update(ctx context.Context, resource *entities.Resource) error
	UpdatePartial(ctx context.Context, id uuid.UUID, updates map[string]interface{}) error
	
	// Delete operations
	Delete(ctx context.Context, id uuid.UUID) error
	SoftDelete(ctx context.Context, id uuid.UUID) error
	
	// Specialized queries
	NameExistsInProject(ctx context.Context, projectID uuid.UUID, name string) (bool, error)
	NameExistsInProjectExcluding(ctx context.Context, projectID uuid.UUID, name string, excludeID uuid.UUID) (bool, error)
	GetResourceStats(ctx context.Context, resourceID uuid.UUID) (*ResourceStats, error)
}

// ProjectFilters defines filtering options for project queries
type ProjectFilters struct {
	Name           *string
	OrganizationID *uuid.UUID
	CreatedBy      *uuid.UUID
	IsPublic       *bool
	DefaultLocale  *string
	CreatedAfter   *string
	CreatedBefore  *string
}

// ProjectStats represents statistics for a project
type ProjectStats struct {
	TotalKeys         int64
	TotalTranslations int64
	CompletedKeys     int64
	PendingKeys       int64
	ReviewedKeys      int64
	LocaleCount       int64
	ResourceCount     int64
}

// ResourceStats represents statistics for a resource
type ResourceStats struct {
	TotalKeys         int64
	TotalTranslations int64
	CompletedKeys     int64
	PendingKeys       int64
}
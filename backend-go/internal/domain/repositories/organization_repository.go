package repositories

import (
	"context"

	"adc-multi-languages/internal/domain/entities"
	"github.com/google/uuid"
)

// OrganizationRepository defines the interface for organization data access
type OrganizationRepository interface {
	// Create operations
	Create(ctx context.Context, org *entities.Organization) error
	
	// Read operations
	GetByID(ctx context.Context, id uuid.UUID) (*entities.Organization, error)
	GetBySlug(ctx context.Context, slug string) (*entities.Organization, error)
	List(ctx context.Context, filters OrganizationFilters, pagination Pagination) ([]*entities.Organization, int64, error)
	GetByOwnerID(ctx context.Context, ownerID uuid.UUID, pagination Pagination) ([]*entities.Organization, int64, error)
	GetByUserMembership(ctx context.Context, userID uuid.UUID, pagination Pagination) ([]*entities.Organization, int64, error)
	
	// Update operations
	Update(ctx context.Context, org *entities.Organization) error
	UpdatePartial(ctx context.Context, id uuid.UUID, updates map[string]interface{}) error
	
	// Delete operations
	Delete(ctx context.Context, id uuid.UUID) error
	SoftDelete(ctx context.Context, id uuid.UUID) error
	
	// Specialized queries
	SlugExists(ctx context.Context, slug string) (bool, error)
	SlugExistsExcluding(ctx context.Context, slug string, excludeID uuid.UUID) (bool, error)
	GetWithOwner(ctx context.Context, id uuid.UUID) (*entities.Organization, *entities.User, error)
	GetSubscriptionInfo(ctx context.Context, id uuid.UUID) (*OrganizationSubscriptionInfo, error)
	UpdateAICredits(ctx context.Context, id uuid.UUID, credits int) error
	ConsumeAICredits(ctx context.Context, id uuid.UUID, amount int) error
}

// OrganizationFilters defines filtering options for organization queries
type OrganizationFilters struct {
	Name               *string
	OwnerID            *uuid.UUID
	SubscriptionTier   *string
	SubscriptionStatus *string
	CreatedAfter       *string
	CreatedBefore      *string
}

// OrganizationSubscriptionInfo represents subscription information for an organization
type OrganizationSubscriptionInfo struct {
	SubscriptionTier          string
	SubscriptionStatus        *string
	AICreditsMonthlyAllowance *int
	AICreditsRemaining        *int
	BillingPeriodStart        *string
	BillingPeriodEnd          *string
}
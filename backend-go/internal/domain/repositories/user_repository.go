package repositories

import (
	"context"

	"adc-multi-languages/internal/domain/entities"
	"adc-multi-languages/internal/domain/valueobjects"
	"github.com/google/uuid"
)

// UserRepository defines the interface for user data access
type UserRepository interface {
	// Create saves a new user
	Create(ctx context.Context, user *entities.User) error
	
	// GetByID retrieves a user by ID
	GetByID(ctx context.Context, id uuid.UUID) (*entities.User, error)
	
	// GetByEmail retrieves a user by email
	GetByEmail(ctx context.Context, email valueobjects.Email) (*entities.User, error)
	
	// GetByUsername retrieves a user by username
	GetByUsername(ctx context.Context, username string) (*entities.User, error)
	
	// Update saves changes to an existing user
	Update(ctx context.Context, user *entities.User) error
	
	// Delete removes a user (soft delete)
	Delete(ctx context.Context, id uuid.UUID) error
	
	// ExistsByEmail checks if a user with the given email exists
	ExistsByEmail(ctx context.Context, email valueobjects.Email) (bool, error)
	
	// ExistsByUsername checks if a user with the given username exists
	ExistsByUsername(ctx context.Context, username string) (bool, error)
	
	// List retrieves users with pagination and filters
	List(ctx context.Context, filter UserFilter, pagination Pagination) ([]*entities.User, *PaginationResult, error)
}

// UserFilter defines filters for user queries
type UserFilter struct {
	IsActive      *bool
	EmailVerified *bool
	Search        string // Search in email, username, first name, last name
}

// Pagination defines pagination parameters
type Pagination struct {
	Page     int
	PageSize int
	OrderBy  string
	Order    string // ASC or DESC
}

// PaginationResult contains pagination metadata
type PaginationResult struct {
	Total       int64
	TotalPages  int
	CurrentPage int
	PageSize    int
	HasNext     bool
	HasPrev     bool
}
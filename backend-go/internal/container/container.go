package container

import (
	"adc-multi-languages/internal/application/services"
	"adc-multi-languages/internal/domain/repositories"
	dbRepos "adc-multi-languages/internal/infrastructure/database/repositories"
	"adc-multi-languages/internal/presentation/handlers"
	"gorm.io/gorm"
)

// Container holds all application dependencies
type Container struct {
	// Infrastructure
	DB *gorm.DB

	// Repositories
	RepositoryManager repositories.RepositoryManager

	// Application Services
	UserService services.UserService
	// OrganizationService services.OrganizationService
	// ProjectService services.ProjectService
	// TranslationService services.TranslationService

	// Presentation Layer
	UserHandler *handlers.UserHandler
	// OrganizationHandler *handlers.OrganizationHandler
	// ProjectHandler *handlers.ProjectHandler
	// TranslationHandler *handlers.TranslationHandler
}

// NewContainer creates and wires up all dependencies
func NewContainer(db *gorm.DB) *Container {
	container := &Container{
		DB: db,
	}

	// Initialize repository manager
	container.RepositoryManager = dbRepos.NewRepositoryManager(db)

	// Initialize application services
	container.UserService = services.NewUserService(container.RepositoryManager)
	// container.OrganizationService = services.NewOrganizationService(container.RepositoryManager)
	// container.ProjectService = services.NewProjectService(container.RepositoryManager)
	// container.TranslationService = services.NewTranslationService(container.RepositoryManager)

	// Initialize handlers
	container.UserHandler = handlers.NewUserHandler(container.UserService)
	// container.OrganizationHandler = handlers.NewOrganizationHandler(container.OrganizationService)
	// container.ProjectHandler = handlers.NewProjectHandler(container.ProjectService)
	// container.TranslationHandler = handlers.NewTranslationHandler(container.TranslationService)

	return container
}

// Close cleans up resources
func (c *Container) Close() error {
	if c.DB != nil {
		sqlDB, err := c.DB.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}
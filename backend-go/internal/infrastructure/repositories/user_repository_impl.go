package repositories

import (
	"context"
	"fmt"

	"adc-multi-languages/internal/domain/entities"
	"adc-multi-languages/internal/domain/repositories"
	"adc-multi-languages/internal/domain/valueobjects"
	"adc-multi-languages/internal/infrastructure/database"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// userRepositoryImpl implements the UserRepository interface
type userRepositoryImpl struct {
	db *gorm.DB
}

// NewUserRepository creates a new user repository
func NewUserRepository(db *gorm.DB) repositories.UserRepository {
	return &userRepositoryImpl{
		db: db,
	}
}

// Create saves a new user
func (r *userRepositoryImpl) Create(ctx context.Context, user *entities.User) error {
	model := r.entityToModel(user)
	return r.db.WithContext(ctx).Create(model).Error
}

// GetByID retrieves a user by ID
func (r *userRepositoryImpl) GetByID(ctx context.Context, id uuid.UUID) (*entities.User, error) {
	var model database.UserModel
	err := r.db.WithContext(ctx).First(&model, "id = ?", id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return r.modelToEntity(&model)
}

// GetByEmail retrieves a user by email
func (r *userRepositoryImpl) GetByEmail(ctx context.Context, email valueobjects.Email) (*entities.User, error) {
	var model database.UserModel
	err := r.db.WithContext(ctx).First(&model, "email = ?", email.Value()).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return r.modelToEntity(&model)
}

// GetByUsername retrieves a user by username
func (r *userRepositoryImpl) GetByUsername(ctx context.Context, username string) (*entities.User, error) {
	var model database.UserModel
	err := r.db.WithContext(ctx).First(&model, "username = ?", username).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return r.modelToEntity(&model)
}

// Update saves changes to an existing user
func (r *userRepositoryImpl) Update(ctx context.Context, user *entities.User) error {
	model := r.entityToModel(user)
	return r.db.WithContext(ctx).Save(model).Error
}

// Delete removes a user (soft delete)
func (r *userRepositoryImpl) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&database.UserModel{}, id).Error
}

// ExistsByEmail checks if a user with the given email exists
func (r *userRepositoryImpl) ExistsByEmail(ctx context.Context, email valueobjects.Email) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&database.UserModel{}).Where("email = ?", email.Value()).Count(&count).Error
	return count > 0, err
}

// ExistsByUsername checks if a user with the given username exists
func (r *userRepositoryImpl) ExistsByUsername(ctx context.Context, username string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&database.UserModel{}).Where("username = ?", username).Count(&count).Error
	return count > 0, err
}

// List retrieves users with pagination and filters
func (r *userRepositoryImpl) List(ctx context.Context, filter repositories.UserFilter, pagination repositories.Pagination) ([]*entities.User, *repositories.PaginationResult, error) {
	query := r.db.WithContext(ctx).Model(&database.UserModel{})
	
	// Apply filters
	if filter.IsActive != nil {
		query = query.Where("is_active = ?", *filter.IsActive)
	}
	if filter.EmailVerified != nil {
		query = query.Where("email_verified = ?", *filter.EmailVerified)
	}
	if filter.Search != "" {
		searchTerm := "%" + filter.Search + "%"
		query = query.Where("email ILIKE ? OR username ILIKE ? OR first_name ILIKE ? OR last_name ILIKE ?", 
			searchTerm, searchTerm, searchTerm, searchTerm)
	}
	
	// Count total records
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, nil, err
	}
	
	// Apply pagination
	offset := (pagination.Page - 1) * pagination.PageSize
	orderBy := "created_at"
	if pagination.OrderBy != "" {
		orderBy = pagination.OrderBy
	}
	order := "DESC"
	if pagination.Order != "" {
		order = pagination.Order
	}
	
	query = query.Offset(offset).Limit(pagination.PageSize).Order(fmt.Sprintf("%s %s", orderBy, order))
	
	var models []database.UserModel
	if err := query.Find(&models).Error; err != nil {
		return nil, nil, err
	}
	
	// Convert models to entities
	users := make([]*entities.User, len(models))
	for i, model := range models {
		user, err := r.modelToEntity(&model)
		if err != nil {
			return nil, nil, err
		}
		users[i] = user
	}
	
	// Calculate pagination metadata
	totalPages := int((total + int64(pagination.PageSize) - 1) / int64(pagination.PageSize))
	result := &repositories.PaginationResult{
		Total:       total,
		TotalPages:  totalPages,
		CurrentPage: pagination.Page,
		PageSize:    pagination.PageSize,
		HasNext:     pagination.Page < totalPages,
		HasPrev:     pagination.Page > 1,
	}
	
	return users, result, nil
}

// entityToModel converts a User entity to a UserModel
func (r *userRepositoryImpl) entityToModel(user *entities.User) *database.UserModel {
	var languageStr *string
	if user.Language() != nil {
		lang := user.Language().Value()
		languageStr = &lang
	}
	
	return &database.UserModel{
		BaseModel: database.BaseModel{
			ID:        user.ID(),
			CreatedAt: user.CreatedAt(),
			UpdatedAt: user.UpdatedAt(),
		},
		Email:             user.Email().Value(),
		Username:          user.Username(),
		PasswordHash:      user.PasswordHash(),
		FirstName:         user.FirstName(),
		LastName:          user.LastName(),
		EmailVerified:     user.EmailVerified(),
		EmailVerifiedAt:   user.EmailVerifiedAt(),
		LastLoginAt:       user.LastLoginAt(),
		IsActive:          user.IsActive(),
		ProfilePictureURL: user.ProfilePictureURL(),
		Timezone:          user.Timezone(),
		Language:          languageStr,
	}
}

// modelToEntity converts a UserModel to a User entity
func (r *userRepositoryImpl) modelToEntity(model *database.UserModel) (*entities.User, error) {
	email, err := valueobjects.NewEmail(model.Email)
	if err != nil {
		return nil, fmt.Errorf("invalid email in database: %w", err)
	}
	
	var language *valueobjects.LocaleCode
	if model.Language != nil {
		lang, err := valueobjects.NewLocaleCode(*model.Language)
		if err != nil {
			return nil, fmt.Errorf("invalid language in database: %w", err)
		}
		language = lang
	}
	
	return entities.ReconstructUser(
		model.ID,
		*email,
		model.Username,
		model.PasswordHash,
		model.FirstName,
		model.LastName,
		model.EmailVerified,
		model.EmailVerifiedAt,
		model.LastLoginAt,
		model.IsActive,
		model.ProfilePictureURL,
		model.Timezone,
		language,
		model.CreatedAt,
		model.UpdatedAt,
	), nil
}
package repositories

import (
	"context"
	"fmt"

	"adc-multi-languages/internal/domain/entities"
	"adc-multi-languages/internal/domain/repositories"
	"adc-multi-languages/internal/infrastructure/database/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// userRepositoryImpl implements the UserRepository interface
type userRepositoryImpl struct {
	db *gorm.DB
}

// NewUserRepository creates a new user repository implementation
func NewUserRepository(db *gorm.DB) repositories.UserRepository {
	return &userRepositoryImpl{db: db}
}

// Create creates a new user
func (r *userRepositoryImpl) Create(ctx context.Context, user *entities.User) error {
	model := models.NewUserFromEntity(user)
	
	if err := r.db.WithContext(ctx).Create(model).Error; err != nil {
		return fmt.Errorf("failed to create user: %w", err)
	}
	
	// Update the entity with the created model's data
	*user = *model.ToEntity()
	return nil
}

// GetByID retrieves a user by ID
func (r *userRepositoryImpl) GetByID(ctx context.Context, id uuid.UUID) (*entities.User, error) {
	var model models.User
	
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get user by ID: %w", err)
	}
	
	return model.ToEntity(), nil
}

// GetByEmail retrieves a user by email
func (r *userRepositoryImpl) GetByEmail(ctx context.Context, email string) (*entities.User, error) {
	var model models.User
	
	if err := r.db.WithContext(ctx).Where("email = ?", email).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get user by email: %w", err)
	}
	
	return model.ToEntity(), nil
}

// GetByUsername retrieves a user by username
func (r *userRepositoryImpl) GetByUsername(ctx context.Context, username string) (*entities.User, error) {
	var model models.User
	
	if err := r.db.WithContext(ctx).Where("username = ?", username).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get user by username: %w", err)
	}
	
	return model.ToEntity(), nil
}

// List retrieves users with filtering and pagination
func (r *userRepositoryImpl) List(ctx context.Context, filters repositories.UserFilters, pagination repositories.Pagination) ([]*entities.User, int64, error) {
	query := r.db.WithContext(ctx).Model(&models.User{})
	
	// Apply filters
	query = r.applyUserFilters(query, filters)
	
	// Count total records
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count users: %w", err)
	}
	
	// Apply pagination
	var modelUsers []models.User
	if err := query.Offset(pagination.Offset).Limit(pagination.Limit).Find(&modelUsers).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list users: %w", err)
	}
	
	// Convert to entities
	users := make([]*entities.User, len(modelUsers))
	for i, model := range modelUsers {
		users[i] = model.ToEntity()
	}
	
	return users, total, nil
}

// Update updates a user
func (r *userRepositoryImpl) Update(ctx context.Context, user *entities.User) error {
	model := models.NewUserFromEntity(user)
	
	if err := r.db.WithContext(ctx).Save(model).Error; err != nil {
		return fmt.Errorf("failed to update user: %w", err)
	}
	
	// Update the entity with the saved model's data
	*user = *model.ToEntity()
	return nil
}

// UpdatePartial updates specific fields of a user
func (r *userRepositoryImpl) UpdatePartial(ctx context.Context, id uuid.UUID, updates map[string]interface{}) error {
	if err := r.db.WithContext(ctx).Model(&models.User{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to partially update user: %w", err)
	}
	
	return nil
}

// Delete permanently deletes a user
func (r *userRepositoryImpl) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Unscoped().Delete(&models.User{}, id).Error; err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}
	
	return nil
}

// SoftDelete soft deletes a user
func (r *userRepositoryImpl) SoftDelete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&models.User{}, id).Error; err != nil {
		return fmt.Errorf("failed to soft delete user: %w", err)
	}
	
	return nil
}

// EmailExists checks if an email already exists
func (r *userRepositoryImpl) EmailExists(ctx context.Context, email string) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.User{}).Where("email = ?", email).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check email existence: %w", err)
	}
	
	return count > 0, nil
}

// UsernameExists checks if a username already exists
func (r *userRepositoryImpl) UsernameExists(ctx context.Context, username string) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.User{}).Where("username = ?", username).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check username existence: %w", err)
	}
	
	return count > 0, nil
}

// GetActiveUsers retrieves active users
func (r *userRepositoryImpl) GetActiveUsers(ctx context.Context, pagination repositories.Pagination) ([]*entities.User, int64, error) {
	query := r.db.WithContext(ctx).Model(&models.User{}).Where("is_active = ?", true)
	
	// Count total records
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count active users: %w", err)
	}
	
	// Apply pagination
	var modelUsers []models.User
	if err := query.Offset(pagination.Offset).Limit(pagination.Limit).Find(&modelUsers).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get active users: %w", err)
	}
	
	// Convert to entities
	users := make([]*entities.User, len(modelUsers))
	for i, model := range modelUsers {
		users[i] = model.ToEntity()
	}
	
	return users, total, nil
}

// SearchByEmailOrUsername searches users by email or username
func (r *userRepositoryImpl) SearchByEmailOrUsername(ctx context.Context, query string, pagination repositories.Pagination) ([]*entities.User, int64, error) {
	dbQuery := r.db.WithContext(ctx).Model(&models.User{}).
		Where("email ILIKE ? OR username ILIKE ?", "%"+query+"%", "%"+query+"%")
	
	// Count total records
	var total int64
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count search results: %w", err)
	}
	
	// Apply pagination
	var modelUsers []models.User
	if err := dbQuery.Offset(pagination.Offset).Limit(pagination.Limit).Find(&modelUsers).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to search users: %w", err)
	}
	
	// Convert to entities
	users := make([]*entities.User, len(modelUsers))
	for i, model := range modelUsers {
		users[i] = model.ToEntity()
	}
	
	return users, total, nil
}

// applyUserFilters applies filters to the query
func (r *userRepositoryImpl) applyUserFilters(query *gorm.DB, filters repositories.UserFilters) *gorm.DB {
	if filters.Email != nil {
		query = query.Where("email = ?", *filters.Email)
	}
	
	if filters.Username != nil {
		query = query.Where("username = ?", *filters.Username)
	}
	
	if filters.IsActive != nil {
		query = query.Where("is_active = ?", *filters.IsActive)
	}
	
	if filters.EmailVerified != nil {
		query = query.Where("email_verified = ?", *filters.EmailVerified)
	}
	
	if filters.CreatedAfter != nil {
		query = query.Where("created_at >= ?", *filters.CreatedAfter)
	}
	
	if filters.CreatedBefore != nil {
		query = query.Where("created_at <= ?", *filters.CreatedBefore)
	}
	
	return query
}
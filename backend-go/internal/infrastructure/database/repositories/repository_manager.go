package repositories

import (
	"fmt"

	"adc-multi-languages/internal/domain/repositories"
	"gorm.io/gorm"
)

// repositoryManager implements the RepositoryManager interface
type repositoryManager struct {
	db                  *gorm.DB
	unitOfWork          repositories.UnitOfWork
	userRepo            repositories.UserRepository
	organizationRepo    repositories.OrganizationRepository
	projectRepo         repositories.ProjectRepository
	projectLocaleRepo   repositories.ProjectLocaleRepository
	resourceRepo        repositories.ResourceRepository
	translationKeyRepo  repositories.TranslationKeyRepository
	translationRepo     repositories.TranslationRepository
	translationHistRepo repositories.TranslationHistoryRepository
	localeRepo          repositories.LocaleRepository
}

// NewRepositoryManager creates a new repository manager
func NewRepositoryManager(db *gorm.DB) repositories.RepositoryManager {
	return &repositoryManager{
		db:         db,
		unitOfWork: NewUnitOfWork(db),
	}
}

// Users returns the user repository
func (rm *repositoryManager) Users() repositories.UserRepository {
	if rm.userRepo == nil {
		rm.userRepo = NewUserRepository(rm.db)
	}
	return rm.userRepo
}

// Organizations returns the organization repository
func (rm *repositoryManager) Organizations() repositories.OrganizationRepository {
	if rm.organizationRepo == nil {
		rm.organizationRepo = NewOrganizationRepository(rm.db)
	}
	return rm.organizationRepo
}

// Projects returns the project repository
func (rm *repositoryManager) Projects() repositories.ProjectRepository {
	if rm.projectRepo == nil {
		rm.projectRepo = NewProjectRepository(rm.db)
	}
	return rm.projectRepo
}

// ProjectLocales returns the project locale repository
func (rm *repositoryManager) ProjectLocales() repositories.ProjectLocaleRepository {
	if rm.projectLocaleRepo == nil {
		rm.projectLocaleRepo = NewProjectLocaleRepository(rm.db)
	}
	return rm.projectLocaleRepo
}

// Resources returns the resource repository
func (rm *repositoryManager) Resources() repositories.ResourceRepository {
	if rm.resourceRepo == nil {
		rm.resourceRepo = NewResourceRepository(rm.db)
	}
	return rm.resourceRepo
}

// TranslationKeys returns the translation key repository
func (rm *repositoryManager) TranslationKeys() repositories.TranslationKeyRepository {
	if rm.translationKeyRepo == nil {
		rm.translationKeyRepo = NewTranslationKeyRepository(rm.db)
	}
	return rm.translationKeyRepo
}

// Translations returns the translation repository
func (rm *repositoryManager) Translations() repositories.TranslationRepository {
	if rm.translationRepo == nil {
		rm.translationRepo = NewTranslationRepository(rm.db)
	}
	return rm.translationRepo
}

// TranslationHistory returns the translation history repository
func (rm *repositoryManager) TranslationHistory() repositories.TranslationHistoryRepository {
	if rm.translationHistRepo == nil {
		rm.translationHistRepo = NewTranslationHistoryRepository(rm.db)
	}
	return rm.translationHistRepo
}

// Locales returns the locale repository
func (rm *repositoryManager) Locales() repositories.LocaleRepository {
	if rm.localeRepo == nil {
		rm.localeRepo = NewLocaleRepository(rm.db)
	}
	return rm.localeRepo
}

// UnitOfWork returns the unit of work
func (rm *repositoryManager) UnitOfWork() repositories.UnitOfWork {
	return rm.unitOfWork
}

// WithTx executes a function within a transaction
func (rm *repositoryManager) WithTx(fn func(repositories.RepositoryManager) error) error {
	return rm.db.Transaction(func(tx *gorm.DB) error {
		txRepoManager := NewRepositoryManager(tx)
		return fn(txRepoManager)
	})
}

// unitOfWorkImpl implements the UnitOfWork interface
type unitOfWorkImpl struct {
	db *gorm.DB
	tx *gorm.DB
}

// NewUnitOfWork creates a new unit of work
func NewUnitOfWork(db *gorm.DB) repositories.UnitOfWork {
	return &unitOfWorkImpl{db: db}
}

// Begin starts a new transaction
func (uow *unitOfWorkImpl) Begin() error {
	if uow.tx != nil {
		return fmt.Errorf("transaction already started")
	}
	
	uow.tx = uow.db.Begin()
	if uow.tx.Error != nil {
		return fmt.Errorf("failed to start transaction: %w", uow.tx.Error)
	}
	
	return nil
}

// Commit commits the current transaction
func (uow *unitOfWorkImpl) Commit() error {
	if uow.tx == nil {
		return fmt.Errorf("no active transaction")
	}
	
	if err := uow.tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}
	
	uow.tx = nil
	return nil
}

// Rollback rolls back the current transaction
func (uow *unitOfWorkImpl) Rollback() error {
	if uow.tx == nil {
		return fmt.Errorf("no active transaction")
	}
	
	if err := uow.tx.Rollback().Error; err != nil {
		return fmt.Errorf("failed to rollback transaction: %w", err)
	}
	
	uow.tx = nil
	return nil
}

// InTransaction returns true if there's an active transaction
func (uow *unitOfWorkImpl) InTransaction() bool {
	return uow.tx != nil
}

// Placeholder implementations for other repositories
// These would be implemented similar to the user repository

func NewOrganizationRepository(db *gorm.DB) repositories.OrganizationRepository {
	// TODO: Implement organization repository
	return nil
}

func NewProjectRepository(db *gorm.DB) repositories.ProjectRepository {
	// TODO: Implement project repository
	return nil
}

func NewProjectLocaleRepository(db *gorm.DB) repositories.ProjectLocaleRepository {
	// TODO: Implement project locale repository
	return nil
}

func NewResourceRepository(db *gorm.DB) repositories.ResourceRepository {
	// TODO: Implement resource repository
	return nil
}

func NewTranslationKeyRepository(db *gorm.DB) repositories.TranslationKeyRepository {
	// TODO: Implement translation key repository
	return nil
}

func NewTranslationRepository(db *gorm.DB) repositories.TranslationRepository {
	// TODO: Implement translation repository
	return nil
}

func NewTranslationHistoryRepository(db *gorm.DB) repositories.TranslationHistoryRepository {
	// TODO: Implement translation history repository
	return nil
}

func NewLocaleRepository(db *gorm.DB) repositories.LocaleRepository {
	// TODO: Implement locale repository
	return nil
}
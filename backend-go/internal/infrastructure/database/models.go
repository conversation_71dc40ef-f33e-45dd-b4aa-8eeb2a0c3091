package database

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// BaseModel contains common fields for all database models
type BaseModel struct {
	ID        uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	CreatedAt time.Time      `gorm:"autoCreateTime"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime"`
	DeletedAt gorm.DeletedAt `gorm:"index"`
}

// Before<PERSON><PERSON> sets the ID before creating a record
func (base *BaseModel) BeforeCreate(tx *gorm.DB) error {
	if base.ID == uuid.Nil {
		base.ID = uuid.New()
	}
	return nil
}

// UserModel represents a user in the database
type UserModel struct {
	BaseModel
	Email             string     `gorm:"uniqueIndex;not null"`
	Username          *string    `gorm:"uniqueIndex"`
	PasswordHash      string     `gorm:"not null"`
	FirstName         *string    
	LastName          *string    
	EmailVerified     bool       `gorm:"default:false"`
	EmailVerifiedAt   *time.Time 
	LastLoginAt       *time.Time 
	IsActive          bool       `gorm:"default:true"`
	ProfilePictureURL *string    
	Timezone          *string    
	Language          *string    `gorm:"default:'en'"`
}

// TableName overrides the table name for UserModel
func (UserModel) TableName() string {
	return "users"
}
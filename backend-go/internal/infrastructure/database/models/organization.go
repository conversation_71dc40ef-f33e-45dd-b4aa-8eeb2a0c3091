package models

import (
	"time"

	"adc-multi-languages/internal/domain/entities"
	"github.com/google/uuid"
)

// Organization represents the database model for organizations
type Organization struct {
	BaseModel
	Name                      string     `json:"name" gorm:"not null"`
	Slug                      string     `json:"slug" gorm:"uniqueIndex;not null"`
	Description               *string    `json:"description"`
	Website                   *string    `json:"website"`
	LogoURL                   *string    `json:"logo_url"`
	OwnerID                   uuid.UUID  `json:"owner_id" gorm:"type:uuid;not null"`
	Owner                     User       `json:"owner" gorm:"foreignKey:OwnerID"`
	SubscriptionTier          string     `json:"subscription_tier" gorm:"default:'free'"`
	SubscriptionStatus        *string    `json:"subscription_status"`
	SubscriptionTierID        *uuid.UUID `json:"subscription_tier_id" gorm:"type:uuid"`
	SubscriptionAutoRenew     *bool      `json:"subscription_auto_renew" gorm:"default:true"`
	BillingPeriodStart        *time.Time `json:"billing_period_start"`
	BillingPeriodEnd          *time.Time `json:"billing_period_end"`
	StripeCustomerID          *string    `json:"stripe_customer_id"`
	StripeSubscriptionID      *string    `json:"stripe_subscription_id"`
	AICreditsMonthlyAllowance *int       `json:"ai_credits_monthly_allowance" gorm:"default:1000"`
	AICreditsRemaining        *int       `json:"ai_credits_remaining" gorm:"default:1000"`
	AICreditsResetDate        *time.Time `json:"ai_credits_reset_date"`
}

// ToEntity converts the database model to domain entity
func (o *Organization) ToEntity() *entities.Organization {
	return &entities.Organization{
		BaseEntity: entities.BaseEntity{
			ID:        o.ID,
			CreatedAt: o.CreatedAt,
			UpdatedAt: o.UpdatedAt,
			DeletedAt: o.getDeletedAt(),
		},
		Name:                      o.Name,
		Slug:                      o.Slug,
		Description:               o.Description,
		Website:                   o.Website,
		LogoURL:                   o.LogoURL,
		OwnerID:                   o.OwnerID,
		SubscriptionTier:          o.SubscriptionTier,
		SubscriptionStatus:        o.SubscriptionStatus,
		SubscriptionTierID:        o.SubscriptionTierID,
		SubscriptionAutoRenew:     o.SubscriptionAutoRenew,
		BillingPeriodStart:        o.BillingPeriodStart,
		BillingPeriodEnd:          o.BillingPeriodEnd,
		StripeCustomerID:          o.StripeCustomerID,
		StripeSubscriptionID:      o.StripeSubscriptionID,
		AICreditsMonthlyAllowance: o.AICreditsMonthlyAllowance,
		AICreditsRemaining:        o.AICreditsRemaining,
		AICreditsResetDate:        o.AICreditsResetDate,
	}
}

// FromEntity converts a domain entity to database model
func (o *Organization) FromEntity(entity *entities.Organization) {
	o.ID = entity.ID
	o.CreatedAt = entity.CreatedAt
	o.UpdatedAt = entity.UpdatedAt
	o.setDeletedAt(entity.DeletedAt)
	o.Name = entity.Name
	o.Slug = entity.Slug
	o.Description = entity.Description
	o.Website = entity.Website
	o.LogoURL = entity.LogoURL
	o.OwnerID = entity.OwnerID
	o.SubscriptionTier = entity.SubscriptionTier
	o.SubscriptionStatus = entity.SubscriptionStatus
	o.SubscriptionTierID = entity.SubscriptionTierID
	o.SubscriptionAutoRenew = entity.SubscriptionAutoRenew
	o.BillingPeriodStart = entity.BillingPeriodStart
	o.BillingPeriodEnd = entity.BillingPeriodEnd
	o.StripeCustomerID = entity.StripeCustomerID
	o.StripeSubscriptionID = entity.StripeSubscriptionID
	o.AICreditsMonthlyAllowance = entity.AICreditsMonthlyAllowance
	o.AICreditsRemaining = entity.AICreditsRemaining
	o.AICreditsResetDate = entity.AICreditsResetDate
}

// NewOrganizationFromEntity creates a new database model from domain entity
func NewOrganizationFromEntity(entity *entities.Organization) *Organization {
	org := &Organization{}
	org.FromEntity(entity)
	return org
}

// OrganizationMembership represents a user's membership in an organization
type OrganizationMembership struct {
	BaseModel
	OrganizationID uuid.UUID    `json:"organization_id" gorm:"type:uuid;not null"`
	Organization   Organization `json:"organization" gorm:"foreignKey:OrganizationID"`
	UserID         uuid.UUID    `json:"user_id" gorm:"type:uuid;not null"`
	User           User         `json:"user" gorm:"foreignKey:UserID"`
	Role           string       `json:"role" gorm:"not null;default:'member'"` // owner, admin, member
	IsActive       bool         `json:"is_active" gorm:"default:true"`
	InvitedBy      *uuid.UUID   `json:"invited_by" gorm:"type:uuid"`
	Inviter        *User        `json:"inviter,omitempty" gorm:"foreignKey:InvitedBy"`
	JoinedAt       *time.Time   `json:"joined_at"`
}

// TableName overrides the table name used by OrganizationMembership
func (OrganizationMembership) TableName() string {
	return "organization_memberships"
}

// Helper methods for handling DeletedAt
func (o *Organization) getDeletedAt() *time.Time {
	if o.DeletedAt.Valid {
		return &o.DeletedAt.Time
	}
	return nil
}

func (o *Organization) setDeletedAt(deletedAt *time.Time) {
	if deletedAt != nil {
		o.DeletedAt.Time = *deletedAt
		o.DeletedAt.Valid = true
	} else {
		o.DeletedAt.Valid = false
	}
}
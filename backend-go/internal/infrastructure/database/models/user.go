package models

import (
	"time"

	"adc-multi-languages/internal/domain/entities"
)

// User represents the database model for users
type User struct {
	BaseModel
	Email             string     `json:"email" gorm:"uniqueIndex;not null"`
	Username          *string    `json:"username" gorm:"uniqueIndex"`
	PasswordHash      string     `json:"-" gorm:"not null"`
	FirstName         *string    `json:"first_name"`
	LastName          *string    `json:"last_name"`
	EmailVerified     bool       `json:"email_verified" gorm:"default:false"`
	EmailVerifiedAt   *time.Time `json:"email_verified_at"`
	LastLoginAt       *time.Time `json:"last_login_at"`
	IsActive          bool       `json:"is_active" gorm:"default:true"`
	ProfilePictureURL *string    `json:"profile_picture_url"`
	Timezone          *string    `json:"timezone"`
	Language          *string    `json:"language" gorm:"default:'en'"`
}

// ToEntity converts the database model to domain entity
func (u *User) ToEntity() *entities.User {
	return &entities.User{
		BaseEntity: entities.BaseEntity{
			ID:        u.ID,
			CreatedAt: u.CreatedAt,
			UpdatedAt: u.UpdatedAt,
			DeletedAt: u.getDeletedAt(),
		},
		Email:             u.Email,
		Username:          u.Username,
		PasswordHash:      u.PasswordHash,
		FirstName:         u.FirstName,
		LastName:          u.LastName,
		EmailVerified:     u.EmailVerified,
		EmailVerifiedAt:   u.EmailVerifiedAt,
		LastLoginAt:       u.LastLoginAt,
		IsActive:          u.IsActive,
		ProfilePictureURL: u.ProfilePictureURL,
		Timezone:          u.Timezone,
		Language:          u.Language,
	}
}

// FromEntity converts a domain entity to database model
func (u *User) FromEntity(entity *entities.User) {
	u.ID = entity.ID
	u.CreatedAt = entity.CreatedAt
	u.UpdatedAt = entity.UpdatedAt
	u.setDeletedAt(entity.DeletedAt)
	u.Email = entity.Email
	u.Username = entity.Username
	u.PasswordHash = entity.PasswordHash
	u.FirstName = entity.FirstName
	u.LastName = entity.LastName
	u.EmailVerified = entity.EmailVerified
	u.EmailVerifiedAt = entity.EmailVerifiedAt
	u.LastLoginAt = entity.LastLoginAt
	u.IsActive = entity.IsActive
	u.ProfilePictureURL = entity.ProfilePictureURL
	u.Timezone = entity.Timezone
	u.Language = entity.Language
}

// NewUserFromEntity creates a new database model from domain entity
func NewUserFromEntity(entity *entities.User) *User {
	user := &User{}
	user.FromEntity(entity)
	return user
}

// Helper methods for handling DeletedAt
func (u *User) getDeletedAt() *time.Time {
	if u.DeletedAt.Valid {
		return &u.DeletedAt.Time
	}
	return nil
}

func (u *User) setDeletedAt(deletedAt *time.Time) {
	if deletedAt != nil {
		u.DeletedAt.Time = *deletedAt
		u.DeletedAt.Valid = true
	} else {
		u.DeletedAt.Valid = false
	}
}
{"permissions": {"allow": ["Bash(git checkout:*)", "Bash(git add:*)", "Bash(git push:*)", "Bash(git remote get-url:*)", "Bash(git remote set-url:*)", "Bash(git config:*)", "Bash(git fetch:*)", "Bash(git commit:*)", "Bash(gh repo create:*)", "Bash(brew install:*)", "Bash(gh auth:*)", "Bash(git merge:*)", "Bash(bun add:*)", "<PERSON><PERSON>(bun dev)", "Bash(lsof:*)", "Bash(kill:*)", "Bash(bun lint:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(make:*)", "<PERSON><PERSON>(pkill:*)", "Bash(rg:*)", "Bash(find:*)", "<PERSON><PERSON>(go run:*)", "<PERSON><PERSON>(python3:*)", "Bash(rm:*)", "Bash(bun run:*)", "Bash(npx tsc:*)", "mcp__ide__getDiagnostics", "Bash(ls:*)", "Bash(grep:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "<PERSON><PERSON>(timeout:*)", "Bash(tree:*)", "<PERSON><PERSON>(mv:*)", "Bash(go build:*)", "Bash(git pull:*)", "Bash(gh workflow:*)", "<PERSON><PERSON>(gh run list:*)", "Bash(gh run view:*)", "<PERSON><PERSON>(gcloud:*)", "Bash(gh secret set:*)", "<PERSON><PERSON>(gh secret:*)", "<PERSON><PERSON>(openssl rand:*)", "<PERSON><PERSON>(go mod tidy:*)", "Ba<PERSON>(go vet:*)", "Bash(go get:*)", "Bash(go mod:*)"], "deny": []}}
{"name": "adc-muti-languages-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3300", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/core": "^0.39.1", "@google/genai": "^0.14.1", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-form": "^0.1.6", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.6", "@reduxjs/toolkit": "^2.8.1", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@supabase/supabase-js": "^2.49.4", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cobe": "^0.6.4", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "framer-motion": "^12.17.0", "geist": "^1.4.2", "lucide-react": "^0.510.0", "mime": "^4.0.7", "next": "14.0.1", "next-auth": "4.24.7", "next-redux-wrapper": "^8.1.0", "node-fetch": "^2.7.0", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.56.3", "react-redux": "^9.2.0", "recharts": "^2.15.3", "stripe": "^18.1.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.17"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/canvas-confetti": "^1.9.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.1", "postcss": "^8", "tw-animate-css": "^1.2.9", "typescript": "^5"}}
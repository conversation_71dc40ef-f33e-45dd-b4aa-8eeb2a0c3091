import { apiSlice } from '../apiSlice';

// Types for organization requests and responses
export interface Organization {
  id: string;
  name: string;
  slug: string;
  owner_id: string;
  subscription_tier: string;
  subscription_status?: string;
  billing_period_start?: string;
  billing_period_end?: string;
  created_at?: string;
  updated_at?: string;
  stripe_customer_id?: string;
  stripe_subscription_id?: string;
  ai_credits_monthly_allowance?: number;
  ai_credits_remaining?: number;
  ai_credits_reset_date?: string;
}

export interface CreateOrganizationRequest {
  name: string;
  slug: string;
}

export interface UpdateOrganizationRequest {
  name?: string;
  subscription_tier?: string;
}

export interface OrganizationMember {
  id: string;
  organization_id: string;
  user_id: string;
  role: string;
  invited_at?: string;
  joined_at?: string;
  is_active?: boolean;
  user?: {
    id: string;
    email: string;
    username: string;
    full_name?: string;
  };
}

export interface InviteOrganizationMemberRequest {
  email: string;
  role: string;
}

export interface UpdateOrganizationMemberRoleRequest {
  role: string;
}

export interface PaymentMethod {
  id: string;
  organization_id: string;
  stripe_payment_method_id: string;
  card_brand?: string;
  card_last4?: string;
  card_exp_month?: number;
  card_exp_year?: number;
  is_default: boolean;
  created_at?: string;
}

export interface Subscription {
  id: string;
  organization_id: string;
  plan_id: string;
  status: string;
  current_period_start: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  created_at: string;
  updated_at?: string;
  stripe_subscription_id?: string;
  trial_end?: string;
}

export interface AICredits {
  total: number;
  used: number;
  remaining: number;
  reset_date: string;
  subscription_tier: string;
  monthly_allowance: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
}

/**
 * Organization API endpoints using RTK Query
 */
export const organizationApi = apiSlice.injectEndpoints({
  overrideExisting: true,
  endpoints: (builder) => ({
    // List organizations endpoint
    listOrganizations: builder.query<ApiResponse<Organization[]>, void>({
      query: () => '/organizations',
      providesTags: (result) =>
        result?.data
          ? [
              ...result.data.map((org) => ({ type: 'Organization' as const, id: org.id })),
              { type: 'Organization', id: 'LIST' },
            ]
          : [{ type: 'Organization', id: 'LIST' }],
    }),

    // Get organization by ID endpoint
    getOrganization: builder.query<ApiResponse<Organization>, string>({
      query: (id) => `/organizations/${id}`,
      providesTags: (result, error, id) => [{ type: 'Organization', id }],
    }),

    // Get organization by slug endpoint
    getOrganizationBySlug: builder.query<ApiResponse<Organization>, string>({
      query: (slug) => `/organizations/slug/${slug}`,
      providesTags: (result, error, slug) => result
        ? [{ type: 'Organization', id: result.data.id }]
        : [],
    }),

    // Create organization endpoint
    createOrganization: builder.mutation<ApiResponse<Organization>, CreateOrganizationRequest>({
      query: (data) => ({
        url: '/organizations',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'Organization', id: 'LIST' }],
    }),

    // Update organization endpoint
    updateOrganization: builder.mutation<ApiResponse<Organization>, { id: string; data: UpdateOrganizationRequest }>({
      query: ({ id, data }) => ({
        url: `/organizations/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Organization', id }],
    }),

    // Get AI credits for organization endpoint
    getAICredits: builder.query<ApiResponse<AICredits>, string>({
      query: (organizationId) => `/organizations/${organizationId}/ai-credits`,
      providesTags: (result, error, organizationId) => [{ type: 'Organization', id: `${organizationId}-credits` }],
    }),

    // Get AI credits history for organization endpoint
    getAICreditsHistory: builder.query<
      ApiResponse<any>,
      { organizationId: string; page?: number; per_page?: number; start_date?: string; end_date?: string }
    >({
      query: ({ organizationId, ...params }) => ({
        url: `/organizations/${organizationId}/ai-credits/history`,
        params,
      }),
      providesTags: (result, error, { organizationId }) => [{ type: 'Organization', id: `${organizationId}-credits-history` }],
    }),

    // Purchase AI credits for organization endpoint
    purchaseAICredits: builder.mutation<
      ApiResponse<any>,
      { organizationId: string; amount: number }
    >({
      query: ({ organizationId, amount }) => ({
        url: `/organizations/${organizationId}/ai-credits/purchase`,
        method: 'POST',
        body: { amount },
      }),
      invalidatesTags: (result, error, { organizationId }) => [
        { type: 'Organization', id: `${organizationId}-credits` },
        { type: 'Organization', id: `${organizationId}-credits-history` },
      ],
    }),

    // List organization members endpoint
    listOrganizationMembers: builder.query<ApiResponse<OrganizationMember[]>, string>({
      query: (organizationId) => `/organizations/${organizationId}/members`,
      providesTags: (result, error, organizationId) =>
        result?.data
          ? [
              ...result.data.map((member) => ({ type: 'OrganizationMember' as const, id: member.id })),
              { type: 'OrganizationMember', id: `${organizationId}-LIST` },
            ]
          : [{ type: 'OrganizationMember', id: `${organizationId}-LIST` }],
    }),

    // Invite organization member endpoint
    inviteOrganizationMember: builder.mutation<
      ApiResponse<OrganizationMember>,
      { organizationId: string; data: InviteOrganizationMemberRequest }
    >({
      query: ({ organizationId, data }) => ({
        url: `/organizations/${organizationId}/members`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { organizationId }) => [
        { type: 'OrganizationMember', id: `${organizationId}-LIST` },
      ],
    }),

    // Update organization member role endpoint
    updateOrganizationMemberRole: builder.mutation<
      ApiResponse<OrganizationMember>,
      { organizationId: string; memberId: string; data: UpdateOrganizationMemberRoleRequest }
    >({
      query: ({ organizationId, memberId, data }) => ({
        url: `/organizations/${organizationId}/members/${memberId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { organizationId, memberId }) => [
        { type: 'OrganizationMember', id: memberId },
        { type: 'OrganizationMember', id: `${organizationId}-LIST` },
      ],
    }),

    // Remove organization member endpoint
    removeOrganizationMember: builder.mutation<
      ApiResponse<void>,
      { organizationId: string; memberId: string }
    >({
      query: ({ organizationId, memberId }) => ({
        url: `/organizations/${organizationId}/members/${memberId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { organizationId }) => [
        { type: 'OrganizationMember', id: `${organizationId}-LIST` },
      ],
    }),

    // List organization payment methods endpoint
    listOrganizationPaymentMethods: builder.query<ApiResponse<PaymentMethod[]>, string>({
      query: (organizationId) => `/organizations/${organizationId}/payment-methods`,
      providesTags: (result, error, organizationId) =>
        result?.data
          ? [
              ...result.data.map((method) => ({ type: 'PaymentMethod' as const, id: method.id })),
              { type: 'PaymentMethod', id: `${organizationId}-LIST` },
            ]
          : [{ type: 'PaymentMethod', id: `${organizationId}-LIST` }],
    }),

    // Add organization payment method endpoint
    addOrganizationPaymentMethod: builder.mutation<
      ApiResponse<PaymentMethod>,
      { organizationId: string; paymentMethodId: string; setAsDefault?: boolean }
    >({
      query: ({ organizationId, paymentMethodId, setAsDefault }) => ({
        url: `/organizations/${organizationId}/payment-methods`,
        method: 'POST',
        body: { payment_method_id: paymentMethodId, set_as_default: setAsDefault },
      }),
      invalidatesTags: (result, error, { organizationId }) => [
        { type: 'PaymentMethod', id: `${organizationId}-LIST` },
      ],
    }),

    // Remove organization payment method endpoint
    removeOrganizationPaymentMethod: builder.mutation<
      ApiResponse<void>,
      { organizationId: string; paymentMethodId: string }
    >({
      query: ({ organizationId, paymentMethodId }) => ({
        url: `/organizations/${organizationId}/payment-methods/${paymentMethodId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { organizationId }) => [
        { type: 'PaymentMethod', id: `${organizationId}-LIST` },
      ],
    }),

    // Set default payment method endpoint
    setDefaultOrganizationPaymentMethod: builder.mutation<
      ApiResponse<PaymentMethod>,
      { organizationId: string; paymentMethodId: string }
    >({
      query: ({ organizationId, paymentMethodId }) => ({
        url: `/organizations/${organizationId}/payment-methods/${paymentMethodId}/set-default`,
        method: 'POST',
        body: { is_default: true },
      }),
      invalidatesTags: (result, error, { organizationId }) => [
        { type: 'PaymentMethod', id: `${organizationId}-LIST` },
      ],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useListOrganizationsQuery,
  useGetOrganizationQuery,
  useGetOrganizationBySlugQuery,
  useCreateOrganizationMutation,
  useUpdateOrganizationMutation,
  useGetAICreditsQuery,
  useGetAICreditsHistoryQuery,
  usePurchaseAICreditsMutation,
  useListOrganizationMembersQuery,
  useInviteOrganizationMemberMutation,
  useUpdateOrganizationMemberRoleMutation,
  useRemoveOrganizationMemberMutation,
  useListOrganizationPaymentMethodsQuery,
  useAddOrganizationPaymentMethodMutation,
  useRemoveOrganizationPaymentMethodMutation,
  useSetDefaultOrganizationPaymentMethodMutation,
} = organizationApi;

import { apiSlice } from '../apiSlice';

// Types for locale requests and responses
export interface Locale {
  id: string;
  code: string;
  name: string;
  native_name: string;
  direction?: string;
  is_active?: boolean;
  created_at?: string;
}

export interface LocaleQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  is_active?: boolean;
}

export interface CreateLocaleRequest {
  code: string;
  name: string;
  native_name: string;
  direction?: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
}

/**
 * Locale API endpoints using RTK Query
 */
export const localeApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // List locales endpoint
    listLocales: builder.query<ApiResponse<Locale[]>, LocaleQueryParams | undefined>({
      query: (params) => {
        if (!params) return '/locales';
        
        const searchParams = new URLSearchParams();
        if (params.page) searchParams.append('page', params.page.toString());
        if (params.limit) searchParams.append('limit', params.limit.toString());
        if (params.search) searchParams.append('search', params.search);
        if (params.is_active !== undefined) searchParams.append('is_active', params.is_active.toString());
        
        const queryString = searchParams.toString();
        return queryString ? `/locales?${queryString}` : '/locales';
      },
      providesTags: (result) =>
        result?.data
          ? [
              ...result.data.map((locale) => ({ type: 'Locale' as const, id: locale.id })),
              { type: 'Locale', id: 'LIST' },
            ]
          : [{ type: 'Locale', id: 'LIST' }],
    }),

    // Get locale by code endpoint
    getLocaleByCode: builder.query<ApiResponse<Locale>, string>({
      query: (code) => `/locales/${code}`,
      providesTags: (result, error, code) => [{ type: 'Locale', id: code }],
    }),

    // Create locale endpoint
    createLocale: builder.mutation<ApiResponse<Locale>, CreateLocaleRequest>({
      query: (data) => ({
        url: '/locales',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'Locale', id: 'LIST' }],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useListLocalesQuery,
  useGetLocaleByCodeQuery,
  useCreateLocaleMutation,
} = localeApi;

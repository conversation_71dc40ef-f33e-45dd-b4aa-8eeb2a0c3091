"use client"

import React, { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { useRouter } from "next/navigation"
import { 
  ChevronRight, 
  ChevronLeft, 
  Check, 
  Building2, 
  Users, 
  Globe, 
  Settings, 
  Languages,
  ArrowRight,
  FileText,
  Key,
  CheckCircle,
  User,
  Mail
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"

interface OnboardingStep {
  id: string
  title: string
  subtitle: string
  icon: React.ReactNode
  content: React.ReactNode
}

interface FormData {
  organizationName: string
  role: string
  teamSize: string
  primaryLanguage: string
  targetLanguages: string[]
  projectName: string
  projectType: string
}

interface APILocale {
  id: string
  code: string
  name: string
  native_name: string
  is_active: boolean
}

interface OnboardingState {
  isLoading: boolean
  error: string | null
  locales: APILocale[]
  createdOrganization: any
  createdProject: any
}

function WelcomeStep() {
  return (
    <div className="text-center space-y-6">
      <div className="mx-auto w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center">
        <Languages className="w-10 h-10 text-primary" />
      </div>
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Welcome to ADC Multi-Languages!</h2>
        <p className="text-muted-foreground max-w-md mx-auto">
          Let's get you set up with everything you need to start translating your content globally. This should only take a few minutes.
        </p>
      </div>
      <div className="grid grid-cols-2 gap-4 max-w-md mx-auto text-sm">
        <div className="flex items-center gap-2">
          <CheckCircle className="w-4 h-4 text-green-500" />
          <span>Create organization</span>
        </div>
        <div className="flex items-center gap-2">
          <CheckCircle className="w-4 h-4 text-green-500" />
          <span>Set up first project</span>
        </div>
        <div className="flex items-center gap-2">
          <CheckCircle className="w-4 h-4 text-green-500" />
          <span>Configure languages</span>
        </div>
        <div className="flex items-center gap-2">
          <CheckCircle className="w-4 h-4 text-green-500" />
          <span>Get API access</span>
        </div>
      </div>
    </div>
  )
}

function OrganizationStep({ data, onChange }: { data: FormData; onChange: (data: Partial<FormData>) => void }) {
  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold">Create Your Organization</h2>
        <p className="text-muted-foreground">Set up your workspace for your team</p>
      </div>
      
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="orgName">Organization Name</Label>
          <Input
            id="orgName"
            type="text"
            placeholder="Enter your organization name"
            value={data.organizationName}
            onChange={(e) => onChange({ organizationName: e.target.value })}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="role">Your Role</Label>
          <Select value={data.role} onValueChange={(value) => onChange({ role: value })}>
            <SelectTrigger>
              <SelectValue placeholder="Select your role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="founder">Founder/CEO</SelectItem>
              <SelectItem value="manager">Product Manager</SelectItem>
              <SelectItem value="developer">Developer</SelectItem>
              <SelectItem value="translator">Translator</SelectItem>
              <SelectItem value="marketing">Marketing</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="teamSize">Team Size</Label>
          <Select value={data.teamSize} onValueChange={(value) => onChange({ teamSize: value })}>
            <SelectTrigger>
              <SelectValue placeholder="Select team size" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">Just me</SelectItem>
              <SelectItem value="2-5">2-5 people</SelectItem>
              <SelectItem value="6-20">6-20 people</SelectItem>
              <SelectItem value="21-50">21-50 people</SelectItem>
              <SelectItem value="50+">50+ people</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  )
}

function LanguageStep({ 
  data, 
  onChange, 
  locales, 
  isLoading 
}: { 
  data: FormData; 
  onChange: (data: Partial<FormData>) => void;
  locales: APILocale[];
  isLoading: boolean;
}) {

  const toggleLanguage = (langCode: string) => {
    const current = data.targetLanguages || []
    const updated = current.includes(langCode)
      ? current.filter(l => l !== langCode)
      : [...current, langCode]
    onChange({ targetLanguages: updated })
  }

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold">Configure Languages</h2>
        <p className="text-muted-foreground">Choose your primary language and target languages</p>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="primaryLang">Primary Language (source content)</Label>
          <Select value={data.primaryLanguage} onValueChange={(value) => onChange({ primaryLanguage: value })}>
            <SelectTrigger>
              <SelectValue placeholder="Select primary language" />
            </SelectTrigger>
            <SelectContent>
              {isLoading ? (
                <SelectItem value="" disabled>Loading languages...</SelectItem>
              ) : (
                locales.map(lang => (
                  <SelectItem key={lang.code} value={lang.code}>
                    {lang.name} ({lang.native_name})
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Target Languages (select all that apply)</Label>
          <div className="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto">
            {isLoading ? (
              <div className="col-span-2 text-center py-4 text-muted-foreground">
                Loading languages...
              </div>
            ) : (
              locales.map(lang => (
                <button
                  key={lang.code}
                  type="button"
                  onClick={() => toggleLanguage(lang.code)}
                  className={`p-2 text-left rounded-md border transition-colors ${
                    (data.targetLanguages || []).includes(lang.code)
                      ? 'border-primary bg-primary/10 text-primary'
                      : 'border-border hover:border-primary/50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="text-sm font-medium">{lang.name}</span>
                      <div className="text-xs text-muted-foreground">{lang.native_name}</div>
                    </div>
                    {(data.targetLanguages || []).includes(lang.code) && (
                      <Check className="w-4 h-4" />
                    )}
                  </div>
                </button>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

function ProjectStep({ data, onChange }: { data: FormData; onChange: (data: Partial<FormData>) => void }) {
  const projectTypes = [
    { id: "website", name: "Website", description: "Translate website content, pages, and UI elements" },
    { id: "mobile", name: "Mobile App", description: "Localize mobile application strings and content" },
    { id: "software", name: "Software", description: "Translate software interface and documentation" },
    { id: "marketing", name: "Marketing", description: "Translate marketing materials and campaigns" },
    { id: "documentation", name: "Documentation", description: "Translate technical docs and help content" },
    { id: "other", name: "Other", description: "Custom translation project" }
  ]

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold">Create Your First Project</h2>
        <p className="text-muted-foreground">Set up your initial translation project</p>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="projectName">Project Name</Label>
          <Input
            id="projectName"
            type="text"
            placeholder="e.g., My Website, Mobile App v2.0"
            value={data.projectName}
            onChange={(e) => onChange({ projectName: e.target.value })}
          />
        </div>

        <div className="space-y-2">
          <Label>Project Type</Label>
          <div className="grid gap-3">
            {projectTypes.map(type => (
              <button
                key={type.id}
                type="button"
                onClick={() => onChange({ projectType: type.id })}
                className={`p-3 text-left rounded-md border transition-colors ${
                  data.projectType === type.id
                    ? 'border-primary bg-primary/10'
                    : 'border-border hover:border-primary/50'
                }`}
              >
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{type.name}</span>
                    {data.projectType === type.id && (
                      <Check className="w-4 h-4 text-primary" />
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">{type.description}</p>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

function CompletionStep({ 
  data, 
  organization, 
  project 
}: { 
  data: FormData; 
  organization: any; 
  project: any; 
}) {
  return (
    <div className="text-center space-y-6">
      <div className="mx-auto w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
        <CheckCircle className="w-10 h-10 text-green-600" />
      </div>
      
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">You're All Set!</h2>
        <p className="text-muted-foreground max-w-md mx-auto">
          Your organization and project have been created. Here's what we've set up for you:
        </p>
      </div>

      <Card className="text-left max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="text-lg">Setup Summary</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center gap-3">
            <Building2 className="w-4 h-4 text-muted-foreground" />
            <div>
              <p className="font-medium">{organization?.name || data.organizationName}</p>
              <p className="text-sm text-muted-foreground">
                Organization created {organization?.slug && `(${organization.slug})`}
              </p>
            </div>
          </div>
          
          <Separator />
          
          <div className="flex items-center gap-3">
            <FileText className="w-4 h-4 text-muted-foreground" />
            <div>
              <p className="font-medium">{project?.name || data.projectName}</p>
              <p className="text-sm text-muted-foreground">
                First project created {project?.slug && `(${project.slug})`}
              </p>
            </div>
          </div>
          
          <Separator />
          
          <div className="flex items-center gap-3">
            <Globe className="w-4 h-4 text-muted-foreground" />
            <div>
              <p className="font-medium">{(data.targetLanguages || []).length} languages</p>
              <p className="text-sm text-muted-foreground">Translation targets configured</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="space-y-2">
        <h3 className="font-semibold">Next Steps:</h3>
        <div className="space-y-2 text-sm text-muted-foreground">
          <p>✓ Upload your first content for translation</p>
          <p>✓ Invite team members to collaborate</p>
          <p>✓ Set up API integration (if needed)</p>
        </div>
      </div>
    </div>
  )
}

export default function OnboardingPage() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(0)
  const [formData, setFormData] = useState<FormData>({
    organizationName: "",
    role: "",
    teamSize: "",
    primaryLanguage: "",
    targetLanguages: [],
    projectName: "",
    projectType: ""
  })
  
  const [state, setState] = useState<OnboardingState>({
    isLoading: false,
    error: null,
    locales: [],
    createdOrganization: null,
    createdProject: null
  })

  const updateFormData = (data: Partial<FormData>) => {
    setFormData(prev => ({ ...prev, ...data }))
  }

  const updateState = (newState: Partial<OnboardingState>) => {
    setState(prev => ({ ...prev, ...newState }))
  }

  // Fetch locales when component mounts
  useEffect(() => {
    fetchLocales()
  }, [])

  const fetchLocales = async () => {
    try {
      updateState({ isLoading: true, error: null })
      const response = await fetch('/api/locales?is_active=true&limit=50')
      
      if (!response.ok) {
        throw new Error('Failed to fetch locales')
      }
      
      const data = await response.json()
      updateState({ 
        locales: data.locales || data.data || [], 
        isLoading: false 
      })
    } catch (error) {
      console.error('Error fetching locales:', error)
      updateState({ 
        error: 'Failed to load languages. Please try again.', 
        isLoading: false 
      })
    }
  }

  const createOrganization = async () => {
    try {
      updateState({ isLoading: true, error: null })
      
      // Generate slug from organization name
      const slug = formData.organizationName
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '')
      
      const response = await fetch('/api/organizations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.organizationName,
          slug: slug,
          description: `Organization for ${formData.teamSize} team members`
        })
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to create organization')
      }
      
      const organization = await response.json()
      updateState({ 
        createdOrganization: organization, 
        isLoading: false 
      })
      
      return organization
    } catch (error) {
      console.error('Error creating organization:', error)
      updateState({ 
        error: error instanceof Error ? error.message : 'Failed to create organization', 
        isLoading: false 
      })
      throw error
    }
  }

  const createProject = async (organizationId: string) => {
    try {
      updateState({ isLoading: true, error: null })
      
      // Generate slug from project name
      const slug = formData.projectName
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '')
      
      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.projectName,
          slug: slug,
          description: `${formData.projectType} project`,
          organization_id: organizationId,
          default_locale: formData.primaryLanguage
        })
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to create project')
      }
      
      const project = await response.json()
      updateState({ 
        createdProject: project, 
        isLoading: false 
      })
      
      // Add target locales to the project
      await addLocalesToProject(project.id)
      
      return project
    } catch (error) {
      console.error('Error creating project:', error)
      updateState({ 
        error: error instanceof Error ? error.message : 'Failed to create project', 
        isLoading: false 
      })
      throw error
    }
  }

  const addLocalesToProject = async (projectId: string) => {
    try {
      // Find locale IDs for the selected language codes
      const targetLocaleIds = state.locales
        .filter(locale => formData.targetLanguages.includes(locale.code))
        .map(locale => locale.id)
      
      // Add each target locale to the project
      for (const localeId of targetLocaleIds) {
        await fetch(`/api/projects/${projectId}/locales`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            locale_id: localeId,
            is_source: false
          })
        })
      }
    } catch (error) {
      console.error('Error adding locales to project:', error)
      // Don't throw here as the main project creation was successful
    }
  }

  const completeOnboarding = async () => {
    try {
      updateState({ isLoading: true, error: null })
      
      // Step 1: Create organization
      const organization = await createOrganization()
      
      // Step 2: Create project
      await createProject(organization.id)
      
      // Step 3: Update user profile with role preference
      try {
        await fetch('/api/users/me', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            preferred_language: formData.primaryLanguage
          })
        })
      } catch (error) {
        console.error('Error updating user profile:', error)
        // Don't fail the entire onboarding for this
      }
      
      updateState({ isLoading: false })
      
    } catch (error) {
      console.error('Error completing onboarding:', error)
      updateState({ 
        error: error instanceof Error ? error.message : 'Failed to complete onboarding', 
        isLoading: false 
      })
    }
  }

  const steps: OnboardingStep[] = [
    {
      id: "welcome",
      title: "Welcome",
      subtitle: "Get started",
      icon: <User className="w-5 h-5" />,
      content: <WelcomeStep />
    },
    {
      id: "organization",
      title: "Organization",
      subtitle: "Create workspace",
      icon: <Building2 className="w-5 h-5" />,
      content: <OrganizationStep data={formData} onChange={updateFormData} />
    },
    {
      id: "languages",
      title: "Languages",
      subtitle: "Configure locales",
      icon: <Globe className="w-5 h-5" />,
      content: (
        <LanguageStep 
          data={formData} 
          onChange={updateFormData} 
          locales={state.locales}
          isLoading={state.isLoading}
        />
      )
    },
    {
      id: "project",
      title: "Project",
      subtitle: "Create first project",
      icon: <FileText className="w-5 h-5" />,
      content: <ProjectStep data={formData} onChange={updateFormData} />
    },
    {
      id: "complete",
      title: "Complete",
      subtitle: "All done!",
      icon: <CheckCircle className="w-5 h-5" />,
      content: (
        <CompletionStep 
          data={formData} 
          organization={state.createdOrganization}
          project={state.createdProject}
        />
      )
    }
  ]

  const canContinue = () => {
    switch (currentStep) {
      case 0: return true
      case 1: return formData.organizationName && formData.role && formData.teamSize
      case 2: return formData.primaryLanguage && formData.targetLanguages.length > 0
      case 3: return formData.projectName && formData.projectType
      case 4: return true
      default: return false
    }
  }

  const nextStep = async () => {
    if (currentStep === steps.length - 2) {
      // This is the project step, complete onboarding before going to final step
      await completeOnboarding()
      if (!state.error) {
        setCurrentStep(currentStep + 1)
      }
    } else if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const isLastStep = currentStep === steps.length - 1

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b border-border/40">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-primary">
                <Languages className="h-5 w-5 text-primary-foreground" />
              </div>
              <span className="text-lg font-bold">ADC Multi-Languages</span>
            </div>
            <Badge variant="secondary">Setup Wizard</Badge>
          </div>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="border-b border-border/40 bg-muted/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between max-w-4xl mx-auto">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className="flex flex-col items-center">
                  <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors ${
                    index <= currentStep 
                      ? 'border-primary bg-primary text-primary-foreground'
                      : 'border-muted-foreground/30 bg-background text-muted-foreground'
                  }`}>
                    {index < currentStep ? (
                      <Check className="w-5 h-5" />
                    ) : (
                      step.icon
                    )}
                  </div>
                  <div className="mt-2 text-center">
                    <p className={`text-sm font-medium ${index <= currentStep ? 'text-foreground' : 'text-muted-foreground'}`}>
                      {step.title}
                    </p>
                    <p className="text-xs text-muted-foreground">{step.subtitle}</p>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 mx-4 ${
                    index < currentStep ? 'bg-primary' : 'bg-muted-foreground/30'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="max-w-2xl mx-auto">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Card>
                <CardContent className="p-8">
                  {state.error && (
                    <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
                      <div className="flex items-center gap-2 text-red-800">
                        <div className="w-4 h-4 rounded-full bg-red-500 flex items-center justify-center">
                          <span className="text-white text-xs">!</span>
                        </div>
                        <span className="font-medium">Error</span>
                      </div>
                      <p className="mt-1 text-sm text-red-700">{state.error}</p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => updateState({ error: null })}
                        className="mt-2"
                      >
                        Dismiss
                      </Button>
                    </div>
                  )}
                  {steps[currentStep].content}
                </CardContent>
              </Card>
            </motion.div>
          </AnimatePresence>

          {/* Navigation */}
          <div className="flex items-center justify-between mt-8">
            <Button
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 0}
              className="flex items-center gap-2"
            >
              <ChevronLeft className="w-4 h-4" />
              Back
            </Button>

            <span className="text-sm text-muted-foreground">
              Step {currentStep + 1} of {steps.length}
            </span>

            {isLastStep ? (
              <Button
                onClick={() => router.push('/dashboard')}
                className="flex items-center gap-2"
              >
                Go to Dashboard
                <ArrowRight className="w-4 h-4" />
              </Button>
            ) : (
              <Button
                onClick={nextStep}
                disabled={!canContinue() || state.isLoading}
                className="flex items-center gap-2"
              >
                {state.isLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    Continue
                    <ChevronRight className="w-4 h-4" />
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
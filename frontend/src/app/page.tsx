"use client"

import React, { useState, useEffect, useRef, use<PERSON><PERSON>back } from "react"
import { motion, AnimatePresence, useAnimation, useInView } from "framer-motion"
import { 
  Languages, 
  Globe as GlobeIcon, 
  Users, 
  Zap, 
  CheckCircle, 
  ArrowRight, 
  Star, 
  Quote,
  Mail,
  MapPin,
  Phone,
  Menu,
  Send
} from "lucide-react"
import createGlobe, { COBEOptions } from "cobe"
import confetti from "canvas-confetti"

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { Button } from "@/components/ui/button"
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu"
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ooter } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>allback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"

// Types
interface MenuItem {
  title: string
  url: string
  description?: string
  icon?: JSX.Element
  items?: MenuItem[]
}

interface Testimonial {
  id: number
  name: string
  role: string
  company: string
  content: string
  rating: number
  avatar: string
}

interface Feature {
  title: string
  description: string
  icon: JSX.Element
}

interface PricingTier {
  name: string
  price: { monthly: string | number; yearly: string | number }
  description: string
  features: string[]
  cta: string
  popular?: boolean
  highlighted?: boolean
}

// Globe Component
const GLOBE_CONFIG: COBEOptions = {
  width: 800,
  height: 800,
  onRender: () => {},
  devicePixelRatio: 2,
  phi: 0,
  theta: 0.3,
  dark: 0,
  diffuse: 0.4,
  mapSamples: 16000,
  mapBrightness: 1.2,
  baseColor: [1, 1, 1],
  markerColor: [59 / 255, 130 / 255, 246 / 255],
  glowColor: [1, 1, 1],
  markers: [
    { location: [14.5995, 120.9842], size: 0.03 },
    { location: [19.076, 72.8777], size: 0.1 },
    { location: [23.8103, 90.4125], size: 0.05 },
    { location: [30.0444, 31.2357], size: 0.07 },
    { location: [39.9042, 116.4074], size: 0.08 },
    { location: [-23.5505, -46.6333], size: 0.1 },
    { location: [19.4326, -99.1332], size: 0.1 },
    { location: [40.7128, -74.006], size: 0.1 },
    { location: [34.6937, 135.5022], size: 0.05 },
    { location: [41.0082, 28.9784], size: 0.06 },
  ],
}

function Globe({ className, config = GLOBE_CONFIG }: { className?: string; config?: COBEOptions }) {
  let phi = 0
  let width = 0
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const pointerInteracting = useRef(null)
  const pointerInteractionMovement = useRef(0)
  const [r, setR] = useState(0)

  const updatePointerInteraction = (value: any) => {
    pointerInteracting.current = value
    if (canvasRef.current) {
      canvasRef.current.style.cursor = value ? "grabbing" : "grab"
    }
  }

  const updateMovement = (clientX: any) => {
    if (pointerInteracting.current !== null) {
      const delta = clientX - pointerInteracting.current
      pointerInteractionMovement.current = delta
      setR(delta / 200)
    }
  }

  const onRender = useCallback(
    (state: Record<string, any>) => {
      if (!pointerInteracting.current) phi += 0.005
      state.phi = phi + r
      state.width = width * 2
      state.height = width * 2
    },
    [r],
  )

  const onResize = () => {
    if (canvasRef.current) {
      width = canvasRef.current.offsetWidth
    }
  }

  useEffect(() => {
    window.addEventListener("resize", onResize)
    onResize()

    const globe = createGlobe(canvasRef.current!, {
      ...config,
      width: width * 2,
      height: width * 2,
      onRender,
    })

    setTimeout(() => (canvasRef.current!.style.opacity = "1"))
    return () => globe.destroy()
  }, [])

  return (
    <div className={`absolute inset-0 mx-auto aspect-[1/1] w-full max-w-[600px] ${className}`}>
      <canvas
        className="size-full opacity-0 transition-opacity duration-500 [contain:layout_paint_size]"
        ref={canvasRef}
        onPointerDown={(e) =>
          updatePointerInteraction(
            e.clientX - pointerInteractionMovement.current,
          )
        }
        onPointerUp={() => updatePointerInteraction(null)}
        onPointerOut={() => updatePointerInteraction(null)}
        onMouseMove={(e) => updateMovement(e.clientX)}
        onTouchMove={(e) =>
          e.touches[0] && updateMovement(e.touches[0].clientX)
        }
      />
    </div>
  )
}

// Navbar Component
function Navbar() {
  const logo = {
    url: "#",
    src: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 2L2 7l10 5 10-5-10-5z'/%3E%3Cpath d='M2 17l10 5 10-5'/%3E%3Cpath d='M2 12l10 5 10-5'/%3E%3C/svg%3E",
    alt: "ADC Multi-Languages",
    title: "ADC Multi-Languages",
  }

  const menu: MenuItem[] = [
    { title: "Home", url: "#home" },
    {
      title: "Features",
      url: "#features",
      items: [
        {
          title: "Translation Management",
          description: "Manage all your translations in one place",
          icon: <Languages className="size-5 shrink-0" />,
          url: "#features",
        },
        {
          title: "Global Reach",
          description: "Connect with audiences worldwide",
          icon: <GlobeIcon className="size-5 shrink-0" />,
          url: "#stats",
        },
        {
          title: "Team Collaboration",
          description: "Work together with your translation team",
          icon: <Users className="size-5 shrink-0" />,
          url: "#testimonials",
        },
        {
          title: "Fast Processing",
          description: "Lightning-fast translation processing",
          icon: <Zap className="size-5 shrink-0" />,
          url: "#features",
        },
      ],
    },
    { title: "Pricing", url: "#pricing" },
    { title: "About", url: "#testimonials" },
    { title: "Contact", url: "#contact" },
  ]

  const auth = {
    login: { text: "Log in", url: "/auth/signin" },
    signup: { text: "Get Started", url: "/auth/signup" },
  }

  const renderMenuItem = (item: MenuItem) => {
    if (item.items) {
      return (
        <NavigationMenuItem key={item.title} className="text-muted-foreground">
          <NavigationMenuTrigger>{item.title}</NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul className="w-80 p-3">
              <NavigationMenuLink>
                {item.items.map((subItem) => (
                  <li key={subItem.title}>
                    <a
                      className="flex select-none gap-4 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-muted hover:text-accent-foreground"
                      href={subItem.url}
                    >
                      {subItem.icon}
                      <div>
                        <div className="text-sm font-semibold">
                          {subItem.title}
                        </div>
                        {subItem.description && (
                          <p className="text-sm leading-snug text-muted-foreground">
                            {subItem.description}
                          </p>
                        )}
                      </div>
                    </a>
                  </li>
                ))}
              </NavigationMenuLink>
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>
      )
    }

    return (
      <a
        key={item.title}
        className="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium text-muted-foreground transition-colors hover:bg-muted hover:text-accent-foreground"
        href={item.url}
      >
        {item.title}
      </a>
    )
  }

  const renderMobileMenuItem = (item: MenuItem) => {
    if (item.items) {
      return (
        <AccordionItem key={item.title} value={item.title} className="border-b-0">
          <AccordionTrigger className="py-0 font-semibold hover:no-underline">
            {item.title}
          </AccordionTrigger>
          <AccordionContent className="mt-2">
            {item.items.map((subItem) => (
              <a
                key={subItem.title}
                className="flex select-none gap-4 rounded-md p-3 leading-none outline-none transition-colors hover:bg-muted hover:text-accent-foreground"
                href={subItem.url}
              >
                {subItem.icon}
                <div>
                  <div className="text-sm font-semibold">{subItem.title}</div>
                  {subItem.description && (
                    <p className="text-sm leading-snug text-muted-foreground">
                      {subItem.description}
                    </p>
                  )}
                </div>
              </a>
            ))}
          </AccordionContent>
        </AccordionItem>
      )
    }

    return (
      <a key={item.title} href={item.url} className="font-semibold">
        {item.title}
      </a>
    )
  }

  return (
    <section className="py-4 border-b border-border/40">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <nav className="hidden justify-between lg:flex">
          <div className="flex items-center gap-6">
            <a href={logo.url} className="flex items-center gap-2">
              <img src={logo.src} className="w-8 h-8" alt={logo.alt} />
              <span className="text-lg font-semibold">{logo.title}</span>
            </a>
            <div className="flex items-center">
              <NavigationMenu>
                <NavigationMenuList>
                  {menu.map((item) => renderMenuItem(item))}
                </NavigationMenuList>
              </NavigationMenu>
            </div>
          </div>
          <div className="flex gap-2">
            <Button asChild variant="outline" size="sm">
              <a href={auth.login.url}>{auth.login.text}</a>
            </Button>
            <Button asChild size="sm">
              <a href={auth.signup.url}>{auth.signup.text}</a>
            </Button>
          </div>
        </nav>
        <div className="block lg:hidden">
          <div className="flex items-center justify-between">
            <a href={logo.url} className="flex items-center gap-2">
              <img src={logo.src} className="w-8 h-8" alt={logo.alt} />
              <span className="text-lg font-semibold">{logo.title}</span>
            </a>
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" size="icon">
                  <Menu className="size-4" />
                </Button>
              </SheetTrigger>
              <SheetContent className="overflow-y-auto">
                <SheetHeader>
                  <SheetTitle>
                    <a href={logo.url} className="flex items-center gap-2">
                      <img src={logo.src} className="w-8 h-8" alt={logo.alt} />
                      <span className="text-lg font-semibold">
                        {logo.title}
                      </span>
                    </a>
                  </SheetTitle>
                </SheetHeader>
                <div className="my-6 flex flex-col gap-6">
                  <Accordion
                    type="single"
                    collapsible
                    className="flex w-full flex-col gap-4"
                  >
                    {menu.map((item) => renderMobileMenuItem(item))}
                  </Accordion>
                  <div className="flex flex-col gap-3">
                    <Button asChild variant="outline">
                      <a href={auth.login.url}>{auth.login.text}</a>
                    </Button>
                    <Button asChild>
                      <a href={auth.signup.url}>{auth.signup.text}</a>
                    </Button>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </section>
  )
}

// Hero Section
function HeroSection() {
  return (
    <section id="home" className="relative bg-background text-foreground py-12 md:py-24 lg:py-32 overflow-hidden">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="relative flex flex-col gap-12 lg:gap-24">
          <div className="relative z-10 flex flex-col items-center gap-6 pt-8 md:pt-16 text-center lg:gap-12">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="inline-block bg-gradient-to-b from-foreground via-foreground/90 to-muted-foreground bg-clip-text text-transparent text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl leading-[1.1] drop-shadow-sm max-w-4xl"
            >
              Translate Your World with ADC Multi-Languages
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="max-w-2xl text-sm sm:text-base md:text-lg lg:text-xl text-muted-foreground font-medium px-4"
            >
              The comprehensive translation management platform that connects your content with global audiences. Streamline workflows, collaborate seamlessly, and scale your international presence.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="relative z-10 flex flex-col sm:flex-row flex-wrap justify-center gap-4 w-full max-w-md"
            >
              <Button size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg w-full sm:w-auto">
                Start Free Trial
              </Button>
              <Button size="lg" variant="outline" className="w-full sm:w-auto">
                <GlobeIcon className="mr-2 h-4 w-4" />
                View Demo
              </Button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="relative w-full pt-12 max-w-4xl"
            >
              <div className="relative flex size-full max-w-lg items-center justify-center overflow-hidden rounded-lg border bg-background px-8 sm:px-20 md:px-40 pb-20 sm:pb-40 pt-8 md:pb-60 md:shadow-xl mx-auto">
                <span className="pointer-events-none whitespace-pre-wrap bg-gradient-to-b from-black to-gray-300/80 bg-clip-text text-center text-4xl sm:text-6xl md:text-8xl font-semibold leading-none text-transparent dark:from-white dark:to-slate-900/10">
                  Global
                </span>
                <Globe className="top-28" />
                <div className="pointer-events-none absolute inset-0 h-full bg-[radial-gradient(circle_at_50%_200%,rgba(0,0,0,0.2),rgba(255,255,255,0))]" />
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  )
}

// Features Section
function FeaturesSection() {
  const features: Feature[] = [
    {
      title: "AI-Powered Translation",
      description: "Advanced machine learning algorithms ensure accurate and contextual translations across 100+ languages.",
      icon: <Zap className="h-8 w-8 text-primary" />
    },
    {
      title: "Team Collaboration",
      description: "Real-time collaboration tools for translators, reviewers, and project managers to work together seamlessly.",
      icon: <Users className="h-8 w-8 text-primary" />
    },
    {
      title: "Global Reach",
      description: "Connect with audiences worldwide with our comprehensive language support and cultural adaptation features.",
      icon: <GlobeIcon className="h-8 w-8 text-primary" />
    },
    {
      title: "Quality Assurance",
      description: "Built-in quality checks, terminology management, and review workflows ensure consistent, high-quality translations.",
      icon: <CheckCircle className="h-8 w-8 text-primary" />
    },
    {
      title: "API Integration",
      description: "Seamlessly integrate with your existing tools and workflows through our comprehensive REST API.",
      icon: <Languages className="h-8 w-8 text-primary" />
    },
    {
      title: "Real-time Updates",
      description: "Instant synchronization across all platforms ensures your translations are always up-to-date.",
      icon: <ArrowRight className="h-8 w-8 text-primary" />
    }
  ]

  return (
    <section id="features" className="w-full py-16 md:py-20 lg:py-32">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col gap-10 lg:gap-16">
          <div className="flex gap-4 flex-col items-start max-w-4xl mx-auto text-center lg:text-left">
            <div className="mx-auto lg:mx-0">
              <Badge>Platform Features</Badge>
            </div>
            <div className="flex gap-4 flex-col">
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl tracking-tighter font-regular">
                Everything you need for global translation
              </h2>
              <p className="text-base sm:text-lg leading-relaxed tracking-tight text-muted-foreground max-w-2xl">
                Powerful features designed to streamline your translation workflow and accelerate your global expansion.
              </p>
            </div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex flex-col gap-4 p-6 rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="bg-muted rounded-md aspect-video mb-2 flex items-center justify-center">
                  {feature.icon}
                </div>
                <h3 className="text-lg sm:text-xl tracking-tight font-semibold">{feature.title}</h3>
                <p className="text-muted-foreground text-sm sm:text-base leading-relaxed">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

// Stats Section
function StatsSection() {
  const stats = [
    {
      name: "Languages Supported",
      value: "100+",
      change: "+12 this month",
      changeType: "positive",
    },
    {
      name: "Active Users",
      value: "50K+",
      change: "+23.1%",
      changeType: "positive",
    },
    {
      name: "Translation Accuracy",
      value: "99.2%",
      change: "+0.3%",
      changeType: "positive",
    },
  ]

  return (
    <section id="stats" className="w-full py-16 md:py-20 lg:py-24 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4">Trusted by Global Teams</h2>
          <p className="text-muted-foreground text-sm sm:text-base lg:text-lg max-w-2xl mx-auto">See how ADC Multi-Languages is making an impact worldwide</p>
        </div>
        <dl className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 max-w-5xl mx-auto">
          {stats.map((item, index) => (
            <motion.div
              key={item.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="p-0 gap-0 h-full">
                <CardContent className="p-6">
                  <dd className="flex items-start justify-between space-x-2 mb-2">
                    <span className="truncate text-xs sm:text-sm text-muted-foreground">
                      {item.name}
                    </span>
                    <span className="text-xs sm:text-sm font-medium text-emerald-700 dark:text-emerald-500">
                      {item.change}
                    </span>
                  </dd>
                  <dd className="text-2xl sm:text-3xl font-semibold text-foreground">
                    {item.value}
                  </dd>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </dl>
      </div>
    </section>
  )
}

// Pricing Section
function PricingSection() {
  const [selectedFrequency, setSelectedFrequency] = useState("monthly")
  const frequencies = ["monthly", "yearly"]

  const tiers: PricingTier[] = [
    {
      name: "Starter",
      price: { monthly: "Free", yearly: "Free" },
      description: "Perfect for small projects",
      features: [
        "Up to 1,000 words/month",
        "5 languages",
        "Basic translation tools",
        "Community support",
        "Standard quality",
      ],
      cta: "Get Started",
    },
    {
      name: "Professional",
      price: { monthly: 49, yearly: 39 },
      description: "Great for growing businesses",
      features: [
        "Up to 50,000 words/month",
        "25+ languages",
        "Advanced translation tools",
        "Priority support",
        "Quality assurance",
        "Team collaboration",
      ],
      cta: "Start Free Trial",
      popular: true,
    },
    {
      name: "Enterprise",
      price: { monthly: 149, yearly: 119 },
      description: "For large organizations",
      features: [
        "Unlimited words",
        "100+ languages",
        "Custom integrations",
        "Dedicated support",
        "Advanced analytics",
        "Custom workflows",
        "SLA guarantee",
      ],
      cta: "Contact Sales",
    },
  ]

  const Tab = ({ text, selected, setSelected, discount }: { text: string; selected: boolean; setSelected: (text: string) => void; discount?: boolean }) => (
    <button
      onClick={() => setSelected(text)}
      className={`relative px-6 py-2 text-sm font-medium transition-colors ${
        selected ? "text-foreground" : "text-muted-foreground hover:text-foreground"
      }`}
    >
      {selected && (
        <motion.div
          layoutId="tab-background"
          className="absolute inset-0 bg-background rounded-full shadow-sm border"
          initial={false}
          transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
        />
      )}
      <span className="relative z-10 flex items-center gap-1">
        {text}
        {discount && (
          <span className="ml-1 rounded-full bg-primary px-2 py-0.5 text-xs text-primary-foreground">
            -20%
          </span>
        )}
      </span>
    </button>
  )

  const PricingCard = ({ tier }: { tier: PricingTier }) => {
    const currentPrice = tier.price[selectedFrequency as keyof typeof tier.price]
    const isNumber = typeof currentPrice === "number"
    const monthlyPrice = tier.price.monthly
    const yearlyPrice = tier.price.yearly
    
    return (
      <Card className={`relative ${tier.popular ? "border-primary shadow-lg scale-105" : ""}`}>
        {tier.popular && (
          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <Badge className="bg-primary text-primary-foreground">Most Popular</Badge>
          </div>
        )}
        <CardContent className="p-6">
          <div className="text-center">
            <h3 className="text-xl font-semibold">{tier.name}</h3>
            <p className="text-muted-foreground text-sm mt-1">{tier.description}</p>
            <div className="mt-4 mb-6">
              <div className="flex flex-col items-center">
                <span className="text-4xl font-bold">
                  {isNumber ? `$${currentPrice}` : currentPrice}
                </span>
                {isNumber && (
                  <div className="flex flex-col items-center">
                    <span className="text-muted-foreground text-sm">
                      /month{selectedFrequency === "yearly" && " billed annually"}
                    </span>
                    {selectedFrequency === "yearly" && typeof monthlyPrice === "number" && typeof yearlyPrice === "number" && (
                      <div className="mt-1 text-xs text-muted-foreground">
                        Save ${(monthlyPrice - yearlyPrice) * 12}/year
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
          <ul className="space-y-3 mb-6">
            {tier.features.map((feature, index) => (
              <li key={index} className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-primary" />
                <span className="text-sm">{feature}</span>
              </li>
            ))}
          </ul>
        </CardContent>
        <CardFooter>
          <Button className="w-full" variant={tier.popular ? "default" : "outline"}>
            {tier.cta}
          </Button>
        </CardFooter>
      </Card>
    )
  }

  return (
    <section id="pricing" className="py-16 md:py-20 lg:py-24 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center gap-10 lg:gap-16">
          <div className="space-y-6 lg:space-y-8 text-center max-w-3xl">
            <div className="space-y-4">
              <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-medium">Simple, Transparent Pricing</h1>
              <p className="text-muted-foreground text-sm sm:text-base lg:text-lg">Choose the perfect plan for your translation needs</p>
            </div>
            <div className="mx-auto flex w-fit rounded-full bg-muted p-1">
              {frequencies.map((freq) => (
                <Tab
                  key={freq}
                  text={freq}
                  selected={selectedFrequency === freq}
                  setSelected={setSelectedFrequency}
                  discount={freq === "yearly"}
                />
              ))}
            </div>
          </div>

          <div className="grid w-full max-w-6xl gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {tiers.map((tier) => (
              <PricingCard key={tier.name} tier={tier} />
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

// Testimonials Section
function TestimonialsSection() {
  const [activeIndex, setActiveIndex] = useState(0)
  const sectionRef = useRef(null)
  const isInView = useInView(sectionRef, { once: true, amount: 0.2 })
  const controls = useAnimation()

  const testimonials: Testimonial[] = [
    {
      id: 1,
      name: "Sarah Johnson",
      role: "Marketing Director",
      company: "GlobalTech Inc",
      content: "ADC Multi-Languages transformed our international expansion. The platform's accuracy and speed helped us launch in 15 new markets within 6 months.",
      rating: 5,
      avatar: "https://randomuser.me/api/portraits/women/32.jpg",
    },
    {
      id: 2,
      name: "Miguel Rodriguez",
      role: "Product Manager",
      company: "InnovateCorp",
      content: "The collaboration features are outstanding. Our distributed translation team can work seamlessly together, and the quality assurance tools ensure consistency across all languages.",
      rating: 5,
      avatar: "https://randomuser.me/api/portraits/men/44.jpg",
    },
    {
      id: 3,
      name: "Li Wei",
      role: "Localization Lead",
      company: "TechStart",
      content: "We've tried many translation platforms, but ADC Multi-Languages stands out for its AI accuracy and intuitive workflow management. It's been a game-changer for our team.",
      rating: 5,
      avatar: "https://randomuser.me/api/portraits/women/46.jpg",
    },
  ]

  useEffect(() => {
    if (isInView) {
      controls.start("visible")
    }
  }, [isInView, controls])

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveIndex((current) => (current + 1) % testimonials.length)
    }, 6000)
    return () => clearInterval(interval)
  }, [testimonials.length])

  return (
    <section id="testimonials" ref={sectionRef} className="py-16 md:py-20 lg:py-24 overflow-hidden bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0 }}
          animate={controls}
          variants={{
            visible: {
              opacity: 1,
              transition: { staggerChildren: 0.1, delayChildren: 0.2 }
            }
          }}
          className="grid grid-cols-1 gap-12 lg:gap-24 lg:grid-cols-2 items-center"
        >
          <motion.div
            variants={{
              hidden: { opacity: 0, y: 20 },
              visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
            }}
            className="flex flex-col justify-center order-2 lg:order-1"
          >
            <div className="space-y-6 max-w-lg mx-auto lg:mx-0">
              <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary/10 text-primary">
                <Star className="mr-1 h-3.5 w-3.5 fill-primary" />
                <span>Trusted by developers</span>
              </div>

              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold tracking-tighter">
                Loved by teams worldwide
              </h2>

              <p className="text-muted-foreground text-sm sm:text-base lg:text-lg leading-relaxed">
                See what translation teams and global companies have to say about our platform.
              </p>

              <div className="flex items-center gap-3 pt-4">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setActiveIndex(index)}
                    className={`h-2.5 rounded-full transition-all duration-300 ${
                      activeIndex === index ? "w-10 bg-primary" : "w-2.5 bg-muted-foreground/30"
                    }`}
                  />
                ))}
              </div>
            </div>
          </motion.div>

          <motion.div
            variants={{
              hidden: { opacity: 0, y: 20 },
              visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
            }}
            className="relative h-full min-h-[300px] md:min-h-[400px] order-1 lg:order-2"
          >
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={testimonial.id}
                className="absolute inset-0"
                initial={{ opacity: 0, x: 100 }}
                animate={{
                  opacity: activeIndex === index ? 1 : 0,
                  x: activeIndex === index ? 0 : 100,
                  scale: activeIndex === index ? 1 : 0.9,
                }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
                style={{ zIndex: activeIndex === index ? 10 : 0 }}
              >
                <div className="bg-card border shadow-lg rounded-xl p-6 sm:p-8 h-full flex flex-col">
                  <div className="mb-6 flex gap-2">
                    {Array(testimonial.rating)
                      .fill(0)
                      .map((_, i) => (
                        <Star key={i} className="h-4 w-4 sm:h-5 sm:w-5 fill-yellow-500 text-yellow-500" />
                      ))}
                  </div>

                  <div className="relative mb-6 flex-1">
                    <Quote className="absolute -top-2 -left-2 h-6 w-6 sm:h-8 sm:w-8 text-primary/20 rotate-180" />
                    <p className="relative z-10 text-sm sm:text-base lg:text-lg font-medium leading-relaxed">"{testimonial.content}"</p>
                  </div>

                  <Separator className="my-4" />

                  <div className="flex items-center gap-4">
                    <Avatar className="h-10 w-10 sm:h-12 sm:w-12 border">
                      <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                      <AvatarFallback>{testimonial.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-semibold text-sm sm:text-base">{testimonial.name}</h3>
                      <p className="text-xs sm:text-sm text-muted-foreground">
                        {testimonial.role}, {testimonial.company}
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

// Newsletter Section
function NewsletterSection() {
  const [email, setEmail] = useState("")
  const [error, setError] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")

    if (!email) {
      setError("Email is required")
      return
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      setError("Please enter a valid email address")
      return
    }

    setIsSubmitting(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      setIsSubmitted(true)
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 },
      })
    } catch (err) {
      setError("An error occurred. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <section id="newsletter" className="py-16 md:py-20 lg:py-24 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto">
          <div className="bg-card border border-primary/10 rounded-lg p-6 sm:p-8">
            <AnimatePresence mode="wait">
              {!isSubmitted ? (
                <motion.form
                  key="form"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  onSubmit={handleSubmit}
                  className="flex flex-col gap-6"
                >
                  <div className="text-center">
                    <motion.h2
                      className="text-2xl sm:text-3xl font-bold text-foreground mb-2"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                    >
                      Stay Updated with ADC Multi-Languages
                    </motion.h2>
                    <motion.p
                      className="text-muted-foreground text-sm sm:text-base"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                    >
                      Get the latest updates on new features, language support, and translation insights.
                    </motion.p>
                  </div>
                  <div className="space-y-4">
                    <motion.div
                      className="flex flex-col sm:flex-row gap-3"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.6 }}
                    >
                      <input
                        type="email"
                        placeholder="Enter your email address"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="flex-1 px-4 py-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary text-sm sm:text-base"
                        disabled={isSubmitting}
                      />
                      <Button
                        type="submit"
                        disabled={isSubmitting}
                        className="px-6 w-full sm:w-auto"
                      >
                        <Send className="h-4 w-4 mr-2" />
                        {isSubmitting ? "Subscribing..." : "Subscribe"}
                      </Button>
                    </motion.div>
                    <AnimatePresence>
                      {error && (
                        <motion.p
                          className="text-red-500 text-sm"
                          initial={{ opacity: 0, y: 5 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -5 }}
                        >
                          {error}
                        </motion.p>
                      )}
                    </AnimatePresence>
                  </div>
                </motion.form>
              ) : (
                <motion.div
                  key="success"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0 }}
                  className="text-center py-8"
                >
                  <h2 className="text-2xl sm:text-3xl font-bold text-foreground mb-4">
                    Thank you for subscribing!
                  </h2>
                  <p className="text-muted-foreground text-sm sm:text-base">
                    We've sent a confirmation email to your inbox. Stay tuned for updates!
                  </p>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </section>
  )
}

// Contact Section
function ContactSection() {
  return (
    <section id="contact" className="bg-background py-16 md:py-20 lg:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-12 lg:mb-16 text-center max-w-4xl mx-auto">
          <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-6">
            Get in Touch
          </h1>
          <p className="text-sm sm:text-base lg:text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
            Ready to start your global translation journey? Contact our team for personalized support.
          </p>
        </div>
        <div className="grid gap-8 sm:gap-10 lg:grid-cols-3 max-w-4xl mx-auto">
          <div className="text-center p-6 rounded-lg hover:bg-muted/50 transition-colors">
            <span className="mb-6 flex size-16 flex-col items-center justify-center rounded-full bg-primary/10 mx-auto">
              <Mail className="h-8 w-8 text-primary" />
            </span>
            <h3 className="mb-3 text-xl font-semibold">Email</h3>
            <p className="mb-4 text-muted-foreground text-sm sm:text-base leading-relaxed">We respond to all emails within 24 hours.</p>
            <a
              href="mailto:<EMAIL>"
              className="font-semibold hover:underline text-primary text-sm sm:text-base transition-colors"
            >
              <EMAIL>
            </a>
          </div>
          <div className="text-center p-6 rounded-lg hover:bg-muted/50 transition-colors">
            <span className="mb-6 flex size-16 flex-col items-center justify-center rounded-full bg-primary/10 mx-auto">
              <MapPin className="h-8 w-8 text-primary" />
            </span>
            <h3 className="mb-3 text-xl font-semibold">Office</h3>
            <p className="mb-4 text-muted-foreground text-sm sm:text-base leading-relaxed">Visit our headquarters for a consultation.</p>
            <a href="#" className="font-semibold hover:underline text-primary text-sm sm:text-base transition-colors">
              123 Translation Ave, Global City, GC 12345
            </a>
          </div>
          <div className="text-center p-6 rounded-lg hover:bg-muted/50 transition-colors lg:col-span-1">
            <span className="mb-6 flex size-16 flex-col items-center justify-center rounded-full bg-primary/10 mx-auto">
              <Phone className="h-8 w-8 text-primary" />
            </span>
            <h3 className="mb-3 text-xl font-semibold">Phone</h3>
            <p className="mb-4 text-muted-foreground text-sm sm:text-base leading-relaxed">Available Mon-Fri, 9am-6pm EST.</p>
            <a href="tel:******-0123" className="font-semibold hover:underline text-primary text-sm sm:text-base transition-colors">
              +****************
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}

// Footer
function Footer() {
  return (
    <footer className="border-t border-border/40 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12 lg:py-16">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-8 lg:gap-12">
          <div className="space-y-6 sm:col-span-2 lg:col-span-2">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-primary">
                <Languages className="h-6 w-6 text-primary-foreground" />
              </div>
              <span className="text-xl font-bold">ADC Multi-Languages</span>
            </div>
            <p className="text-muted-foreground text-sm leading-relaxed max-w-md">
              The comprehensive translation management platform that connects your content with global audiences. Streamline workflows, collaborate seamlessly, and scale your international presence.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-muted-foreground hover:text-primary transition-colors">
                <span className="sr-only">Twitter</span>
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84" />
                </svg>
              </a>
              <a href="#" className="text-muted-foreground hover:text-primary transition-colors">
                <span className="sr-only">GitHub</span>
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clipRule="evenodd" />
                </svg>
              </a>
              <a href="#" className="text-muted-foreground hover:text-primary transition-colors">
                <span className="sr-only">LinkedIn</span>
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clipRule="evenodd" />
                </svg>
              </a>
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold mb-6 text-base">Product</h3>
            <ul className="space-y-4 text-sm text-muted-foreground">
              <li><a href="#" className="hover:text-primary transition-colors hover:underline">Features</a></li>
              <li><a href="#pricing" className="hover:text-primary transition-colors hover:underline">Pricing</a></li>
              <li><a href="#" className="hover:text-primary transition-colors hover:underline">API</a></li>
              <li><a href="#" className="hover:text-primary transition-colors hover:underline">Integrations</a></li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold mb-6 text-base">Company</h3>
            <ul className="space-y-4 text-sm text-muted-foreground">
              <li><a href="#" className="hover:text-primary transition-colors hover:underline">About</a></li>
              <li><a href="#" className="hover:text-primary transition-colors hover:underline">Blog</a></li>
              <li><a href="#" className="hover:text-primary transition-colors hover:underline">Careers</a></li>
              <li><a href="#contact" className="hover:text-primary transition-colors hover:underline">Contact</a></li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold mb-6 text-base">Support</h3>
            <ul className="space-y-4 text-sm text-muted-foreground">
              <li><a href="#" className="hover:text-primary transition-colors hover:underline">Help Center</a></li>
              <li><a href="#" className="hover:text-primary transition-colors hover:underline">Documentation</a></li>
              <li><a href="#" className="hover:text-primary transition-colors hover:underline">Status</a></li>
              <li><a href="#" className="hover:text-primary transition-colors hover:underline">Privacy Policy</a></li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-border/40 mt-12 pt-8 flex flex-col sm:flex-row justify-between items-center gap-4">
          <p className="text-xs sm:text-sm text-muted-foreground">
            &copy; 2024 ADC Multi-Languages. All rights reserved.
          </p>
          <div className="flex items-center gap-6 text-xs sm:text-sm text-muted-foreground">
            <a href="#" className="hover:text-primary transition-colors">Terms of Service</a>
            <a href="#" className="hover:text-primary transition-colors">Privacy Policy</a>
            <a href="#" className="hover:text-primary transition-colors">Cookie Policy</a>
          </div>
        </div>
      </div>
    </footer>
  )
}

// Main Landing Page Component
export default function ADCMultiLanguagesLanding() {
  useEffect(() => {
    // Add smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth'
    
    return () => {
      // Cleanup
      document.documentElement.style.scrollBehavior = 'auto'
    }
  }, [])

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <HeroSection />
      <StatsSection />
      <FeaturesSection />
      <PricingSection />
      <TestimonialsSection />
      <NewsletterSection />
      <ContactSection />
      <Footer />
    </div>
  )
}

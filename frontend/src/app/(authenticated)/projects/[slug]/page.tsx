'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  useGetProjectBySlugQuery,
  useListProjectLocalesQuery,
  useUpdateProjectMutation,
  useDeleteProjectMutation,
} from '@/lib/redux/api/endpoints/projectApi';
import { useListOrganizationsQuery } from '@/lib/redux/api/endpoints';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ArrowLeft,
  Settings,
  Globe,
  Trash2,
  Edit,
  Plus,
  FileText,
  Languages,
  Key,
} from 'lucide-react';
import { TranslationsTab } from '@/components/project/TranslationsTab';
import { useToast } from '@/components/ui/use-toast';
import { formatDate } from '@/lib/utils';
import AddLocaleDialog from '@/components/projects/AddLocaleDialog';
import EditProjectDialog from '@/components/projects/EditProjectDialog';
import DeleteProjectDialog from '@/components/projects/DeleteProjectDialog';

interface ProjectPageProps {
  params: {
    slug: string;
  };
}

export default function ProjectPage({ params }: ProjectPageProps) {
  // Create local variable to avoid direct access to params properties
  // This approach works with the current version of Next.js and avoids the warning
  const projectSlug = params?.slug;
  const router = useRouter();
  const { toast } = useToast();

  const [activeTab, setActiveTab] = useState('overview');
  const [isAddLocaleDialogOpen, setIsAddLocaleDialogOpen] = useState(false);
  const [isEditProjectDialogOpen, setIsEditProjectDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [organizationId, setOrganizationId] = useState<string | null>(null);

  // Fetch organizations to get the first one's ID
  const {
    data: organizationsData,
    isLoading: isLoadingOrganizations,
  } = useListOrganizationsQuery();

  // Set the first organization as selected when data is loaded
  useEffect(() => {
    if (organizationsData?.data?.length && !organizationId) {
      setOrganizationId(organizationsData.data[0].id);
    }
  }, [organizationsData, organizationId]);

  // Fetch project data by slug
  const {
    data: projectData,
    isLoading: isLoadingProject,
    error: projectError,
    refetch: refetchProject,
  } = useGetProjectBySlugQuery(
    { slug: projectSlug, organization_id: organizationId || '' },
    { skip: !organizationId }
  );

  // Fetch project locales once we have the project ID
  const {
    data: localesData,
    isLoading: isLoadingLocales,
    error: localesError,
    refetch: refetchLocales,
  } = useListProjectLocalesQuery(
    projectData?.data?.id || '',
    { skip: !projectData?.data?.id }
  );

  // Project update and delete mutations
  const [updateProject] = useUpdateProjectMutation();
  const [deleteProject] = useDeleteProjectMutation();

  // Handle back button click
  const handleBack = () => {
    // Find the organization slug for the selected organization
    const selectedOrg = organizationsData?.data?.find(org => org.id === organizationId);
    if (selectedOrg) {
      router.push(`/organizations/${selectedOrg.slug}`);
    } else {
      router.push('/organizations');
    }
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  // Handle add locale
  const handleAddLocale = () => {
    setIsAddLocaleDialogOpen(true);
  };

  // Handle edit project
  const handleEditProject = () => {
    setIsEditProjectDialogOpen(true);
  };

  // Handle delete project
  const handleDeleteProject = () => {
    setIsDeleteDialogOpen(true);
  };

  // Loading state
  if (isLoadingOrganizations || (organizationId && isLoadingProject)) {
    return (
      <div className="container mx-auto p-4 md:p-8">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="sm" className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <Skeleton className="h-8 w-48" />
        </div>

        <Skeleton className="h-12 w-full mb-6" />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Skeleton className="h-64 col-span-2" />
          <Skeleton className="h-64" />
        </div>
      </div>
    );
  }

  // Error state
  if (projectError) {
    return (
      <div className="container mx-auto p-4 md:p-8">
        <Button variant="ghost" size="sm" onClick={handleBack} className="mb-6">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Projects
        </Button>

        <Card className="w-full max-w-md mx-auto">
          <CardHeader>
            <CardTitle>Error</CardTitle>
            <CardDescription>Failed to load project</CardDescription>
          </CardHeader>
          <CardContent>
            <p>There was an error loading the project. Please try again later.</p>
          </CardContent>
          <CardFooter>
            <Button onClick={handleBack}>Return to Projects</Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  const project = projectData?.data;

  if (!project) {
    return null;
  }

  return (
    <div className="container mx-auto p-4 md:p-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div className="flex items-center">
          <Button variant="ghost" size="sm" onClick={handleBack} className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h1 className="text-3xl font-bold">{project.name}</h1>
        </div>

        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={handleEditProject}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button variant="destructive" size="sm" onClick={handleDeleteProject}>
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {project.description && (
        <p className="text-muted-foreground mb-6">{project.description}</p>
      )}

      <Tabs value={activeTab} onValueChange={handleTabChange} className="mb-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="locales">Locales</TabsTrigger>
          <TabsTrigger value="translations">Translations</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="col-span-2">
              <CardHeader>
                <CardTitle>Project Details</CardTitle>
                <CardDescription>Basic information about your project</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="font-medium">Name</h3>
                    <p>{project.name}</p>
                  </div>
                  <div>
                    <h3 className="font-medium">Slug</h3>
                    <p>{project.slug}</p>
                  </div>
                  <div>
                    <h3 className="font-medium">Default Locale</h3>
                    <p>{project.default_locale || 'Not set'}</p>
                  </div>
                  <div>
                    <h3 className="font-medium">Visibility</h3>
                    <p>{project.is_public ? 'Public' : 'Private'}</p>
                  </div>
                  <div>
                    <h3 className="font-medium">Created</h3>
                    <p>{project.created_at ? formatDate(project.created_at) : 'Unknown'}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Common project tasks</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button className="w-full justify-start" onClick={() => setActiveTab('locales')}>
                  <Globe className="mr-2 h-4 w-4" />
                  Manage Locales
                </Button>
                <Button className="w-full justify-start" onClick={() => setActiveTab('translations')}>
                  <Languages className="mr-2 h-4 w-4" />
                  Manage Translations
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Key className="mr-2 h-4 w-4" />
                  Add Translation Keys
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <FileText className="mr-2 h-4 w-4" />
                  Export Translations
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="locales" className="mt-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Project Locales</h2>
            <Button onClick={handleAddLocale}>
              <Plus className="h-4 w-4 mr-2" />
              Add Locale
            </Button>
          </div>

          {isLoadingLocales ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-32 w-full" />
              ))}
            </div>
          ) : localesError ? (
            <Card>
              <CardHeader>
                <CardTitle>Error</CardTitle>
                <CardDescription>Failed to load locales</CardDescription>
              </CardHeader>
              <CardContent>
                <p>There was an error loading the project locales. Please try again later.</p>
              </CardContent>
              <CardFooter>
                <Button onClick={() => refetchLocales()}>Retry</Button>
              </CardFooter>
            </Card>
          ) : localesData?.data?.length === 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>No Locales</CardTitle>
                <CardDescription>This project doesn't have any locales yet</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Add locales to start translating your content.</p>
              </CardContent>
              <CardFooter>
                <Button onClick={handleAddLocale}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Locale
                </Button>
              </CardFooter>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {localesData?.data?.map((locale) => (
                <Card key={locale.id}>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Globe className="mr-2 h-5 w-5 text-primary" />
                      {locale.locale_name || locale.locale_code || 'Unknown'}
                    </CardTitle>
                    <CardDescription>
                      {locale.locale_native_name || locale.locale_code || 'No native name'}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-sm">
                      <span className="font-medium">Code:</span> {locale.locale_code || 'Unknown'}
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">Direction:</span> {locale.direction || 'LTR'}
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">Source:</span> {locale.is_source ? 'Yes' : 'No'}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="translations" className="mt-6">
          <TranslationsTab projectId={project.id} projectSlug={projectSlug} />
        </TabsContent>

        <TabsContent value="settings" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Project Settings</CardTitle>
              <CardDescription>Configure your project settings</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Project settings will be implemented in the next phase.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add Locale Dialog */}
      <AddLocaleDialog
        open={isAddLocaleDialogOpen}
        onOpenChange={setIsAddLocaleDialogOpen}
        projectId={project.id}
        onSuccess={() => refetchLocales()}
      />

      {/* Edit Project Dialog */}
      <EditProjectDialog
        open={isEditProjectDialogOpen}
        onOpenChange={setIsEditProjectDialogOpen}
        project={project}
        onSuccess={() => refetchProject()}
      />

      {/* Delete Project Dialog */}
      <DeleteProjectDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        projectId={project.id}
        projectName={project.name || ''}
        onSuccess={() => {
          // Find the organization slug for the selected organization
          const selectedOrg = organizationsData?.data?.find(org => org.id === organizationId);
          if (selectedOrg) {
            router.push(`/organizations/${selectedOrg.slug}`);
          } else {
            router.push('/organizations');
          }
        }}
      />
    </div>
  );
}

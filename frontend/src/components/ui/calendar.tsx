import React from 'react';

interface CalendarProps {
  mode?: 'single' | 'multiple' | 'range';
  selected?: Date | Date[] | { from: Date; to: Date };
  onSelect?: (date: Date | Date[] | { from: Date; to: Date } | undefined) => void;
  className?: string;
}

export function Calendar({ mode = 'single', selected, onSelect, className }: CalendarProps) {
  return (
    <div className={`p-4 bg-white border rounded-lg ${className || ''}`}>
      <p className="text-sm text-gray-600">Calendar component coming soon...</p>
      <input
        type="date"
        onChange={(e) => onSelect && onSelect(new Date(e.target.value))}
        className="mt-2 w-full p-2 border rounded"
      />
    </div>
  );
}
import React, { ReactNode } from 'react';

interface PaginationProps {
  children: ReactNode;
  className?: string;
}

interface PaginationContentProps {
  children: ReactNode;
}

interface PaginationItemProps {
  children: ReactNode;
  className?: string;
}

interface PaginationLinkProps {
  href?: string;
  children: ReactNode;
  isActive?: boolean;
}

interface PaginationPreviousProps {
  href?: string;
  children?: ReactNode;
}

interface PaginationNextProps {
  href?: string;
  children?: ReactNode;
}

interface PaginationEllipsisProps {
  className?: string;
}

export function Pagination({ children, className }: PaginationProps) {
  return (
    <nav className={`flex items-center justify-center ${className || ''}`}>
      {children}
    </nav>
  );
}

export function PaginationContent({ children }: PaginationContentProps) {
  return <ul className="flex items-center space-x-1">{children}</ul>;
}

export function PaginationItem({ children, className }: PaginationItemProps) {
  return <li className={className}>{children}</li>;
}

export function PaginationLink({ href, children, isActive }: PaginationLinkProps) {
  return (
    <a
      href={href}
      className={`px-3 py-2 text-sm rounded-md ${
        isActive
          ? 'bg-blue-500 text-white'
          : 'text-gray-700 hover:bg-gray-100 border border-gray-300'
      }`}
    >
      {children}
    </a>
  );
}

export function PaginationPrevious({ href, children }: PaginationPreviousProps) {
  return (
    <a
      href={href}
      className="px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 border border-gray-300 rounded-md"
    >
      {children || 'Previous'}
    </a>
  );
}

export function PaginationNext({ href, children }: PaginationNextProps) {
  return (
    <a
      href={href}
      className="px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 border border-gray-300 rounded-md"
    >
      {children || 'Next'}
    </a>
  );
}

export function PaginationEllipsis({ className }: PaginationEllipsisProps) {
  return <span className={`px-2 ${className || ''}`}>...</span>;
}
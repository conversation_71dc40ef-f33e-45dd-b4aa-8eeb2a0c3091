'use client';

import { useState } from 'react';
import {
  useListProjectTranslationKeysQuery,
  useListTranslationsByKeyQuery,
  useListProjectLocalesQuery,
} from '@/lib/redux/api/endpoints';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Languages,
  FileText,
} from 'lucide-react';
import { TranslationKey } from '@/lib/redux/api/endpoints/translationApi';
import {
  AddTranslationKeyDialog,
  EditTranslationKeyDialog,
  DeleteTranslationKeyDialog,
  TranslationEditorDialog,
  AISettingsDialog,
  LanguageSelector,
  TranslationKeysTable,
  SelectedLanguageStatus
} from '@/components/project/translations';

interface TranslationsTabProps {
  projectId: string;
  projectSlug: string;
}

export function TranslationsTab({ projectId, projectSlug }: TranslationsTabProps) {
  const [selectedKey, setSelectedKey] = useState<string>('');
  const [selectedLocale, setSelectedLocale] = useState<string>('');

  // AI translation provider and model state
  const [aiProvider, setAiProvider] = useState<string>('openai');
  const [aiModel, setAiModel] = useState<string>('gpt-4');
  const [isAiSettingsDialogOpen, setIsAiSettingsDialogOpen] = useState<boolean>(false);

  // Edit Translation Key dialog state
  const [isEditKeyDialogOpen, setIsEditKeyDialogOpen] = useState<boolean>(false);
  const [editingKey, setEditingKey] = useState<TranslationKey | null>(null);

  // Delete Translation Key dialog state
  const [isDeleteKeyDialogOpen, setIsDeleteKeyDialogOpen] = useState<boolean>(false);
  const [deletingKeyId, setDeletingKeyId] = useState<string | null>(null);

  // Editor modal state
  const [isEditorModalOpen, setIsEditorModalOpen] = useState<boolean>(false);

  // Fetch project locales
  const {
    data: localesData,
    isLoading: isLoadingLocales,
    error: localesError
  } = useListProjectLocalesQuery(projectId);

  // Fetch all translation keys for the project
  const {
    data: keysData,
    isLoading: isLoadingKeys,
    error: keysError,
    refetch: refetchKeys
  } = useListProjectTranslationKeysQuery({
    projectSlug,
    resourceId: undefined
  });

  // Fetch translations for the selected key
  const {
    data: translationsData,
    refetch: refetchTranslations
  } = useListTranslationsByKeyQuery(selectedKey, {
    skip: !selectedKey
  });

  // Get the selected key details
  const selectedKeyDetails = keysData?.data?.find(k => k.id === selectedKey);

  // Handle key selection
  const handleKeySelect = (keyId: string) => {
    setSelectedKey(keyId);
    setIsEditorModalOpen(true);
  };

  // Handle edit key
  const handleEditKey = (key: TranslationKey) => {
    setEditingKey(key);
    setSelectedKey(key.id);
    setIsEditKeyDialogOpen(true);
  };

  // Handle delete key
  const handleDeleteKey = (key: TranslationKey) => {
    setDeletingKeyId(key.id);
    setSelectedKey(key.id);
    setIsDeleteKeyDialogOpen(true);
  };

  // Handle success callbacks
  const handleKeyOperationSuccess = () => {
    refetchKeys();
    refetchTranslations();
  };

  const handleTranslationSuccess = () => {
    refetchTranslations();
  };

  // Loading state
  if (isLoadingLocales) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  // Error state
  if (localesError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Error</CardTitle>
          <CardDescription>Failed to load translation resources</CardDescription>
        </CardHeader>
        <CardContent>
          <p>There was an error loading the translation resources. Please try again.</p>
          <Button onClick={() => window.location.reload()} className="mt-4">Retry</Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Translations</h2>
          <p className="text-muted-foreground">Manage your project translations</p>
        </div>
        <div className="flex space-x-2">
          <AddTranslationKeyDialog 
            projectSlug={projectSlug}
            onSuccess={handleKeyOperationSuccess}
          />
        </div>
      </div>

      <Tabs defaultValue="editor" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="editor" className="flex items-center">
            <Languages className="mr-2 h-4 w-4" />
            Languages
          </TabsTrigger>
          <TabsTrigger value="keys" className="flex items-center">
            <FileText className="mr-2 h-4 w-4" />
            Keys
          </TabsTrigger>
        </TabsList>

        {/* Languages Tab */}
        <TabsContent value="editor" className="space-y-6">
          {/* Language Selection */}
          <LanguageSelector
            localesData={localesData}
            selectedLocale={selectedLocale}
            onLocaleSelect={setSelectedLocale}
          />

          {/* Translation Keys List */}
          <TranslationKeysTable
            keysData={keysData}
            isLoadingKeys={isLoadingKeys}
            keysError={keysError}
            selectedLocale={selectedLocale}
            localesData={localesData}
            translationsData={translationsData}
            selectedKey={selectedKey}
            showLanguageColumn={true}
            showActions={true}
            showManagementActions={false}
            onKeySelect={handleKeySelect}
            onRefetch={refetchKeys}
          />

          {/* Selected Language Status */}
          <SelectedLanguageStatus
            selectedLocale={selectedLocale}
            localesData={localesData}
          />
        </TabsContent>

        {/* Keys Management Tab */}
        <TabsContent value="keys" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Keys</CardTitle>
              <CardDescription>Manage all keys in your project</CardDescription>
            </CardHeader>
            <CardContent>
              <TranslationKeysTable
                keysData={keysData}
                isLoadingKeys={isLoadingKeys}
                keysError={keysError}
                selectedLocale={selectedLocale}
                localesData={localesData}
                selectedKey={selectedKey}
                showLanguageColumn={false}
                showActions={false}
                showManagementActions={true}
                onEditKey={handleEditKey}
                onDeleteKey={handleDeleteKey}
                onRefetch={refetchKeys}
              />
            </CardContent>
            <div className="p-4 border-t">
              <AddTranslationKeyDialog 
                projectSlug={projectSlug}
                onSuccess={handleKeyOperationSuccess}
              />
            </div>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Editor Modal */}
      <TranslationEditorDialog
        isOpen={isEditorModalOpen}
        onOpenChange={setIsEditorModalOpen}
        selectedKey={selectedKey}
        selectedLocale={selectedLocale}
        selectedKeyDetails={selectedKeyDetails}
        localesData={localesData}
        aiProvider={aiProvider}
        aiModel={aiModel}
        onSuccess={handleTranslationSuccess}
      />

      {/* Edit Translation Key Dialog */}
      <EditTranslationKeyDialog
        isOpen={isEditKeyDialogOpen}
        onOpenChange={setIsEditKeyDialogOpen}
        translationKey={editingKey}
        onSuccess={handleKeyOperationSuccess}
      />

      {/* Delete Translation Key Dialog */}
      <DeleteTranslationKeyDialog
        isOpen={isDeleteKeyDialogOpen}
        onOpenChange={setIsDeleteKeyDialogOpen}
        translationKeyId={deletingKeyId}
        onSuccess={handleKeyOperationSuccess}
      />

      {/* AI Settings Dialog */}
      <AISettingsDialog
        isOpen={isAiSettingsDialogOpen}
        onOpenChange={setIsAiSettingsDialogOpen}
        aiProvider={aiProvider}
        aiModel={aiModel}
        onProviderChange={setAiProvider}
        onModelChange={setAiModel}
      />
    </div>
  );
}
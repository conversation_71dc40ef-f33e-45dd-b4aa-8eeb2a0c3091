'use client';

import { useState } from 'react';
import {
  useListProjectLocalesQuery,
  useAddProjectLocaleMutation,
  useUpdateProjectLocaleMutation,
} from '@/lib/redux/api/endpoints';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Globe,
  Plus,
  Loader2,
  RefreshCw,
  Check,
  Star,
  StarOff,
  Trash2,
} from 'lucide-react';
import AddLocaleDialog from '@/components/projects/AddLocaleDialog';

interface LocalesTabProps {
  projectId: string;
}

export function LocalesTab({ projectId }: LocalesTabProps) {
  const { toast } = useToast();
  const [isAddLocaleDialogOpen, setIsAddLocaleDialogOpen] = useState<boolean>(false);

  // Fetch project locales
  const {
    data: localesData,
    isLoading: isLoadingLocales,
    error: localesError,
    refetch: refetchLocales
  } = useListProjectLocalesQuery(projectId);

  // Mutation for updating locale (to set as source)
  const [updateProjectLocale, { isLoading: isUpdating }] = useUpdateProjectLocaleMutation();

  // Handle add locale
  const handleAddLocale = () => {
    setIsAddLocaleDialogOpen(true);
  };

  // Handle setting a locale as the source
  const handleSetAsSource = async (localeId: string) => {
    try {
      await updateProjectLocale({
        projectId,
        localeId,
        data: {
          is_source: true
        }
      }).unwrap();

      toast({
        title: "Success",
        description: "Source locale updated successfully",
      });

      refetchLocales();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.message || "Failed to update source locale",
        variant: "destructive"
      });
    }
  };

  // Loading state
  if (isLoadingLocales) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold">Project Locales</h2>
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (localesError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Error</CardTitle>
          <CardDescription>Failed to load project locales</CardDescription>
        </CardHeader>
        <CardContent>
          <p>There was an error loading the project locales. Please try again.</p>
          <Button onClick={() => refetchLocales()} className="mt-4">
            <RefreshCw className="mr-2 h-4 w-4" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Project Locales</h2>
          <p className="text-muted-foreground">Manage your project languages</p>
        </div>
        <Button onClick={handleAddLocale}>
          <Plus className="mr-2 h-4 w-4" />
          Add Locale
        </Button>
      </div>

      {localesData?.data?.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-6">
              <Globe className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No Locales Added</h3>
              <p className="text-muted-foreground mb-4">
                Add locales to your project to start managing translations.
              </p>
              <Button onClick={handleAddLocale}>
                <Plus className="mr-2 h-4 w-4" />
                Add Your First Locale
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Globe className="mr-2 h-5 w-5 text-primary" />
              Available Locales
            </CardTitle>
            <CardDescription>
              {localesData?.data?.length} locale{localesData?.data?.length !== 1 ? 's' : ''} configured for this project
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Locale</TableHead>
                  <TableHead>Code</TableHead>
                  <TableHead>Native Name</TableHead>
                  <TableHead>Direction</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {localesData?.data?.map((locale) => (
                  <TableRow key={locale.id}>
                    <TableCell className="font-medium">{locale.name}</TableCell>
                    <TableCell>{locale.code}</TableCell>
                    <TableCell>{locale.native_name}</TableCell>
                    <TableCell>
                      {locale.direction === 'rtl' ? 'Right to Left' : 'Left to Right'}
                    </TableCell>
                    <TableCell>
                      {locale.is_source ? (
                        <Badge variant="default" className="bg-green-100 text-green-800">
                          <Star className="mr-1 h-3 w-3" />
                          Source
                        </Badge>
                      ) : (
                        <Badge variant="outline">Target</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        {!locale.is_source && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleSetAsSource(locale.id)}
                                  disabled={isUpdating}
                                >
                                  <Star className="h-4 w-4" />
                                  <span className="sr-only">Set as Source</span>
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Set as Source Locale</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Add Locale Dialog */}
      <AddLocaleDialog
        open={isAddLocaleDialogOpen}
        onOpenChange={setIsAddLocaleDialogOpen}
        projectId={projectId}
        onSuccess={() => refetchLocales()}
      />
    </div>
  );
}

'use client';

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Languages } from 'lucide-react';

interface SelectedLanguageStatusProps {
  selectedLocale: string;
  localesData: any;
}

export function SelectedLanguageStatus({ selectedLocale, localesData }: SelectedLanguageStatusProps) {
  const selectedLanguage = localesData?.data?.find((l: any) => l.id === selectedLocale);

  if (!selectedLocale || !selectedLanguage) {
    return null;
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center">
          <Languages className="mr-2 h-5 w-5 text-primary" />
          Selected Language: {selectedLanguage.name} ({selectedLanguage.code})
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-center p-6 bg-primary/5 rounded-md border-2 border-dashed border-primary/20">
          <Languages className="h-12 w-12 text-primary/60 mx-auto mb-3" />
          <h3 className="font-medium mb-2">Ready to Translate</h3>
          <p className="text-sm text-muted-foreground mb-4">
            You've selected <strong>{selectedLanguage.name}</strong> as your target language.
            Click the "Edit" button on any translation key above to start translating.
          </p>
          <div className="text-xs text-muted-foreground">
            📝 Translation progress will be shown here as you work
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
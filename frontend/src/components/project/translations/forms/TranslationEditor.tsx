'use client';

import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface Locale {
  id: string;
  name: string;
  code: string;
  is_source: boolean;
}

interface Translation {
  id: string;
  content: string;
  locale_id: string;
  key_id: string;
  is_reviewed?: boolean;
}

interface TranslationEditorProps {
  sourceLocale: Locale | null;
  targetLocale: Locale | null;
  sourceTranslation: Translation | null;
  targetTranslation: string;
  onTargetTranslationChange: (content: string) => void;
  placeholder?: string;
}

export function TranslationEditor({
  sourceLocale,
  targetLocale,
  sourceTranslation,
  targetTranslation,
  onTargetTranslationChange,
  placeholder = "Enter translation..."
}: TranslationEditorProps) {
  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onTargetTranslationChange(e.target.value);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Source translation */}
      {sourceLocale && (
        <div className="space-y-2">
          <Label className="flex items-center">
            Source: {sourceLocale.name} ({sourceLocale.code})
          </Label>
          <div className="border rounded-md p-3 bg-muted min-h-[120px]">
            {sourceTranslation ? (
              <p>{sourceTranslation.content}</p>
            ) : (
              <p className="text-muted-foreground italic">No source translation available</p>
            )}
          </div>
        </div>
      )}

      {/* Target translation */}
      {targetLocale && (
        <div className="space-y-2">
          <Label className="flex items-center">
            Target: {targetLocale.name} ({targetLocale.code})
          </Label>
          <Textarea
            value={targetTranslation}
            onChange={handleContentChange}
            placeholder={placeholder}
            className="min-h-[120px]"
          />
        </div>
      )}
    </div>
  );
}
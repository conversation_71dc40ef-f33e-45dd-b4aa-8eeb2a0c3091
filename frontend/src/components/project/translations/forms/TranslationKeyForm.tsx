'use client';

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { ImageUploader } from '@/components/shared/ImageUploader';

interface TranslationKeyFormData {
  key_name: string;
  description: string;
  context: string;
  image_url: string;
}

interface TranslationKeyFormProps {
  initialData?: Partial<TranslationKeyFormData>;
  onChange: (data: TranslationKeyFormData) => void;
  mode: 'create' | 'edit';
}

export function TranslationKeyForm({
  initialData = {},
  onChange,
  mode
}: TranslationKeyFormProps) {
  const [formData, setFormData] = useState<TranslationKeyFormData>({
    key_name: '',
    description: '',
    context: '',
    image_url: '',
    ...initialData
  });

  // Update form data when initialData changes
  useEffect(() => {
    setFormData({
      key_name: '',
      description: '',
      context: '',
      image_url: '',
      ...initialData
    });
  }, [initialData]);

  // Notify parent of changes
  useEffect(() => {
    onChange(formData);
  }, [formData, onChange]);

  const handleInputChange = (field: keyof TranslationKeyFormData) => (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: e.target.value
    }));
  };

  const handleImageUpload = (imageUrl: string) => {
    setFormData(prev => ({
      ...prev,
      image_url: imageUrl
    }));
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="keyName">Key Name *</Label>
        <Input
          id="keyName"
          value={formData.key_name}
          onChange={handleInputChange('key_name')}
          placeholder="Enter key name (e.g. common.buttons.save)"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Input
          id="description"
          value={formData.description}
          onChange={handleInputChange('description')}
          placeholder="Enter description (optional)"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="context">Context</Label>
        <Textarea
          id="context"
          value={formData.context}
          onChange={handleInputChange('context')}
          placeholder="Enter context for translators (optional)"
          className="min-h-[80px]"
        />
      </div>

      <ImageUploader
        onImageUploaded={handleImageUpload}
        className="space-y-2"
      />

      {formData.image_url && (
        <div className="mt-4">
          <Label className="mb-2 block">Current Screenshot</Label>
          <div className="border rounded-md overflow-hidden">
            <img
              src={formData.image_url}
              alt="Translation context screenshot"
              className="w-full h-auto max-h-[200px] object-contain"
            />
          </div>
        </div>
      )}
    </div>
  );
}
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Plus, Loader2 } from 'lucide-react';
import { ImageUploader } from '@/components/shared/ImageUploader';
import { useCreateTranslationKeyMutation } from '@/lib/redux/api/endpoints';
import { useToast } from '@/components/ui/use-toast';

interface AddTranslationKeyDialogProps {
  projectSlug: string;
  onSuccess?: () => void;
}

export function AddTranslationKeyDialog({ projectSlug, onSuccess }: AddTranslationKeyDialogProps) {
  const { toast } = useToast();
  const [createTranslationKey, { isLoading: isCreatingKey }] = useCreateTranslationKeyMutation();
  
  const [isOpen, setIsOpen] = useState(false);
  const [newKeyName, setNewKeyName] = useState('');
  const [newKeyDescription, setNewKeyDescription] = useState('');
  const [newKeyContext, setNewKeyContext] = useState('');
  const [newKeyImageUrl, setNewKeyImageUrl] = useState('');

  const handleCreateTranslationKey = async () => {
    if (!newKeyName.trim()) {
      toast({
        title: "Error",
        description: "Please enter a key name",
        variant: "destructive"
      });
      return;
    }

    try {
      await createTranslationKey({
        projectSlug,
        key: {
          key_name: newKeyName.trim(),
          description: newKeyDescription.trim() || undefined,
          context: newKeyContext.trim() || undefined,
          image_url: newKeyImageUrl.trim() || undefined,
          screenshot_url: newKeyImageUrl.trim() || undefined
        }
      }).unwrap();

      // Reset form and close dialog
      setNewKeyName('');
      setNewKeyDescription('');
      setNewKeyContext('');
      setNewKeyImageUrl('');
      setIsOpen(false);

      toast({
        title: "Success",
        description: "Translation key created successfully",
      });

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create translation key",
        variant: "destructive"
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Add Translation Key
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add Translation Key</DialogTitle>
          <DialogDescription>
            Create a new translation key for your project
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="keyName">Key Name *</Label>
            <Input
              id="keyName"
              value={newKeyName}
              onChange={(e) => setNewKeyName(e.target.value)}
              placeholder="Enter key name (e.g. common.buttons.save)"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Input
              id="description"
              value={newKeyDescription}
              onChange={(e) => setNewKeyDescription(e.target.value)}
              placeholder="Enter description (optional)"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="context">Context</Label>
            <Textarea
              id="context"
              value={newKeyContext}
              onChange={(e) => setNewKeyContext(e.target.value)}
              placeholder="Enter context for translators (optional)"
              className="min-h-[80px]"
            />
          </div>

          <ImageUploader
            onImageUploaded={(imageUrl) => setNewKeyImageUrl(imageUrl)}
            className="space-y-2"
          />

          {newKeyImageUrl && (
            <div className="mt-4">
              <Label className="mb-2 block">Current Screenshot</Label>
              <div className="border rounded-md overflow-hidden">
                <img
                  src={newKeyImageUrl}
                  alt="Translation context screenshot"
                  className="w-full h-auto max-h-[200px] object-contain"
                />
              </div>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button
            type="submit"
            onClick={handleCreateTranslationKey}
            disabled={isCreatingKey || !newKeyName.trim()}
          >
            {isCreatingKey ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating...
              </>
            ) : (
              'Create Key'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
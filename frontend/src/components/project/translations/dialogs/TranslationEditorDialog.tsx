'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Skeleton } from '@/components/ui/skeleton';
import { Languages, Save, Loader2, Sparkles, RefreshCw } from 'lucide-react';
import { 
  useListTranslationsByKeyQuery,
  useUpdateTranslationMutation,
  useCreateTranslationMutation,
  useAiTranslateMutation
} from '@/lib/redux/api/endpoints';
import { useToast } from '@/components/ui/use-toast';
import { TranslationKey } from '@/lib/redux/api/endpoints/translationApi';

interface TranslationEditorDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  selectedKey: string;
  selectedLocale: string;
  selectedKeyDetails: TranslationKey | undefined;
  localesData: any;
  aiProvider: string;
  aiModel: string;
  onSuccess?: () => void;
}

export function TranslationEditorDialog({
  isOpen,
  onOpenChange,
  selectedKey,
  selectedLocale,
  selectedKeyDetails,
  localesData,
  aiProvider,
  aiModel,
  onSuccess
}: TranslationEditorDialogProps) {
  const { toast } = useToast();
  const [translationContent, setTranslationContent] = useState('');
  const [isEditing, setIsEditing] = useState(false);

  // Fetch translations for the selected key
  const {
    data: translationsData,
    isLoading: isLoadingTranslations,
    error: translationsError,
    refetch: refetchTranslations
  } = useListTranslationsByKeyQuery(selectedKey, {
    skip: !selectedKey
  });

  // Mutations
  const [updateTranslation, { isLoading: isUpdating }] = useUpdateTranslationMutation();
  const [createTranslation, { isLoading: isCreating }] = useCreateTranslationMutation();
  const [aiTranslate, { isLoading: isTranslating }] = useAiTranslateMutation();

  // Find the current translation for the selected locale
  const currentTranslation = translationsData?.data?.find(
    t => t.locale_id === selectedLocale
  );

  // Find the source translation
  const sourceLocale = localesData?.data?.find((locale: any) => locale.is_source);
  const sourceTranslation = translationsData?.data?.find(
    t => sourceLocale && t.locale_id === sourceLocale.id
  );

  // Set translation content when a translation is selected
  useEffect(() => {
    if (currentTranslation) {
      setTranslationContent(currentTranslation.content);
      setIsEditing(true);
    } else {
      setTranslationContent('');
      setIsEditing(false);
    }
  }, [currentTranslation]);

  // Handle translation content change
  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTranslationContent(e.target.value);
  };

  // Handle save translation
  const handleSaveTranslation = async () => {
    if (!selectedKey || !selectedLocale || !translationContent.trim()) {
      toast({
        title: "Error",
        description: "Please select a key, locale, and enter translation content",
        variant: "destructive"
      });
      return;
    }

    try {
      if (currentTranslation) {
        // Update existing translation
        await updateTranslation({
          id: currentTranslation.id,
          data: {
            content: translationContent,
            is_reviewed: true
          }
        }).unwrap();

        toast({
          title: "Success",
          description: "Translation updated successfully",
        });
      } else {
        // Create new translation
        await createTranslation({
          key_id: selectedKey,
          locale_id: selectedLocale,
          content: translationContent
        }).unwrap();

        toast({
          title: "Success",
          description: "Translation created successfully",
        });
      }

      refetchTranslations();
      
      // Close modal after successful save
      onOpenChange(false);
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save translation",
        variant: "destructive"
      });
    }
  };

  // Handle AI translation
  const handleAiTranslate = async () => {
    if (!sourceLocale) {
      toast({
        title: "Error",
        description: "No source locale found. Please set a source locale first.",
        variant: "destructive"
      });
      return;
    }

    if (!sourceTranslation) {
      toast({
        title: "Error",
        description: "No source translation found. Please add a translation in the source locale first.",
        variant: "destructive"
      });
      return;
    }

    // Get target locale code
    const targetLocale = localesData?.data?.find((locale: any) => locale.id === selectedLocale);

    if (!targetLocale || !targetLocale.code) {
      toast({
        title: "Error",
        description: "Target locale information is incomplete.",
        variant: "destructive"
      });
      return;
    }

    try {
      const result = await aiTranslate({
        text: sourceTranslation.content,
        source_locale: sourceLocale.code || 'en',
        target_locale: targetLocale.code,
        context: selectedKeyDetails?.context,
        provider: aiProvider,
        model: aiModel
      }).unwrap();

      setTranslationContent(result.data.translated_text);

      toast({
        title: "AI Translation Generated",
        description: `Used ${result.data.credits_used} credits. ${result.data.credits_remaining} credits remaining.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate AI translation",
        variant: "destructive"
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Languages className="mr-2 h-5 w-5 text-primary" />
            Editor - {selectedKeyDetails?.key_name}
          </DialogTitle>
          <DialogDescription>
            {selectedKeyDetails?.description && selectedKeyDetails.description}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Display image if available */}
          {(selectedKeyDetails?.image_url || selectedKeyDetails?.screenshot_url) && (
            <div>
              <Label className="mb-2 block">Screenshot Reference</Label>
              <div className="border rounded-md overflow-hidden">
                <img
                  src={selectedKeyDetails?.image_url || selectedKeyDetails?.screenshot_url}
                  alt="Translation context screenshot"
                  className="w-full h-auto max-h-[200px] object-contain"
                />
              </div>
            </div>
          )}

          {/* Context information */}
          {selectedKeyDetails?.context && (
            <div className="p-3 bg-muted rounded-md">
              <Label className="mb-1 block font-medium">Context for Translators:</Label>
              <p className="text-sm">{selectedKeyDetails.context}</p>
            </div>
          )}

          {/* Show currently selected language */}
          {selectedLocale && (
            <div className="p-3 bg-primary/10 rounded-md">
              <Label className="mb-1 block font-medium">Currently Selected Language:</Label>
              <p className="text-sm font-medium">
                {localesData?.data?.find((l: any) => l.id === selectedLocale)?.name} 
                ({localesData?.data?.find((l: any) => l.id === selectedLocale)?.code})
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                Change language selection in the Languages section above to translate to a different language.
              </p>
            </div>
          )}

          {isLoadingTranslations ? (
            <Skeleton className="h-32 w-full" />
          ) : translationsError ? (
            <div>
              <p className="text-destructive">Failed to load translations</p>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={() => refetchTranslations()}
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Retry
              </Button>
            </div>
          ) : !selectedLocale ? (
            <div className="text-center text-muted-foreground p-6 bg-muted rounded-md">
              <p className="mb-2">No target language selected</p>
              <p className="text-sm">Please select a language from the Languages section above to start translating</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Source translation */}
              {sourceLocale && (
                <div className="space-y-2">
                  <Label className="flex items-center">
                    Source: {sourceLocale.name} ({sourceLocale.code})
                  </Label>
                  <div className="border rounded-md p-3 bg-muted min-h-[120px]">
                    {sourceTranslation ? (
                      <p>{sourceTranslation.content}</p>
                    ) : (
                      <p className="text-muted-foreground italic">No source translation available</p>
                    )}
                  </div>
                </div>
              )}

              {/* Target translation */}
              <div className="space-y-2">
                <Label className="flex items-center">
                  Target: {localesData?.data?.find((l: any) => l.id === selectedLocale)?.name}
                  ({localesData?.data?.find((l: any) => l.id === selectedLocale)?.code})
                </Label>
                <Textarea
                  value={translationContent}
                  onChange={handleContentChange}
                  placeholder="Enter translation..."
                  className="min-h-[120px]"
                />
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <div className="flex space-x-2 w-full">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            
            {selectedLocale && sourceTranslation && (
              <Button
                variant="outline"
                onClick={handleAiTranslate}
                disabled={isTranslating}
              >
                {isTranslating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Translating...
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-4 w-4" />
                    AI Translate
                  </>
                )}
              </Button>
            )}

            <Button
              onClick={handleSaveTranslation}
              disabled={isUpdating || isCreating || !translationContent.trim() || !selectedLocale}
            >
              {(isUpdating || isCreating) ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  {isEditing ? 'Update' : 'Create'} Translation
                </>
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
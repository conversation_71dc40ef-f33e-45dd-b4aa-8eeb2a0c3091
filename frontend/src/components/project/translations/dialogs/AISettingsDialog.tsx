'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { AIProviderSelector } from '@/components/translation/AIProviderSelector';

interface AISettingsDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  aiProvider: string;
  aiModel: string;
  onProviderChange: (provider: string) => void;
  onModelChange: (model: string) => void;
}

export function AISettingsDialog({ 
  isOpen, 
  onOpenChange, 
  aiProvider, 
  aiModel, 
  onProviderChange, 
  onModelChange 
}: AISettingsDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>AI Translation Settings</DialogTitle>
          <DialogDescription>
            Configure the AI provider and model for translations
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <AIProviderSelector
            onProviderChange={onProviderChange}
            onModelChange={onModelChange}
            defaultProvider={aiProvider}
            defaultModel={aiModel}
          />
        </div>
        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>
            Save Settings
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
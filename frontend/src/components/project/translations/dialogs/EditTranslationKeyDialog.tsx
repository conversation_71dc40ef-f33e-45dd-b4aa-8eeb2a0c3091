'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Loader2 } from 'lucide-react';
import { ImageUploader } from '@/components/shared/ImageUploader';
import { useUpdateTranslationKeyMutation } from '@/lib/redux/api/endpoints';
import { useToast } from '@/components/ui/use-toast';
import { TranslationKey } from '@/lib/redux/api/endpoints/translationApi';

interface EditTranslationKeyDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  translationKey: TranslationKey | null;
  onSuccess?: () => void;
}

export function EditTranslationKeyDialog({ 
  isOpen, 
  onOpenChange, 
  translationKey, 
  onSuccess 
}: EditTranslationKeyDialogProps) {
  const { toast } = useToast();
  const [updateTranslationKey, { isLoading: isUpdatingKey }] = useUpdateTranslationKeyMutation();
  
  const [editKeyName, setEditKeyName] = useState('');
  const [editKeyDescription, setEditKeyDescription] = useState('');
  const [editKeyContext, setEditKeyContext] = useState('');
  const [editKeyImageUrl, setEditKeyImageUrl] = useState('');

  // Initialize form data when translationKey changes
  useEffect(() => {
    if (translationKey) {
      setEditKeyName(translationKey.key_name);
      setEditKeyDescription(translationKey.description || '');
      setEditKeyContext(translationKey.context || '');
      setEditKeyImageUrl(translationKey.image_url || translationKey.screenshot_url || '');
    }
  }, [translationKey]);

  const handleUpdateTranslationKey = async () => {
    if (!translationKey?.id || !editKeyName.trim()) {
      toast({
        title: "Error",
        description: "Please enter a key name",
        variant: "destructive"
      });
      return;
    }

    try {
      await updateTranslationKey({
        id: translationKey.id,
        data: {
          key_name: editKeyName.trim(),
          description: editKeyDescription.trim() || undefined,
          context: editKeyContext.trim() || undefined,
          image_url: editKeyImageUrl.trim() || undefined,
          screenshot_url: editKeyImageUrl.trim() || undefined
        }
      }).unwrap();

      onOpenChange(false);

      toast({
        title: "Success",
        description: "Translation key updated successfully",
      });

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update translation key",
        variant: "destructive"
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Translation Key</DialogTitle>
          <DialogDescription>
            Update the translation key details
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="editKeyName">Key Name *</Label>
            <Input
              id="editKeyName"
              value={editKeyName}
              onChange={(e) => setEditKeyName(e.target.value)}
              placeholder="Enter key name (e.g. common.buttons.save)"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="editDescription">Description</Label>
            <Input
              id="editDescription"
              value={editKeyDescription}
              onChange={(e) => setEditKeyDescription(e.target.value)}
              placeholder="Enter description (optional)"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="editContext">Context</Label>
            <Textarea
              id="editContext"
              value={editKeyContext}
              onChange={(e) => setEditKeyContext(e.target.value)}
              placeholder="Enter context for translators (optional)"
              className="min-h-[80px]"
            />
          </div>

          <ImageUploader
            onImageUploaded={(imageUrl) => setEditKeyImageUrl(imageUrl)}
            className="space-y-2"
          />

          {editKeyImageUrl && (
            <div className="mt-4">
              <Label className="mb-2 block">Current Screenshot</Label>
              <div className="border rounded-md overflow-hidden">
                <img
                  src={editKeyImageUrl}
                  alt="Translation context screenshot"
                  className="w-full h-auto max-h-[200px] object-contain"
                />
              </div>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button
            type="submit"
            onClick={handleUpdateTranslationKey}
            disabled={isUpdatingKey || !editKeyName.trim()}
          >
            {isUpdatingKey ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Updating...
              </>
            ) : (
              'Update Key'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
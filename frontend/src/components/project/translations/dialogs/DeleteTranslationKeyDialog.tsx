'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Loader2, AlertCircle } from 'lucide-react';
import { useDeleteTranslationKeyMutation } from '@/lib/redux/api/endpoints';
import { useToast } from '@/components/ui/use-toast';

interface DeleteTranslationKeyDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  translationKeyId: string | null;
  onSuccess?: () => void;
}

export function DeleteTranslationKeyDialog({ 
  isOpen, 
  onOpenChange, 
  translationKeyId, 
  onSuccess 
}: DeleteTranslationKeyDialogProps) {
  const { toast } = useToast();
  const [deleteTranslationKey, { isLoading: isDeletingKey }] = useDeleteTranslationKeyMutation();

  const handleDeleteTranslationKey = async () => {
    if (!translationKeyId) {
      return;
    }

    try {
      await deleteTranslationKey(translationKeyId).unwrap();

      onOpenChange(false);

      toast({
        title: "Success",
        description: "Translation key deleted successfully",
      });

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete translation key",
        variant: "destructive"
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete Translation Key</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete this translation key? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <div className="flex items-center p-4 border rounded-md bg-muted">
            <AlertCircle className="h-5 w-5 text-destructive mr-2" />
            <p className="text-sm">All translations associated with this key will also be deleted.</p>
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDeleteTranslationKey}
            disabled={isDeletingKey}
          >
            {isDeletingKey ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              'Delete Key'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
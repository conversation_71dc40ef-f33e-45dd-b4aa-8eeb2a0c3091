# Translation Components

This directory contains extracted components from the original `TranslationsTab.tsx` to improve maintainability and follow the single responsibility principle.

## Component Structure

```
translations/
├── dialogs/
│   ├── AddTranslationKeyDialog.tsx      # Dialog for creating new translation keys
│   ├── EditTranslationKeyDialog.tsx     # Dialog for editing existing keys
│   ├── DeleteTranslationKeyDialog.tsx   # Confirmation dialog for key deletion
│   ├── TranslationEditorDialog.tsx      # Main translation editing interface
│   ├── AISettingsDialog.tsx             # AI provider configuration dialog
│   └── index.ts                         # Export barrel
├── tables/
│   ├── LanguageSelector.tsx             # Language selection interface
│   ├── TranslationKeysTable.tsx         # Reusable table for displaying keys
│   └── index.ts                         # Export barrel
├── forms/
│   ├── TranslationKeyForm.tsx           # Reusable form for key creation/editing
│   ├── TranslationEditor.tsx            # Side-by-side translation editing
│   └── index.ts                         # Export barrel
├── status/
│   ├── SelectedLanguageStatus.tsx       # Shows current language status
│   └── index.ts                         # Export barrel
└── index.ts                             # Main export barrel
```

## Refactoring Results

- **Original file**: 1,399 lines
- **Refactored main file**: 511 lines (63% reduction)
- **Extracted components**: 1,266 lines across 8 components
- **Total lines**: Slightly decreased due to removed duplication

## Benefits

1. **Single Responsibility**: Each component has one clear purpose
2. **Reusability**: Components can be reused across the app
3. **Testability**: Smaller components are easier to test
4. **Maintainability**: Easier to modify specific features
5. **Code Organization**: Logical grouping of related functionality
6. **Performance**: Components can be optimized individually

## Usage

Import components from the main index:

```typescript
import {
  AddTranslationKeyDialog,
  EditTranslationKeyDialog,
  DeleteTranslationKeyDialog,
  TranslationEditorDialog,
  AISettingsDialog,
  LanguageSelector,
  TranslationKeysTable,
  SelectedLanguageStatus
} from './translations';
```

## Component Dependencies

All components follow the established patterns:
- Use shadcn/ui components
- Handle loading and error states
- Accept proper TypeScript interfaces
- Follow the project's naming conventions
- Use consistent prop patterns
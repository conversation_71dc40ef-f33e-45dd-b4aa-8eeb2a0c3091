'use client';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface LanguageSelectorProps {
  localesData: any;
  selectedLocale: string;
  onLocaleSelect: (localeId: string) => void;
}

export function LanguageSelector({ localesData, selectedLocale, onLocaleSelect }: LanguageSelectorProps) {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Languages</CardTitle>
      </CardHeader>
      <CardContent>
        <TooltipProvider>
          <div className="flex flex-wrap gap-2">
            {localesData?.data?.map((locale: any) => (
              <Tooltip key={locale.id}>
                <TooltipTrigger asChild>
                  <Button
                    variant={locale.is_source ? "secondary" : selectedLocale === locale.id ? "default" : "outline"}
                    onClick={() => !locale.is_source && onLocaleSelect(locale.id)}
                    disabled={locale.is_source}
                    className="justify-center"
                  >
                    {locale.code}
                    {locale.is_source && " (source)"}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{locale.name} {locale.is_source ? "(Source Language)" : ""}</p>
                </TooltipContent>
              </Tooltip>
            ))}
          </div>
        </TooltipProvider>
      </CardContent>
    </Card>
  );
}
'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Search, RefreshCw, ChevronRight, ChevronLeft, Pencil, Trash2, Check, FileText } from 'lucide-react';
import { TranslationKey } from '@/lib/redux/api/endpoints/translationApi';

interface TranslationKeysTableProps {
  keysData: any;
  isLoadingKeys: boolean;
  keysError: any;
  selectedLocale: string;
  localesData: any;
  translationsData?: any;
  selectedKey?: string;
  showLanguageColumn?: boolean;
  showActions?: boolean;
  showManagementActions?: boolean;
  onKeySelect?: (keyId: string) => void;
  onEditKey?: (key: TranslationKey) => void;
  onDeleteKey?: (key: TranslationKey) => void;
  onRefetch?: () => void;
}

export function TranslationKeysTable({
  keysData,
  isLoadingKeys,
  keysError,
  selectedLocale,
  localesData,
  translationsData,
  selectedKey,
  showLanguageColumn = true,
  showActions = true,
  showManagementActions = false,
  onKeySelect,
  onEditKey,
  onDeleteKey,
  onRefetch
}: TranslationKeysTableProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [currentKeyIndex, setCurrentKeyIndex] = useState(0);

  // Filter keys by search query
  const filteredKeys = keysData?.data?.filter((key: TranslationKey) =>
    key.key_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (key.description && key.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Handle search input
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Navigate to previous key
  const handlePreviousKey = () => {
    if (filteredKeys && currentKeyIndex > 0) {
      const prevIndex = currentKeyIndex - 1;
      setCurrentKeyIndex(prevIndex);
      if (onKeySelect) {
        onKeySelect(filteredKeys[prevIndex].id);
      }
    }
  };

  // Navigate to next key
  const handleNextKey = () => {
    if (filteredKeys && currentKeyIndex < filteredKeys.length - 1) {
      const nextIndex = currentKeyIndex + 1;
      setCurrentKeyIndex(nextIndex);
      if (onKeySelect) {
        onKeySelect(filteredKeys[nextIndex].id);
      }
    }
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center">
          <FileText className="mr-2 h-5 w-5 text-primary" />
          Keys
          {selectedLocale && showLanguageColumn && (
            <span className="ml-2 text-sm font-normal text-muted-foreground">
              showing {localesData?.data?.find((l: any) => l.id === selectedLocale)?.code} translations
            </span>
          )}
        </CardTitle>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search keys..."
            value={searchQuery}
            onChange={handleSearchChange}
            className="pl-10"
          />
        </div>
      </CardHeader>
      <CardContent className="p-0">
        {isLoadingKeys ? (
          <div className="p-4 space-y-2">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
          </div>
        ) : keysError ? (
          <div className="p-4">
            <p className="text-destructive">Failed to load translation keys</p>
            {onRefetch && (
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={onRefetch}
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Retry
              </Button>
            )}
          </div>
        ) : keysData?.data?.length === 0 ? (
          <div className="p-4 text-center text-muted-foreground">
            No translation keys found. Click "Add Translation Key" to create one.
          </div>
        ) : filteredKeys?.length === 0 ? (
          <div className="p-4 text-center text-muted-foreground">
            No translation keys found matching your search
          </div>
        ) : (
          <div className="max-h-[300px] overflow-y-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Key</TableHead>
                  <TableHead>Description</TableHead>
                  {showLanguageColumn && selectedLocale && (
                    <TableHead>
                      Translation ({localesData?.data?.find((l: any) => l.id === selectedLocale)?.code})
                    </TableHead>
                  )}
                  {showManagementActions && <TableHead>Context</TableHead>}
                  <TableHead>Image</TableHead>
                  {(showActions || showManagementActions) && <TableHead className="text-right">Actions</TableHead>}
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredKeys?.map((key: TranslationKey) => {
                  // Find translation for this key in the selected language
                  const translation = selectedLocale && translationsData?.data?.find(
                    (t: any) => t.key_id === key.id && t.locale_id === selectedLocale
                  );
                  
                  return (
                    <TableRow key={key.id}>
                      <TableCell className="font-medium">{key.key_name}</TableCell>
                      <TableCell>{key.description || '-'}</TableCell>
                      {showLanguageColumn && selectedLocale && (
                        <TableCell>
                          {translation ? (
                            <div className="max-w-xs">
                              <p className="text-sm truncate" title={translation.content}>
                                {translation.content}
                              </p>
                              {translation.is_reviewed && (
                                <Badge variant="secondary" className="text-xs mt-1">
                                  <Check className="w-3 h-3 mr-1" />
                                  Reviewed
                                </Badge>
                              )}
                            </div>
                          ) : selectedKey === key.id ? (
                            <span className="text-muted-foreground text-sm italic">
                              Not translated
                            </span>
                          ) : (
                            <span className="text-muted-foreground text-xs">
                              Click Edit to view
                            </span>
                          )}
                        </TableCell>
                      )}
                      {showManagementActions && (
                        <TableCell>{key.context ? (
                          <span className="text-sm">{key.context.substring(0, 50)}{key.context.length > 50 ? '...' : ''}</span>
                        ) : '-'}</TableCell>
                      )}
                      <TableCell>
                        {key.image_url || key.screenshot_url ? (
                          <Badge variant="secondary" className="bg-green-100 text-green-800">
                            <img src="/icons/image.svg" alt="Has image" className="w-4 h-4 mr-1" />
                            Available
                          </Badge>
                        ) : (
                          <span className="text-muted-foreground text-sm">None</span>
                        )}
                      </TableCell>
                      {(showActions || showManagementActions) && (
                        <TableCell className="text-right">
                          {showActions && onKeySelect && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => onKeySelect(key.id)}
                            >
                              <Pencil className="h-4 w-4 mr-1" />
                              Edit
                            </Button>
                          )}
                          {showManagementActions && (
                            <div className="flex justify-end space-x-2">
                              {onEditKey && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => onEditKey(key)}
                                  title="Edit Translation Key"
                                >
                                  <Pencil className="h-4 w-4" />
                                </Button>
                              )}
                              {onDeleteKey && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => onDeleteKey(key)}
                                  title="Delete Translation Key"
                                >
                                  <Trash2 className="h-4 w-4 text-destructive" />
                                </Button>
                              )}
                            </div>
                          )}
                        </TableCell>
                      )}
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
      {filteredKeys && filteredKeys.length > 0 && showActions && (
        <div className="flex justify-between items-center p-4 border-t">
          <div className="text-sm text-muted-foreground">
            {filteredKeys.length} keys found
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePreviousKey}
              disabled={!filteredKeys || currentKeyIndex <= 0}
            >
              <ChevronLeft className="h-4 w-4 mr-1" />
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleNextKey}
              disabled={!filteredKeys || currentKeyIndex >= filteredKeys.length - 1}
            >
              Next
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </div>
      )}
    </Card>
  );
}
'use client';

import { useState, useEffect } from 'react';
import {
  useListProjectTranslationKeysQuery,
  useListTranslationsByKeyQuery,
  useUpdateTranslationMutation,
  useCreateTranslationMutation,
  useCreateTranslationKeyMutation,
  useUpdateTranslationKeyMutation,
  useDeleteTranslationKeyMutation,
  useAiTranslateMutation,
  useListProjectLocalesQuery,
} from '@/lib/redux/api/endpoints';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Languages, FileText, Plus } from 'lucide-react';

// Import extracted components
import {
  AddTranslationKeyDialog,
  EditTranslationKeyDialog,
  DeleteTranslationKeyDialog,
  TranslationEditorDialog,
  AISettingsDialog,
  LanguageSelector,
  TranslationKeysTable,
  SelectedLanguageStatus
} from './translations';

interface TranslationsTabProps {
  projectId: string;
  projectSlug: string;
}

export function TranslationsTab({ projectId, projectSlug }: TranslationsTabProps) {
  const { toast } = useToast();
  
  // State management
  const [selectedKey, setSelectedKey] = useState<string>('');
  const [selectedLocale, setSelectedLocale] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [translationContent, setTranslationContent] = useState<string>('');
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [currentKeyIndex, setCurrentKeyIndex] = useState<number>(0);

  // AI translation state
  const [aiProvider, setAiProvider] = useState<string>('openai');
  const [aiModel, setAiModel] = useState<string>('gpt-4');
  const [isAiSettingsDialogOpen, setIsAiSettingsDialogOpen] = useState<boolean>(false);

  // Dialog states
  const [isAddKeyDialogOpen, setIsAddKeyDialogOpen] = useState<boolean>(false);
  const [isEditKeyDialogOpen, setIsEditKeyDialogOpen] = useState<boolean>(false);
  const [isDeleteKeyDialogOpen, setIsDeleteKeyDialogOpen] = useState<boolean>(false);
  const [isEditorModalOpen, setIsEditorModalOpen] = useState<boolean>(false);
  const [editingKey, setEditingKey] = useState<any>(null);

  // API calls
  const {
    data: localesData,
    isLoading: isLoadingLocales,
    error: localesError
  } = useListProjectLocalesQuery(projectId);

  const {
    data: keysData,
    isLoading: isLoadingKeys,
    error: keysError,
    refetch: refetchKeys
  } = useListProjectTranslationKeysQuery({
    projectSlug,
    resourceId: undefined
  });

  const {
    data: translationsData,
    isLoading: isLoadingTranslations,
    error: translationsError,
    refetch: refetchTranslations
  } = useListTranslationsByKeyQuery(selectedKey, {
    skip: !selectedKey
  });

  // Mutations
  const [updateTranslation, { isLoading: isUpdating }] = useUpdateTranslationMutation();
  const [createTranslation, { isLoading: isCreating }] = useCreateTranslationMutation();
  const [createTranslationKey, { isLoading: isCreatingKey }] = useCreateTranslationKeyMutation();
  const [updateTranslationKey, { isLoading: isUpdatingKey }] = useUpdateTranslationKeyMutation();
  const [deleteTranslationKey, { isLoading: isDeletingKey }] = useDeleteTranslationKeyMutation();
  const [aiTranslate, { isLoading: isTranslating }] = useAiTranslateMutation();

  // Computed values
  const filteredKeys = keysData?.data?.filter(key =>
    key.key_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (key.description && key.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const currentTranslation = translationsData?.data?.find(
    t => t.locale_id === selectedLocale
  );

  const sourceLocale = localesData?.data?.find(locale => locale.is_source);
  const sourceTranslation = translationsData?.data?.find(
    t => sourceLocale && t.locale_id === sourceLocale.id
  );

  const selectedKeyDetails = keysData?.data?.find(k => k.id === selectedKey);
  const targetLocale = localesData?.data?.find(l => l.id === selectedLocale);

  // Effects
  useEffect(() => {
    if (currentTranslation) {
      setTranslationContent(currentTranslation.content);
      setIsEditing(true);
    } else {
      setTranslationContent('');
      setIsEditing(false);
    }
  }, [currentTranslation]);

  useEffect(() => {
    if (filteredKeys && selectedKey) {
      const index = filteredKeys.findIndex(key => key.id === selectedKey);
      if (index !== -1) {
        setCurrentKeyIndex(index);
      }
    }
  }, [filteredKeys, selectedKey]);

  // Event handlers
  const handleKeySelect = (keyId: string) => {
    setSelectedKey(keyId);
    setIsEditorModalOpen(true);
  };

  const handlePreviousKey = () => {
    if (filteredKeys && currentKeyIndex > 0) {
      const prevIndex = currentKeyIndex - 1;
      setSelectedKey(filteredKeys[prevIndex].id);
      setCurrentKeyIndex(prevIndex);
    }
  };

  const handleNextKey = () => {
    if (filteredKeys && currentKeyIndex < filteredKeys.length - 1) {
      const nextIndex = currentKeyIndex + 1;
      setSelectedKey(filteredKeys[nextIndex].id);
      setCurrentKeyIndex(nextIndex);
    }
  };

  const handleCreateTranslationKey = async (keyData: {
    key_name: string;
    description?: string;
    context?: string;
    image_url?: string;
  }) => {
    try {
      await createTranslationKey({
        projectSlug,
        key: {
          key_name: keyData.key_name,
          description: keyData.description,
          context: keyData.context,
          image_url: keyData.image_url,
          screenshot_url: keyData.image_url
        }
      }).unwrap();

      refetchKeys();
      toast({
        title: "Success",
        description: "Translation key created successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create translation key",
        variant: "destructive"
      });
      throw error;
    }
  };

  const handleUpdateTranslationKey = async (keyId: string, keyData: {
    key_name: string;
    description?: string;
    context?: string;
    image_url?: string;
  }) => {
    try {
      await updateTranslationKey({
        id: keyId,
        data: {
          key_name: keyData.key_name,
          description: keyData.description,
          context: keyData.context,
          image_url: keyData.image_url,
          screenshot_url: keyData.image_url
        }
      }).unwrap();

      refetchKeys();
      toast({
        title: "Success",
        description: "Translation key updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update translation key",
        variant: "destructive"
      });
      throw error;
    }
  };

  const handleDeleteTranslationKey = async () => {
    if (!selectedKey) return;

    try {
      await deleteTranslationKey(selectedKey).unwrap();
      setSelectedKey('');
      refetchKeys();
      toast({
        title: "Success",
        description: "Translation key deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete translation key",
        variant: "destructive"
      });
      throw error;
    }
  };

  const handleSaveTranslation = async () => {
    if (!selectedKey || !selectedLocale || !translationContent.trim()) {
      toast({
        title: "Error",
        description: "Please select a key, locale, and enter translation content",
        variant: "destructive"
      });
      return;
    }

    try {
      if (currentTranslation) {
        await updateTranslation({
          id: currentTranslation.id,
          data: {
            content: translationContent,
            is_reviewed: true
          }
        }).unwrap();

        toast({
          title: "Success",
          description: "Translation updated successfully",
        });
      } else {
        await createTranslation({
          key_id: selectedKey,
          locale_id: selectedLocale,
          content: translationContent
        }).unwrap();

        toast({
          title: "Success",
          description: "Translation created successfully",
        });
      }

      refetchTranslations();
      setIsEditorModalOpen(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save translation",
        variant: "destructive"
      });
    }
  };

  const handleAiTranslate = async () => {
    if (!sourceLocale || !sourceTranslation || !targetLocale) {
      toast({
        title: "Error",
        description: "Missing source translation or target locale",
        variant: "destructive"
      });
      return;
    }

    try {
      const result = await aiTranslate({
        text: sourceTranslation.content,
        source_locale: sourceLocale.code || 'en',
        target_locale: targetLocale.code,
        context: selectedKeyDetails?.context,
        provider: aiProvider,
        model: aiModel
      }).unwrap();

      setTranslationContent(result.data.translated_text);

      toast({
        title: "AI Translation Generated",
        description: `Used ${result.data.credits_used} credits. ${result.data.credits_remaining} credits remaining.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate AI translation",
        variant: "destructive"
      });
    }
  };

  const handleEditKey = (key: any) => {
    setSelectedKey(key.id);
    setEditingKey(key);
    setIsEditKeyDialogOpen(true);
  };

  const handleDeleteKey = (keyId: string) => {
    setSelectedKey(keyId);
    setIsDeleteKeyDialogOpen(true);
  };

  // Loading state
  if (isLoadingLocales) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  // Error state
  if (localesError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Error</CardTitle>
          <CardDescription>Failed to load translation resources</CardDescription>
        </CardHeader>
        <CardContent>
          <p>There was an error loading the translation resources. Please try again.</p>
          <Button onClick={() => window.location.reload()} className="mt-4">Retry</Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Translations</h2>
          <p className="text-muted-foreground">Manage your project translations</p>
        </div>
        <div className="flex space-x-2">
          <AddTranslationKeyDialog
            isOpen={isAddKeyDialogOpen}
            onOpenChange={setIsAddKeyDialogOpen}
            onCreateKey={handleCreateTranslationKey}
            isLoading={isCreatingKey}
          />
        </div>
      </div>

      <Tabs defaultValue="editor" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="editor" className="flex items-center">
            <Languages className="mr-2 h-4 w-4" />
            Languages
          </TabsTrigger>
          <TabsTrigger value="keys" className="flex items-center">
            <FileText className="mr-2 h-4 w-4" />
            Keys
          </TabsTrigger>
        </TabsList>

        {/* Languages Tab */}
        <TabsContent value="editor" className="space-y-6">
          {/* Language Selection */}
          <LanguageSelector
            locales={localesData?.data || []}
            selectedLocale={selectedLocale}
            onLocaleSelect={setSelectedLocale}
          />

          {/* Translation Keys List */}
          <TranslationKeysTable
            keys={keysData?.data || []}
            translations={translationsData?.data}
            locales={localesData?.data || []}
            selectedLocale={selectedLocale}
            selectedKey={selectedKey}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            onKeySelect={handleKeySelect}
            onEditKey={handleEditKey}
            onDeleteKey={handleDeleteKey}
            onPreviousKey={handlePreviousKey}
            onNextKey={handleNextKey}
            currentKeyIndex={currentKeyIndex}
            isLoading={isLoadingKeys}
            error={keysError}
            onRetry={refetchKeys}
          />

          {/* Selected Language Status */}
          {selectedLocale && (
            <SelectedLanguageStatus
              selectedLocale={selectedLocale}
              locales={localesData?.data || []}
            />
          )}
        </TabsContent>

        {/* Keys Management Tab */}
        <TabsContent value="keys" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Keys</CardTitle>
              <CardDescription>Manage all keys in your project</CardDescription>
            </CardHeader>
            <CardContent>
              <TranslationKeysTable
                keys={keysData?.data || []}
                locales={localesData?.data || []}
                searchQuery={searchQuery}
                onSearchChange={setSearchQuery}
                onKeySelect={handleKeySelect}
                onEditKey={handleEditKey}
                onDeleteKey={handleDeleteKey}
                onPreviousKey={handlePreviousKey}
                onNextKey={handleNextKey}
                currentKeyIndex={currentKeyIndex}
                isLoading={isLoadingKeys}
                error={keysError}
                onRetry={refetchKeys}
                showManagementActions={true}
              />
            </CardContent>
            <div className="p-4 border-t">
              <Button onClick={() => setIsAddKeyDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Translation Key
              </Button>
            </div>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Dialogs */}
      <EditTranslationKeyDialog
        isOpen={isEditKeyDialogOpen}
        onOpenChange={setIsEditKeyDialogOpen}
        onUpdateKey={handleUpdateTranslationKey}
        keyData={editingKey}
        isLoading={isUpdatingKey}
      />

      <DeleteTranslationKeyDialog
        isOpen={isDeleteKeyDialogOpen}
        onOpenChange={setIsDeleteKeyDialogOpen}
        onDeleteKey={handleDeleteTranslationKey}
        keyName={selectedKeyDetails?.key_name}
        isLoading={isDeletingKey}
      />

      <TranslationEditorDialog
        isOpen={isEditorModalOpen}
        onOpenChange={setIsEditorModalOpen}
        selectedKey={selectedKeyDetails || null}
        selectedLocale={selectedLocale}
        locales={localesData?.data || []}
        sourceTranslation={sourceTranslation || null}
        currentTranslation={currentTranslation || null}
        translationContent={translationContent}
        onTranslationContentChange={setTranslationContent}
        onSaveTranslation={handleSaveTranslation}
        onAiTranslate={handleAiTranslate}
        isEditing={isEditing}
        isLoading={isLoadingTranslations}
        isSaving={isUpdating || isCreating}
        isTranslating={isTranslating}
        translationsError={translationsError}
        onRetryTranslations={refetchTranslations}
      />

      <AISettingsDialog
        isOpen={isAiSettingsDialogOpen}
        onOpenChange={setIsAiSettingsDialogOpen}
        aiProvider={aiProvider}
        aiModel={aiModel}
        onProviderChange={setAiProvider}
        onModelChange={setAiModel}
      />
    </div>
  );
}